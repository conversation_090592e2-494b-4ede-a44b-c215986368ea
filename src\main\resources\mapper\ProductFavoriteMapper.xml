<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gzsf.javawebspringboot.mapper.ProductFavoriteMapper">

    <!-- 添加收藏 -->
    <insert id="addFavorite" parameterType="cn.gzsf.javawebspringboot.entity.ProductFavorite">
        INSERT INTO product_favorite (user_phone, product_id, created_time, updated_time)
        VALUES (#{userPhone}, #{productId}, #{createdTime}, #{updatedTime})
    </insert>

    <!-- 取消收藏 -->
    <delete id="removeFavorite">
        DELETE FROM product_favorite 
        WHERE user_phone = #{userPhone} AND product_id = #{productId}
    </delete>

    <!-- 检查是否已收藏 -->
    <select id="checkFavorite" resultType="cn.gzsf.javawebspringboot.entity.ProductFavorite">
        SELECT * FROM product_favorite 
        WHERE user_phone = #{userPhone} AND product_id = #{productId}
    </select>

    <!-- 获取用户收藏列表 -->
    <select id="getUserFavorites" resultType="cn.gzsf.javawebspringboot.entity.ProductFavorite">
        SELECT
            pf.*,
            p.name as productName,
            COALESCE(pi.image_url, p.image_url, '/images/default-product.svg') as productImageUrl,
            p.price as productPrice,
            p.description as productDescription
        FROM product_favorite pf
        LEFT JOIN products p ON pf.product_id = p.id
        LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
        WHERE pf.user_phone = #{userPhone}
        ORDER BY pf.created_time DESC
        LIMIT #{offset}, #{size}
    </select>

    <!-- 获取用户收藏总数 -->
    <select id="getUserFavoriteCount" resultType="int">
        SELECT COUNT(*) FROM product_favorite 
        WHERE user_phone = #{userPhone}
    </select>

    <!-- 获取产品收藏总数 -->
    <select id="getProductFavoriteCount" resultType="int">
        SELECT COUNT(*) FROM product_favorite 
        WHERE product_id = #{productId}
    </select>

    <!-- 获取热门收藏产品 -->
    <select id="getPopularFavorites" resultType="cn.gzsf.javawebspringboot.entity.ProductFavorite">
        SELECT
            pf.product_id,
            p.name as productName,
            p.image_url as productImageUrl,
            p.price as productPrice,
            p.description as productDescription,
            COUNT(*) as favoriteCount
        FROM product_favorite pf
        LEFT JOIN products p ON pf.product_id = p.id
        GROUP BY pf.product_id, p.name, p.image_url, p.price, p.description
        ORDER BY favoriteCount DESC
        LIMIT #{limit}
    </select>

    <!-- 获取所有收藏记录（管理员用） -->
    <select id="getAllFavorites" resultType="cn.gzsf.javawebspringboot.entity.ProductFavorite">
        SELECT
            pf.*,
            p.name as productName,
            COALESCE(pi.image_url, p.image_url, '/images/default-product.svg') as productImageUrl,
            p.price as productPrice,
            p.description as productDescription,
            u.username as userName,
            ud.avatar_url as userAvatar,
            u.user_id as userAccount,
            u.phone as userPhone
        FROM product_favorite pf
        LEFT JOIN products p ON pf.product_id = p.id
        LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
        LEFT JOIN user u ON pf.user_phone COLLATE utf8mb4_unicode_ci = u.phone COLLATE utf8mb4_unicode_ci
        LEFT JOIN user_detail ud ON u.phone COLLATE utf8mb4_unicode_ci = ud.phone COLLATE utf8mb4_unicode_ci
        ORDER BY pf.created_time DESC
        LIMIT #{offset}, #{size}
    </select>

    <!-- 获取收藏总数（管理员用） -->
    <select id="getTotalFavoriteCount" resultType="int">
        SELECT COUNT(*) FROM product_favorite
    </select>

    <!-- 获取今日新增收藏数 -->
    <select id="getTodayFavoriteCount" resultType="int">
        SELECT COUNT(*) FROM product_favorite
        WHERE DATE(FROM_UNIXTIME(created_time/1000)) = CURDATE()
    </select>

    <!-- 获取最多收藏的商品收藏数 -->
    <select id="getMaxProductFavoriteCount" resultType="int">
        SELECT COALESCE(MAX(favorite_count), 0) FROM (
            SELECT COUNT(*) as favorite_count
            FROM product_favorite
            GROUP BY product_id
        ) as counts
    </select>

    <!-- 获取活跃收藏用户数 -->
    <select id="getActiveFavoriteUserCount" resultType="int">
        SELECT COUNT(DISTINCT user_phone) FROM product_favorite
    </select>

    <!-- 根据ID删除收藏记录 -->
    <delete id="deleteFavoriteById">
        DELETE FROM product_favorite WHERE id = #{id}
    </delete>

</mapper>
