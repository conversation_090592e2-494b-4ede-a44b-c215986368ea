# Admin后台管理系统后端API设置指南

## 📋 概述

本文档说明如何设置和使用Admin后台管理系统的新增后端API功能，包括日志管理、备份管理、安全中心等模块。

## 🚀 快速开始

### 1. 启动应用

```bash
# 启动SpringBoot应用
mvn spring-boot:run
```

### 2. 自动初始化

应用启动时会自动执行以下操作：
- ✅ 创建新功能所需的数据表
- ✅ 插入默认配置数据
- ✅ 初始化示例数据

### 3. 访问测试页面

- **Admin后台**: http://localhost:8082/admin.html
- **API测试工具**: http://localhost:8082/api-test.html
- **功能总结**: http://localhost:8082/admin-optimization-summary.html

## 📊 新增API接口

### 日志管理API

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/admin/logs/stats` | 获取日志统计信息 |
| GET | `/api/admin/logs` | 获取日志列表（分页） |
| POST | `/api/admin/logs` | 添加日志记录 |
| DELETE | `/api/admin/logs/{id}` | 删除指定日志 |
| POST | `/api/admin/logs/clear` | 清理旧日志 |
| GET | `/api/admin/logs/export` | 导出日志数据 |

### 备份管理API

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/admin/backup/stats` | 获取备份统计信息 |
| GET | `/api/admin/backup/list` | 获取备份列表（分页） |
| POST | `/api/admin/backup/create` | 创建手动备份 |
| POST | `/api/admin/backup/auto-settings` | 更新自动备份设置 |
| GET | `/api/admin/backup/download/{id}` | 下载备份文件 |
| POST | `/api/admin/backup/restore/{id}` | 恢复备份 |
| DELETE | `/api/admin/backup/{id}` | 删除备份 |
| POST | `/api/admin/backup/clean` | 清理旧备份 |

### 安全中心API

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/admin/security/stats` | 获取安全统计信息 |
| GET | `/api/admin/security/logs` | 获取安全事件日志（分页） |
| GET | `/api/admin/security/blacklist` | 获取黑名单IP列表 |
| GET | `/api/admin/security/whitelist` | 获取白名单IP列表 |
| POST | `/api/admin/security/blacklist` | 添加黑名单IP |
| POST | `/api/admin/security/whitelist` | 添加白名单IP |
| DELETE | `/api/admin/security/blacklist/{id}` | 移除黑名单IP |
| DELETE | `/api/admin/security/whitelist/{id}` | 移除白名单IP |
| POST | `/api/admin/security/events/{id}/handle` | 处理安全事件 |
| POST | `/api/admin/security/logs/clear` | 清理安全日志 |
| POST | `/api/admin/security/settings` | 更新安全设置 |

### 系统设置API

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/admin/settings` | 获取系统设置 |
| POST | `/api/admin/settings` | 保存系统设置 |
| POST | `/api/admin/settings/reset` | 重置系统设置 |
| POST | `/api/admin/settings/save` | 保存特定类型设置 |
| GET | `/api/admin/settings/{type}` | 获取特定类型设置 |

## 🗄️ 数据库表结构

### 新增数据表

1. **system_logs** - 系统操作日志表
2. **system_backups** - 系统备份表
3. **security_events** - 安全事件表
4. **ip_management** - IP管理表
5. **auto_backup_config** - 自动备份配置表
6. **security_config** - 安全配置表

### 表结构详情

```sql
-- 系统日志表
CREATE TABLE system_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    level VARCHAR(10) NOT NULL,           -- 日志级别
    action VARCHAR(20) NOT NULL,          -- 操作类型
    user VARCHAR(50) NOT NULL,            -- 操作用户
    description TEXT NOT NULL,            -- 操作描述
    ip VARCHAR(45) NOT NULL,              -- 操作IP
    user_agent TEXT,                      -- 用户代理
    time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    details JSON                          -- 详细信息
);

-- 其他表结构请参考 src/main/resources/sql/admin_features_init.sql
```

## 🔧 配置说明

### 应用配置

在 `application.properties` 中确保以下配置：

```properties
# 数据库配置
spring.datasource.url=*****************************************
spring.datasource.username=your_username
spring.datasource.password=your_password

# JPA配置
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true

# 文件上传配置
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
```

### 安全配置

系统提供以下安全配置选项：

- **密码强度要求**：最小长度、特殊字符要求
- **登录安全**：失败次数限制、账户锁定时间
- **API安全**：请求频率限制、IP白名单
- **防护机制**：SQL注入防护、XSS防护

## 📝 使用示例

### 1. 记录操作日志

```java
@Autowired
private AdminLogService adminLogService;

// 记录用户登录日志
adminLogService.logOperation("INFO", "LOGIN", "admin", "管理员登录系统", "127.0.0.1");

// 记录操作失败日志
adminLogService.logOperation("ERROR", "DELETE", "admin", "删除用户失败：用户不存在", "127.0.0.1");
```

### 2. 创建备份

```java
@Autowired
private AdminBackupService adminBackupService;

// 创建完整备份
SystemBackup backup = adminBackupService.createBackup("手动备份_测试", "full", "admin");
```

### 3. 记录安全事件

```java
@Autowired
private AdminSecurityService adminSecurityService;

// 记录登录失败事件
adminSecurityService.recordSecurityEvent("LOGIN_FAIL", "*************", "连续登录失败5次", "中");
```

## 🧪 测试验证

### 1. 使用API测试工具

访问 http://localhost:8082/api-test.html 进行接口测试

### 2. 手动测试

```bash
# 测试日志统计API
curl -X GET http://localhost:8082/api/admin/logs/stats

# 测试备份统计API
curl -X GET http://localhost:8082/api/admin/backup/stats

# 测试安全统计API
curl -X GET http://localhost:8082/api/admin/security/stats
```

### 3. 前端集成测试

访问 http://localhost:8082/admin.html 测试完整的前后端集成功能

## 🔍 故障排除

### 常见问题

1. **404错误**：确保应用已启动且端口正确
2. **数据库连接失败**：检查数据库配置和连接
3. **表不存在**：确保DatabaseInitService正常执行
4. **权限错误**：检查数据库用户权限

### 日志查看

```bash
# 查看应用启动日志
tail -f logs/spring.log

# 查看数据库初始化日志
grep "数据表初始化" logs/spring.log
```

## 📈 性能优化

### 数据库优化

1. **索引优化**：已为常用查询字段添加索引
2. **分页查询**：使用LIMIT进行分页，避免全表扫描
3. **定期清理**：提供旧数据清理功能

### 缓存策略

```java
// 可以添加Redis缓存来提升性能
@Cacheable("logStats")
public Map<String, Object> getLogStats() {
    // 缓存日志统计数据
}
```

## 🔒 安全建议

1. **API访问控制**：添加认证和授权机制
2. **输入验证**：对所有输入进行严格验证
3. **SQL注入防护**：使用参数化查询
4. **日志脱敏**：敏感信息不记录到日志中

## 📚 扩展开发

### 添加新的日志类型

```java
// 在AdminLogService中添加新的日志记录方法
public void logSecurityEvent(String event, String details) {
    logOperation("WARN", "SECURITY", "system", event, "system");
}
```

### 自定义备份策略

```java
// 实现自定义备份逻辑
public class CustomBackupStrategy implements BackupStrategy {
    @Override
    public void backup(String type, String path) {
        // 自定义备份实现
    }
}
```

## 📞 技术支持

如有问题，请：

1. 查看控制台日志
2. 检查API测试工具的测试结果
3. 参考本文档的故障排除部分
4. 查看源代码注释

---

**版本**: v2.0  
**更新时间**: 2024年12月17日  
**状态**: 已完成 ✅
