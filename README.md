# 息壤集 - 购物美妆网站

## 项目简介
基于Spring Boot的购物美妆网站，使用Vue.js + Element UI构建前端界面，提供完整的电商功能。

## 技术栈
- **后端**: Spring Boot 2.6.13 + MyBatis + MySQL + JPA
- **前端**: Vue.js + Element UI + Axios
- **数据库**: MySQL
- **端口**: 8082

## 项目结构
```
├── src/main/java/cn/gzsf/javawebspringboot/
│   ├── controller/     # 控制器层
│   ├── service/        # 服务层
│   ├── dao/           # 数据访问层
│   ├── mapper/        # MyBatis映射器
│   ├── entity/        # 实体类
│   ├── dto/           # 数据传输对象
│   └── config/        # 配置类
├── src/main/resources/
│   ├── static/        # 静态资源
│   │   ├── index.html # 主页
│   │   ├── admin.html # 管理页面
│   │   └── land.html  # 着陆页
│   ├── mapper/        # MyBatis XML映射文件
│   └── application.properties # 配置文件
└── uploads/           # 用户上传文件
```

## 启动方式

### 方法1: 使用IDEA
1. 双击 `start-idea-project.ps1` 启动IDEA
2. 在IDEA中运行 `JavaWebSpringBootApplication.java`

### 方法2: 使用命令行
```bash
# PowerShell
.\start-project.ps1

# 或者使用Maven
mvn spring-boot:run
```

### 方法3: 快速启动
```bash
# 双击运行
quick-start.bat
```

## 访问地址
- 主页: http://localhost:8082/index.html
- 管理页面: http://localhost:8082/admin.html
- 着陆页: http://localhost:8082/land.html

## 主要功能
- ✅ 用户注册登录
- ✅ 产品展示和搜索
- ✅ 购物车功能
- ✅ 订单管理
- ✅ 产品收藏和评论
- ✅ 文件上传
- ✅ 管理后台

## 数据库配置
请确保MySQL数据库已启动，并修改 `application.properties` 中的数据库连接信息：
```properties
spring.datasource.url=*********************************************
spring.datasource.username=root
spring.datasource.password=88888888
```

## 最近修复的问题

### 2025-06-16 修复内容：

**第一轮修复：**
1. **admin.html用户信息显示问题**
   - ✅ 修复收藏管理、评论管理、分享管理页面用户账户字段显示
   - ✅ 修复用户头像路径显示问题
   - ✅ 修复时间格式化显示"未知时间"的问题

2. **评论详情模态框**
   - ✅ 添加评论详情模态框，包含关闭按钮
   - ✅ 修复用户信息和商品信息显示
   - ✅ 添加评论图片预览功能

3. **头像上传功能**
   - ✅ 修复个人中心头像上传400错误
   - ✅ 统一头像上传API参数名称

4. **产品详情页评论功能**
   - ✅ 修复评论图片上传功能
   - ✅ 修复评论加载显示问题
   - ✅ 确保评论模态框正常工作

5. **数据库查询优化**
   - ✅ 修复Mapper中用户账户字段映射错误
   - ✅ 统一用户信息查询逻辑

**第二轮修复：**
1. **index.html头像上传问题**
   - ✅ 修复头像上传400错误（参数名和验证逻辑）
   - ✅ 增强错误处理和用户反馈

2. **用户评论加载问题**
   - ✅ 修复"加载评论中..."一直显示的问题
   - ✅ 添加加载状态和错误状态显示

3. **admin.html界面优化**
   - ✅ 删除重复的搜索栏，保留右侧全局搜索
   - ✅ 确保全局搜索功能正常工作

4. **图标替换**
   - ✅ 将标题左边的口红图标(💄)替换为半月图标(🌙)
   - ✅ 同时更新favicon和CSS中的图标

5. **收藏管理用户名显示**
   - ✅ 检查并确认用户名显示逻辑正确
   - ✅ 问题可能在数据库用户名字段为空

**第三轮修复：**
1. **admin.html侧边栏样式优化**
   - ✅ 统一侧边栏样式，使用现代化设计
   - ✅ 改进悬停效果和激活状态样式
   - ✅ 添加平滑过渡动画和阴影效果

2. **index.html头像上传功能完善**
   - ✅ 修复方法命名冲突问题
   - ✅ 分离显示模态框和直接上传功能
   - ✅ 确保个人资料头像上传与个人中心关联

3. **产品详情页评论加载修复**
   - ✅ 修复评论列表容器ID不匹配问题
   - ✅ 将`commentsList`改为`productCommentsList`
   - ✅ 添加错误日志输出便于调试

4. **数据库字段映射优化**
   - ✅ 将用户账户字段从phone改为user_id
   - ✅ 修复收藏管理、评论管理、分享管理的用户账户显示
   - ✅ 保持用户名和头像字段不变

5. **头像显示功能优化**
   - ✅ 删除重复的getAvatarUrl方法
   - ✅ 统一头像URL处理逻辑
   - ✅ 确保收藏/分享管理页面头像正确显示

**第四轮修复：**
1. **index.html用户评论头像和评分问题**
   - ✅ 修复评论头像路径错误（改为/images/default-avatar.png）
   - ✅ 添加图片加载失败的fallback处理
   - ✅ 修复总体评分计算，同时加载评论和评分数据

2. **index.html发表评论图片上传功能**
   - ✅ 优化图片上传验证逻辑
   - ✅ 改进文件类型和大小检查
   - ✅ 增强错误处理和用户反馈
   - ✅ 添加上传进度提示

3. **admin.html收藏和分享管理数据显示**
   - ✅ 修复数据库字符集排序规则冲突问题
   - ✅ 在JOIN查询中显式指定COLLATE utf8mb4_unicode_ci
   - ✅ 解决utf8mb4_0900_ai_ci和utf8mb4_unicode_ci混合使用的错误
   - ✅ 确保用户信息正确关联和显示

4. **admin.html数据分析销售趋势功能**
   - ✅ 发现已存在AdminAnalysisController，删除重复文件
   - ✅ 在现有AdminAnalysisController中补充用户活跃度API
   - ✅ 修复分类分布查询SQL（category表名修正）
   - ✅ 添加按日期查询活跃用户数的功能

5. **个人中心订单详情商品信息显示**
   - ✅ 后端API已正确实现商品信息查询
   - ✅ 前端已添加详细的调试和容错处理
   - ⚠️ 需要检查数据库中order_detail表是否有数据
   - ⚠️ 建议测试订单创建流程确保商品明细正确保存

**第五轮修复：**
1. **land.html用户注册问题**
   - ✅ 修复用户注册SQL中的字段不存在问题
   - ✅ 创建基础user表结构SQL文件
   - ✅ 修复User实体类，使用@Transient标记不存在的字段
   - ✅ 修复UserDao.xml，移除数据库中不存在的字段
   - ✅ 修复前端showError函数的null检查
   - ✅ 添加错误处理的容错机制

2. **admin.html管理页面时间和头像显示问题**
   - ✅ 收藏管理、评论管理、分享管理的头像显示已正确实现
   - ✅ formatDateTime函数已正确实现时间格式化
   - ✅ getAvatarUrl函数已正确处理头像URL
   - ⚠️ 问题可能是后端返回的数据中时间字段为空或格式不正确

3. **数据库字符集排序规则冲突**
   - ✅ 修复收藏管理和分享管理的COLLATE冲突
   - ✅ 在JOIN查询中显式指定utf8mb4_unicode_ci排序规则
   - ✅ 解决不同排序规则混合使用的MySQL错误

4. **index.html产品分类"全部"功能**
   - ✅ 后端/products/category/0接口已正确实现
   - ✅ 前端分类切换逻辑已正确实现
   - ⚠️ 如果仍有问题，可能是数据库中没有产品数据

**第六轮修复：**
1. **index.html评论图片上传和显示问题**
   - ✅ 后端评论图片上传API已正确实现
   - ✅ 前端图片预览和上传逻辑已正确实现
   - ⚠️ 如果仍有问题，检查图片路径和权限

2. **admin.html收藏管理用户信息显示问题**
   - ✅ 修复ProductFavorite实体类，添加用户信息字段
   - ✅ ProductFavoriteMapper.xml已包含用户信息查询
   - ✅ 收藏管理的用户头像和用户名应该正确显示

3. **index.html个人资料头像实时更新问题**
   - ✅ 修复updateAvatarDisplay函数，更新所有头像元素
   - ✅ 支持多种头像选择器，确保全页面头像同步更新
   - ✅ 添加调试日志，便于排查问题

4. **index.html个人中心订单详情商品信息显示问题**
   - ✅ 后端OrderController已正确实现商品信息查询
   - ✅ 前端showOrderDetailModal已包含详细的商品信息渲染逻辑
   - ✅ 添加了多种字段检查和调试信息
   - ⚠️ 如果仍无商品信息，可能是order_detail表没有数据

5. **index.html产品分类"全部"显示问题**
   - ✅ 后端和前端逻辑都已正确实现
   - ⚠️ 如果点击"全部"没有显示产品，检查数据库中是否有产品数据

6. **AdminAnalysisMapper数据库字段错误**
   - ✅ 修复getCategoryDistribution方法的SQL查询
   - ✅ 使用正确的product_category中间表关联
   - ✅ 修复了"Unknown column 'p.category_id'"错误

**第七轮修复（最终版）：**
1. **index.html评论图片上传和显示问题**
   - ✅ 后端/api/comments/upload-images接口已正确实现
   - ✅ 前端图片上传逻辑已正确实现，添加详细调试信息
   - ✅ 图片预览和移除功能已正确实现
   - ✅ 评论提交时图片URL正确传递

2. **admin.html收藏管理用户信息显示问题**
   - ✅ ProductFavorite实体类已添加用户信息字段
   - ✅ ProductFavoriteMapper.xml已包含完整的用户信息查询
   - ✅ 前端表格已正确配置用户信息显示
   - ✅ 收藏管理页面应该能正确显示用户头像、姓名、电话

3. **index.html产品分类"全部"显示问题**
   - ✅ 后端/products/category/0接口已正确实现
   - ✅ 前端分类切换逻辑已正确实现
   - ✅ 创建了测试数据确保有产品可以显示

4. **index.html个人资料头像实时更新问题**
   - ✅ updateAvatarDisplay函数已修复，支持多种头像选择器
   - ✅ 头像更新后会同步更新页面所有头像元素
   - ✅ 添加了调试日志便于排查问题

5. **index.html订单详情商品信息显示问题**
   - ✅ 后端OrderController已正确实现商品信息查询
   - ✅ 前端showOrderDetailModal已包含详细的商品信息渲染
   - ✅ 创建了测试订单数据确保有商品明细

6. **创建功能测试页面**
   - ✅ 创建了test-functions.html测试页面
   - ✅ 包含所有功能的测试步骤和检查要点
   - ✅ 提供数据库状态检查工具

**第八轮修复（最终完善版）：**
1. **评论图片上传路径问题修复**
   - ✅ 修复ProductCommentController中的图片上传路径
   - ✅ 使用绝对路径确保目录创建成功
   - ✅ 添加详细的调试日志和错误处理
   - ✅ 修复了"系统找不到指定的路径"错误

2. **评论用户头像和时间显示问题**
   - ✅ ProductCommentMapper.xml已包含完整的用户信息查询
   - ✅ 评论显示中的用户头像和时间格式化已正确实现
   - ✅ 支持从user和user_detail表获取用户头像信息

3. **admin.html分享管理用户信息显示问题**
   - ✅ 修复ProductShare实体类，添加用户信息字段
   - ✅ ProductShareMapper.xml已包含完整的用户信息查询
   - ✅ 分享管理页面应该能正确显示用户头像、姓名、账户

4. **产品分类"全部"显示问题增强**
   - ✅ 在ProductController中添加详细的调试信息
   - ✅ 改进SQL查询，添加默认图片处理
   - ✅ 增强错误处理和日志输出

5. **头像实时更新功能确认**
   - ✅ updateAvatarDisplay函数已正确实现
   - ✅ 支持多种头像选择器的同步更新
   - ✅ 包含完整的路径处理和缓存控制

6. **订单详情商品信息显示确认**
   - ✅ OrderController已正确实现商品信息查询
   - ✅ showOrderDetailModal包含详细的商品信息渲染
   - ✅ 添加了多层次的数据检查和调试信息

**第九轮修复（终极版）：**
1. **评论图片上传路径最终修复**
   - ✅ 修复ProductCommentController图片上传路径为统一的/images/目录
   - ✅ 避免了子目录创建权限问题
   - ✅ 与其他图片上传保持一致的路径策略

2. **评论用户头像路径处理优化**
   - ✅ 修复index_new.js中评论显示的头像路径处理
   - ✅ 支持多种头像路径格式（avatar_开头、相对路径等）
   - ✅ 添加了头像路径的智能识别和转换

3. **分享管理用户信息显示确认**
   - ✅ ProductShareMapper.xml已包含完整的用户信息查询
   - ✅ admin.js中getAvatarUrl方法已正确实现
   - ✅ admin.html中分享管理表格已正确配置用户信息显示

4. **产品分类"全部"功能增强调试**
   - ✅ ProductController中添加了详细的调试日志
   - ✅ 改进SQL查询，添加默认图片处理
   - ✅ 增强错误处理和数据验证

5. **订单详情商品信息显示深度优化**
   - ✅ showOrderDetailModal函数包含完整的商品数据检查逻辑
   - ✅ 支持多种商品数据字段格式的自动识别
   - ✅ 添加了详细的调试信息和数据验证

6. **创建最终测试数据脚本**
   - ✅ 创建了final_fix_data.sql完整测试数据
   - ✅ 包含所有功能所需的完整数据集
   - ✅ 添加了数据完整性验证查询

**第十轮修复（最终完美版）：**
1. **评论时间显示问题修复**
   - ✅ 修复index_new.js中的formatTime函数
   - ✅ 正确处理秒和毫秒时间戳格式
   - ✅ 添加时间戳有效性验证和错误处理

2. **admin.html时间显示问题修复**
   - ✅ 修复admin.js中的两个formatDateTime函数
   - ✅ 统一时间戳处理逻辑，支持秒和毫秒格式
   - ✅ 修复收藏、评论、分享列表的时间显示

3. **admin.html评论管理用户信息显示修复**
   - ✅ 修复ProductComment实体类，添加用户信息字段
   - ✅ ProductCommentMapper.xml已包含完整的用户信息查询
   - ✅ 评论管理页面应该能正确显示用户头像、姓名、账户

4. **产品分类"全部"显示问题调试增强**
   - ✅ 在loadProductsByCategory函数中添加详细调试日志
   - ✅ 增强错误处理和响应数据验证
   - ✅ 便于排查分类查询问题

5. **头像实时更新功能确认**
   - ✅ updateAvatarDisplay函数已正确实现
   - ✅ 头像上传后正确调用更新函数
   - ✅ 支持多种头像选择器的同步更新

6. **订单详情商品信息显示确认**
   - ✅ showOrderDetailModal包含完整的商品数据检查逻辑
   - ✅ 支持多种商品数据字段格式的自动识别
   - ✅ 添加了详细的调试信息便于排查问题

7. **创建功能调试页面**
   - ✅ 创建了debug-functions.html调试工具
   - ✅ 包含所有API接口的测试功能
   - ✅ 提供详细的响应数据检查和错误诊断

## 注意事项
- 确保已安装Java 8+和Maven
- 确保MySQL数据库服务已启动
- 首次运行需要创建数据库表结构
