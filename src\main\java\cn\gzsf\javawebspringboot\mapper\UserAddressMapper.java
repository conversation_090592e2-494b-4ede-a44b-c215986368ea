package cn.gzsf.javawebspringboot.mapper;

import cn.gzsf.javawebspringboot.entity.UserAddress;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 用户地址Mapper接口
 */
@Mapper
public interface UserAddressMapper {

    /**
     * 根据用户手机号获取地址列表
     */
    @Select("SELECT * FROM user_address WHERE user_phone = #{userPhone} ORDER BY is_default DESC, created_time DESC")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "userPhone", column = "user_phone"),
        @Result(property = "receiverName", column = "receiver_name"),
        @Result(property = "receiverPhone", column = "receiver_phone"),
        @Result(property = "province", column = "province"),
        @Result(property = "city", column = "city"),
        @Result(property = "district", column = "district"),
        @Result(property = "detailAddress", column = "detail_address"),
        @Result(property = "isDefault", column = "is_default"),
        @Result(property = "createdTime", column = "created_time"),
        @Result(property = "updatedTime", column = "updated_time")
    })
    List<UserAddress> getAddressByUserPhone(@Param("userPhone") String userPhone);

    /**
     * 插入地址
     */
    @Insert("INSERT INTO user_address (user_phone, receiver_name, receiver_phone, province, city, district, detail_address, is_default, created_time, updated_time) " +
            "VALUES (#{userPhone}, #{receiverName}, #{receiverPhone}, #{province}, #{city}, #{district}, #{detailAddress}, #{isDefault}, #{createdTime}, #{updatedTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertAddress(UserAddress address);

    /**
     * 更新地址
     */
    @Update("UPDATE user_address SET receiver_name = #{receiverName}, receiver_phone = #{receiverPhone}, " +
            "province = #{province}, city = #{city}, district = #{district}, detail_address = #{detailAddress}, " +
            "is_default = #{isDefault}, updated_time = #{updatedTime} WHERE id = #{id}")
    int updateAddress(UserAddress address);

    /**
     * 删除地址
     */
    @Delete("DELETE FROM user_address WHERE id = #{id}")
    int deleteAddress(@Param("id") Integer id);

    /**
     * 清除用户的默认地址
     */
    @Update("UPDATE user_address SET is_default = false WHERE user_phone = #{userPhone}")
    int clearDefaultAddress(@Param("userPhone") String userPhone);

    /**
     * 设置默认地址
     */
    @Update("UPDATE user_address SET is_default = true WHERE id = #{id}")
    int setDefaultAddress(@Param("id") Integer id);
}
