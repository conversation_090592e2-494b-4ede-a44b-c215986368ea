<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gzsf.javawebspringboot.dao.UserDao">

    <resultMap id="BaseResultMap" type="cn.gzsf.javawebspringboot.entity.User">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="register_time" property="registerTime" jdbcType="BIGINT"/>
    </resultMap>



    <!-- 根据用户ID或手机号和密码查询用户 -->
    <select id="findUserByIdOrPhoneAndPassword" resultMap="BaseResultMap">
        SELECT * FROM user
        WHERE (user_id = #{idOrPhone} OR phone = #{idOrPhone}) AND password = #{password}
    </select>

    <!-- 插入新用户 -->
    <insert id="insertUser" parameterType="cn.gzsf.javawebspringboot.entity.User">
        INSERT INTO user (user_id, username, phone, password, register_time)
        VALUES (#{userId}, #{username}, #{phone}, #{password}, #{registerTime})
    </insert>

    <!-- 删除用户 -->
    <delete id="deleteUser" parameterType="String">
        DELETE FROM user WHERE user_id = #{userId}
    </delete>

    <!-- 根据手机号查询用户 -->
    <select id="findUserByPhone" resultMap="BaseResultMap">
        SELECT * FROM user WHERE phone = #{phone}
    </select>

    <!-- 根据用户ID查询用户 -->
    <select id="findUserByUserId" resultMap="BaseResultMap">
        SELECT * FROM user WHERE user_id = #{userId}
    </select>



    <!-- 查询/添加用户列表 -->
    <select id="getAllUsers" resultMap="BaseResultMap">
        SELECT * FROM user ORDER BY register_time DESC
    </select>


    <!--编辑用户信息-->
    <update id="updateUser" parameterType="cn.gzsf.javawebspringboot.entity.User">
        UPDATE user
        <set>
            <if test="username != null">username = #{username},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="password != null">password = #{password},</if>
        </set>
        WHERE user_id = #{userId}
    </update>

    <!-- 分页查询用户 -->
    <select id="findUsersPage" resultMap="BaseResultMap">
        SELECT * FROM user
        ORDER BY register_time DESC
        LIMIT #{offset}, #{size}
    </select>

    <!-- 获取用户总数 -->
    <select id="getTotalUserCount" resultType="int">
        SELECT COUNT(*) FROM user
    </select>

</mapper>