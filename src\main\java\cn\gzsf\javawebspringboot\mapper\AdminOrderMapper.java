package cn.gzsf.javawebspringboot.mapper;

import cn.gzsf.javawebspringboot.entity.UserOrder;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 管理员订单Mapper接口
 */
@Mapper
public interface AdminOrderMapper {

    /**
     * 获取订单列表（分页）
     */
    @Select("<script>" +
            "SELECT * FROM user_order " +
            "<where>" +
            "<if test='status != null'>" +
            "AND status = #{status}" +
            "</if>" +
            "</where>" +
            "ORDER BY created_time DESC " +
            "LIMIT #{offset}, #{size}" +
            "</script>")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "orderNo", column = "order_no"),
        @Result(property = "userPhone", column = "user_phone"),
        @Result(property = "totalAmount", column = "total_amount"),
        @Result(property = "status", column = "status"),
        @Result(property = "receiverName", column = "receiver_name"),
        @Result(property = "receiverPhone", column = "receiver_phone"),
        @Result(property = "receiverAddress", column = "receiver_address"),
        @Result(property = "remark", column = "remark"),
        @Result(property = "createdTime", column = "created_time"),
        @Result(property = "updatedTime", column = "updated_time")
    })
    List<UserOrder> getOrderList(@Param("offset") int offset, 
                                @Param("size") int size, 
                                @Param("status") Integer status);

    /**
     * 获取订单总数
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM user_order " +
            "<where>" +
            "<if test='status != null'>" +
            "AND status = #{status}" +
            "</if>" +
            "</where>" +
            "</script>")
    int getOrderCount(@Param("status") Integer status);

    /**
     * 获取总订单数
     */
    @Select("SELECT COUNT(*) FROM user_order")
    int getTotalOrderCount();

    /**
     * 获取待处理订单数（待付款 + 待发货）
     */
    @Select("SELECT COUNT(*) FROM user_order WHERE status IN (1, 2)")
    int getPendingOrderCount();

    /**
     * 获取已完成订单数
     */
    @Select("SELECT COUNT(*) FROM user_order WHERE status = 4")
    int getCompletedOrderCount();

    /**
     * 获取总销售额（已完成订单）
     */
    @Select("SELECT COALESCE(SUM(total_amount), 0) FROM user_order WHERE status = 4")
    Double getTotalSalesAmount();
}
