package cn.gzsf.javawebspringboot.mapper;

import cn.gzsf.javawebspringboot.entity.ProductShare;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 产品分享Mapper接口
 */
@Mapper
public interface ProductShareMapper {
    
    /**
     * 添加分享记录
     */
    int addShare(ProductShare share);
    
    /**
     * 获取产品分享总数
     */
    int getProductShareCount(@Param("productId") Long productId);
    
    /**
     * 获取用户分享列表
     */
    List<ProductShare> getUserShares(@Param("userPhone") String userPhone, 
                                   @Param("offset") int offset, 
                                   @Param("size") int size);
    
    /**
     * 获取用户分享总数
     */
    int getUserShareCount(@Param("userPhone") String userPhone);
    
    /**
     * 获取热门分享产品
     */
    List<ProductShare> getPopularShares(@Param("limit") int limit);
    
    /**
     * 获取分享统计（按类型）
     */
    List<Map<String, Object>> getShareStatsByType();
    
    /**
     * 获取所有分享记录（管理员用）
     */
    List<ProductShare> getAllShares(@Param("offset") int offset, @Param("size") int size);
    
    /**
     * 获取分享总数（管理员用）
     */
    int getTotalShareCount();

    /**
     * 根据ID删除分享记录
     */
    int deleteShareById(@Param("id") Long id);
}
