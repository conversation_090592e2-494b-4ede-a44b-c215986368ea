# \u8FDE\u63A5\u6570\u636E\u5E93\uFF0C\u6570\u636E\u5E93\u8D26\u53F7\u548C\u5BC6\u7801
spring.datasource.url=******************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=88888888
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# MyBatis\u914D\u7F6E
mybatis.mapper-locations=classpath:mapper/*.xml
mybatis.type-aliases-package=cn.gzsf.javawebspringboot.entity

# \u8BBE\u7F6E\u6587\u4EF6\u4E0A\u4F20\u7684\u6700\u5927\u5927\u5C0F
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# \u7981\u7528spring.jpa.open-in-view
spring.jpa.open-in-view=false


# \u914D\u7F6E\u6587\u4EF6\u4E0A\u4F20\u7684\u7EDD\u5BF9\u8DEF\u5F84
file.upload.dir=src/main/resources/static/images

# \u914D\u7F6E\u9759\u6001\u8D44\u6E90\u6620\u5C04 - 注释掉，使用WebConfig中的配置
# spring.mvc.static-path-pattern=/images/**

# 配置服务器端口
server.port=8082