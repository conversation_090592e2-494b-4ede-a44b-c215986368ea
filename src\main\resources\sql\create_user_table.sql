-- 创建用户基础表
CREATE TABLE IF NOT EXISTS `user` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    `user_id` VARCHAR(50) UNIQUE NOT NULL COMMENT '用户账号',
    `username` VARCHAR(100) NOT NULL COMMENT '用户名',
    `phone` VARCHAR(20) UNIQUE NOT NULL COMMENT '手机号',
    `password` VARCHAR(255) NOT NULL COMMENT '密码',
    `register_time` BIGINT NOT NULL COMMENT '注册时间',
    INDEX `idx_phone` (`phone`),
    INDEX `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户基础信息表';

-- 插入测试用户数据（如果表为空）
INSERT IGNORE INTO `user` (`user_id`, `username`, `phone`, `password`, `register_time`) VALUES 
('admin', '管理员', '15120248009', 'admin123', UNIX_TIMESTAMP() * 1000),
('user001', '小雯', '18823912577', '123456', UNIX_TIMESTAMP() * 1000),
('user002', 'KKKK', '18796247689', '123456', UNIX_TIMESTAMP() * 1000),
('user003', '测试用户1', '13800138001', '123456', UNIX_TIMESTAMP() * 1000),
('user004', '测试用户2', '13800138002', '123456', UNIX_TIMESTAMP() * 1000);
