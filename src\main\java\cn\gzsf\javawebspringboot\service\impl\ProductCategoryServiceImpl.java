package cn.gzsf.javawebspringboot.service.impl;

import cn.gzsf.javawebspringboot.dao.ProductCategoryDao;
import cn.gzsf.javawebspringboot.entity.ProductCategory;
import cn.gzsf.javawebspringboot.service.ProductCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 产品分类关联服务实现类
 */
@Service
public class ProductCategoryServiceImpl implements ProductCategoryService {
    
    @Autowired
    private ProductCategoryDao productCategoryDao;

    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Override
    @Transactional
    public boolean saveProductCategories(Long productId, List<Long> categoryIds) {
        try {
            System.out.println("🔄 开始保存产品分类关联: 产品ID=" + productId + ", 分类IDs=" + categoryIds);

            // 方法1：尝试使用MyBatis DAO
            try {
                // 先删除现有的分类关联
                productCategoryDao.deleteByProductId(productId);

                // 如果有新的分类，则添加
                if (categoryIds != null && !categoryIds.isEmpty()) {
                    List<ProductCategory> productCategories = new ArrayList<>();
                    long currentTime = System.currentTimeMillis();

                    for (Long categoryId : categoryIds) {
                        ProductCategory pc = new ProductCategory();
                        pc.setProductId(productId);
                        pc.setCategoryId(categoryId);
                        pc.setCreatedTime(currentTime);
                        productCategories.add(pc);
                    }

                    int result = productCategoryDao.insertBatch(productCategories);
                    System.out.println("✅ MyBatis方式：为产品 " + productId + " 保存了 " + result + " 个分类关联");
                    return result > 0;
                }

                System.out.println("✅ MyBatis方式：产品 " + productId + " 没有分类，删除操作成功");
                return true;

            } catch (Exception mybatisException) {
                System.err.println("⚠️ MyBatis方式失败，尝试JdbcTemplate方式: " + mybatisException.getMessage());

                // 方法2：回退到JdbcTemplate
                try {
                    // 先删除现有关联
                    String deleteSql = "DELETE FROM product_category WHERE product_id = ?";
                    int deletedRows = jdbcTemplate.update(deleteSql, productId);
                    System.out.println("🗑️ JdbcTemplate方式：删除了 " + deletedRows + " 条现有关联");

                    // 如果有新的分类，则添加
                    if (categoryIds != null && !categoryIds.isEmpty()) {
                        long currentTime = System.currentTimeMillis();
                        int insertedRows = 0;

                        for (Long categoryId : categoryIds) {
                            String insertSql = "INSERT INTO product_category (product_id, category_id, created_time) VALUES (?, ?, ?)";
                            jdbcTemplate.update(insertSql, productId, categoryId, currentTime);
                            insertedRows++;
                        }

                        System.out.println("✅ JdbcTemplate方式：为产品 " + productId + " 保存了 " + insertedRows + " 个分类关联");
                        return insertedRows > 0;
                    }

                    System.out.println("✅ JdbcTemplate方式：产品 " + productId + " 没有分类，删除操作成功");
                    return true;

                } catch (Exception jdbcException) {
                    System.err.println("❌ JdbcTemplate方式也失败: " + jdbcException.getMessage());
                    throw jdbcException;
                }
            }

        } catch (Exception e) {
            System.err.println("❌ 保存产品分类关联完全失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    @Override
    public List<Long> getProductCategoryIds(Long productId) {
        try {
            return productCategoryDao.getCategoryIdsByProductId(productId);
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<String> getProductCategoryNames(Long productId) {
        try {
            return productCategoryDao.getCategoryNamesByProductId(productId);
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<Long> getCategoryProductIds(Long categoryId) {
        try {
            return productCategoryDao.getProductIdsByCategoryId(categoryId);
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }
    
    @Override
    @Transactional
    public boolean deleteProductCategories(Long productId) {
        try {
            int result = productCategoryDao.deleteByProductId(productId);
            return result >= 0; // 即使删除0条记录也算成功
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    @Override
    @Transactional
    public boolean deleteCategoryProducts(Long categoryId) {
        try {
            int result = productCategoryDao.deleteByCategoryId(categoryId);
            return result >= 0; // 即使删除0条记录也算成功
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    @Override
    public int getCategoryProductCount(Long categoryId) {
        try {
            return productCategoryDao.getProductCountByCategoryId(categoryId);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }
}
