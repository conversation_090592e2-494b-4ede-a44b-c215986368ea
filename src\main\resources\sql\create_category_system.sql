-- 创建完整的产品分类系统

-- 1. 确保categories表存在（如果是category表则重命名）
CREATE TABLE IF NOT EXISTS `categories` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `description` text COMMENT '分类描述',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序顺序',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用(0:否, 1:是)',
  `created_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `updated_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品分类表';

-- 2. 创建产品分类关联表
CREATE TABLE IF NOT EXISTS `product_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `product_id` bigint(20) NOT NULL COMMENT '产品ID',
  `category_id` bigint(20) NOT NULL COMMENT '分类ID',
  `created_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_product_category` (`product_id`, `category_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_category_id` (`category_id`),
  CONSTRAINT `fk_pc_product` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_pc_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品分类关联表';

-- 3. 创建轮播图表
CREATE TABLE IF NOT EXISTS `carousel_images` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '轮播图ID',
  `name` varchar(100) NOT NULL COMMENT '轮播图名称',
  `image_url` varchar(500) NOT NULL COMMENT '图片URL',
  `title` varchar(200) DEFAULT NULL COMMENT '轮播图标题',
  `description` text COMMENT '轮播图描述',
  `link_url` varchar(500) DEFAULT NULL COMMENT '点击跳转链接',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序顺序',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用(0:否, 1:是)',
  `category_id` bigint(20) DEFAULT NULL COMMENT '关联分类ID',
  `created_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `updated_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_category_id` (`category_id`),
  CONSTRAINT `fk_carousel_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='轮播图表';

-- 4. 如果存在旧的category表，迁移数据到categories表
INSERT IGNORE INTO `categories` (`id`, `name`, `created_time`, `updated_time`)
SELECT `id`, `name`, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000
FROM `category`
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'category');

-- 5. 插入默认分类数据
INSERT IGNORE INTO `categories` (`name`, `description`, `sort_order`, `created_time`, `updated_time`) VALUES
('护肤品', '各类护肤产品', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('彩妆', '化妆品和彩妆用品', 2, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('香水', '各种香水产品', 3, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('面膜', '面膜护理产品', 4, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('洁面', '洁面清洁产品', 5, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('精华', '精华液产品', 6, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);

-- 6. 插入轮播图数据（使用name='轮播图'）
INSERT IGNORE INTO `carousel_images` (`name`, `image_url`, `title`, `description`, `sort_order`, `category_id`, `created_time`, `updated_time`) VALUES
('轮播图', '/images/074dc74f-ab1e-4241-ad36-40a3dae3234a.jpg', '护肤精品推荐', '优质护肤产品，呵护您的肌肤', 1, 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('轮播图', '/images/496ea50b-83b6-438b-8493-2e49911b089e.jpg', '彩妆新品上市', '时尚彩妆，展现您的美丽', 2, 2, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('轮播图', '/images/acd8ab1a-4e42-41c5-bca6-21d886257b1c.jpg', '香水特惠活动', '迷人香氛，留住美好时光', 3, 3, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);

-- 7. 为现有产品分配默认分类（如果没有分类关联）
INSERT IGNORE INTO `product_category` (`product_id`, `category_id`, `created_time`)
SELECT p.id, 
       CASE 
         WHEN p.id % 6 = 1 THEN 1  -- 护肤品
         WHEN p.id % 6 = 2 THEN 2  -- 彩妆
         WHEN p.id % 6 = 3 THEN 3  -- 香水
         WHEN p.id % 6 = 4 THEN 4  -- 面膜
         WHEN p.id % 6 = 5 THEN 5  -- 洁面
         ELSE 6                    -- 精华
       END as category_id,
       UNIX_TIMESTAMP() * 1000
FROM `products` p
WHERE NOT EXISTS (
    SELECT 1 FROM `product_category` pc WHERE pc.product_id = p.id
);
