package cn.gzsf.javawebspringboot.service;

import cn.gzsf.javawebspringboot.dto.CartItemDTO;
import cn.gzsf.javawebspringboot.entity.ShoppingCart;

import java.util.List;

/**
 * 购物车服务接口
 */
public interface CartService {
    
    /**
     * 添加商品到购物车
     */
    boolean addToCart(ShoppingCart cartItem);
    
    /**
     * 获取用户购物车
     */
    List<CartItemDTO> getCartByUserPhone(String userPhone);
    
    /**
     * 更新购物车商品数量
     */
    boolean updateCartItem(ShoppingCart cartItem);
    
    /**
     * 从购物车删除商品
     */
    boolean removeFromCart(String userPhone, Integer productId);
    
    /**
     * 清空购物车
     */
    boolean clearCart(String userPhone);
    
    /**
     * 获取购物车商品数量
     */
    int getCartItemCount(String userPhone);
}
