package cn.gzsf.javawebspringboot.service.impl;

import cn.gzsf.javawebspringboot.entity.ProductFavorite;
import cn.gzsf.javawebspringboot.mapper.ProductFavoriteMapper;
import cn.gzsf.javawebspringboot.service.ProductFavoriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 产品收藏服务实现类
 */
@Service
public class ProductFavoriteServiceImpl implements ProductFavoriteService {
    
    @Autowired
    private ProductFavoriteMapper favoriteMapper;
    
    @Override
    public boolean addFavorite(String userPhone, Long productId) {
        try {
            // 检查是否已收藏
            ProductFavorite existing = favoriteMapper.checkFavorite(userPhone, productId);
            if (existing != null) {
                return false; // 已收藏
            }
            
            ProductFavorite favorite = new ProductFavorite();
            favorite.setUserPhone(userPhone);
            favorite.setProductId(productId);
            favorite.setCreatedTime(System.currentTimeMillis());
            favorite.setUpdatedTime(System.currentTimeMillis());
            
            return favoriteMapper.addFavorite(favorite) > 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    @Override
    public boolean removeFavorite(String userPhone, Long productId) {
        try {
            return favoriteMapper.removeFavorite(userPhone, productId) > 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    @Override
    public boolean isFavorited(String userPhone, Long productId) {
        try {
            ProductFavorite favorite = favoriteMapper.checkFavorite(userPhone, productId);
            return favorite != null;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    @Override
    public Map<String, Object> getUserFavorites(String userPhone, int page, int size) {
        Map<String, Object> result = new HashMap<>();
        try {
            int offset = (page - 1) * size;
            List<ProductFavorite> favorites = favoriteMapper.getUserFavorites(userPhone, offset, size);
            int total = favoriteMapper.getUserFavoriteCount(userPhone);
            
            result.put("success", true);
            result.put("data", favorites);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取收藏列表失败");
        }
        return result;
    }
    
    @Override
    public int getProductFavoriteCount(Long productId) {
        try {
            return favoriteMapper.getProductFavoriteCount(productId);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }
    
    @Override
    public List<ProductFavorite> getPopularFavorites(int limit) {
        try {
            return favoriteMapper.getPopularFavorites(limit);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    
    @Override
    public Map<String, Object> getAllFavorites(int page, int size) {
        Map<String, Object> result = new HashMap<>();
        try {
            int offset = (page - 1) * size;
            List<ProductFavorite> favorites = favoriteMapper.getAllFavorites(offset, size);
            int total = favoriteMapper.getTotalFavoriteCount();
            
            result.put("success", true);
            result.put("data", favorites);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取收藏列表失败");
        }
        return result;
    }

    @Override
    public Map<String, Object> getFavoriteStats() {
        Map<String, Object> stats = new HashMap<>();
        try {
            // 总收藏数
            int total = favoriteMapper.getTotalFavoriteCount();

            // 今日新增收藏数
            int todayNew = favoriteMapper.getTodayFavoriteCount();

            // 热门商品收藏数（收藏最多的商品）
            int popularProduct = favoriteMapper.getMaxProductFavoriteCount();

            // 活跃用户数（有收藏行为的用户数）
            int activeUsers = favoriteMapper.getActiveFavoriteUserCount();

            stats.put("total", total);
            stats.put("todayNew", todayNew);
            stats.put("popularProduct", popularProduct);
            stats.put("activeUsers", activeUsers);
        } catch (Exception e) {
            e.printStackTrace();
            stats.put("total", 0);
            stats.put("todayNew", 0);
            stats.put("popularProduct", 0);
            stats.put("activeUsers", 0);
        }
        return stats;
    }

    @Override
    public boolean deleteFavoriteById(Long id) {
        try {
            return favoriteMapper.deleteFavoriteById(id) > 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
