-- 最终修复数据脚本
-- 确保所有功能都有完整的测试数据

-- 1. 确保有完整的用户数据（包含头像）
INSERT IGNORE INTO user (phone, password, username, user_id, created_time, updated_time) VALUES
('13220248009', 'password123', '张三', 'user001', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('13220248010', 'password123', '李四', 'user002', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('13220248011', 'password123', '王五', 'user003', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('13220248012', 'password123', '赵六', 'user004', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('13220248013', 'password123', '孙七', 'user005', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);

-- 2. 确保有用户详情数据（包含头像）
INSERT IGNORE INTO user_detail (phone, real_name, avatar_url, gender, birthday, address, created_time, updated_time) VALUES
('13220248009', '张三', 'avatar1.jpg', '男', '1990-01-01', '北京市朝阳区', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('13220248010', '李四', 'avatar2.jpg', '女', '1992-05-15', '上海市浦东新区', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('13220248011', '王五', 'avatar3.jpg', '男', '1988-12-20', '广州市天河区', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('13220248012', '赵六', 'avatar4.jpg', '女', '1995-03-10', '深圳市南山区', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('13220248013', '孙七', 'avatar5.jpg', '男', '1987-11-25', '杭州市西湖区', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);

-- 3. 确保有产品数据
INSERT IGNORE INTO products (id, name, description, price, stock, image_url, is_new) VALUES
(1, '美白精华液', '深层美白，淡化色斑，让肌肤焕发自然光彩', 299.00, 100, '/images/product1.jpg', 1),
(2, '保湿面霜', '24小时长效保湿，滋润不油腻', 199.00, 150, '/images/product2.jpg', 1),
(3, '防晒霜SPF50', '高倍防晒，有效阻挡紫外线伤害', 159.00, 200, '/images/product3.jpg', 0),
(4, '洁面乳', '温和清洁，深层去污不伤肌', 89.00, 300, '/images/product4.jpg', 0),
(5, '眼霜', '紧致眼部肌肤，淡化细纹', 399.00, 80, '/images/product5.jpg', 1),
(6, '面膜套装', '补水美白双重功效，一周见效', 129.00, 120, '/images/product6.jpg', 0),
(7, '精华水', '深层补水，提亮肌肤', 179.00, 90, '/images/product7.jpg', 1),
(8, '卸妆油', '温和卸妆，不伤肌肤', 119.00, 110, '/images/product8.jpg', 0);

-- 4. 确保有分类数据
INSERT IGNORE INTO categories (id, name, description, sort_order, is_active) VALUES
(1, '护肤品', '各类护肤产品', 1, 1),
(2, '彩妆', '化妆品类', 2, 1),
(3, '香水', '各种香水', 3, 1),
(4, '身体护理', '身体护理产品', 4, 1);

-- 5. 确保产品分类关联
INSERT IGNORE INTO product_category (product_id, category_id, created_time) VALUES
(1, 1, UNIX_TIMESTAMP() * 1000),
(2, 1, UNIX_TIMESTAMP() * 1000),
(3, 1, UNIX_TIMESTAMP() * 1000),
(4, 1, UNIX_TIMESTAMP() * 1000),
(5, 1, UNIX_TIMESTAMP() * 1000),
(6, 1, UNIX_TIMESTAMP() * 1000),
(7, 1, UNIX_TIMESTAMP() * 1000),
(8, 1, UNIX_TIMESTAMP() * 1000);

-- 6. 确保有订单数据（修复表名为user_order）
INSERT IGNORE INTO user_order (id, order_no, user_phone, total_amount, status, receiver_name, receiver_phone, receiver_address, created_time, updated_time) VALUES
(1, 'ORD20241201001', '13220248009', 498.00, 2, '张三', '13220248009', '北京市朝阳区某某街道123号', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(2, 'ORD20241201002', '13220248010', 358.00, 3, '李四', '13220248010', '上海市浦东新区某某路456号', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(3, 'ORD20241201003', '13220248011', 288.00, 1, '王五', '13220248011', '广州市天河区某某大道789号', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(4, 'ORD20241201004', '13220248012', 518.00, 4, '赵六', '13220248012', '深圳市南山区某某路101号', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(5, 'ORD20241201005', '13220248013', 428.00, 2, '孙七', '13220248013', '杭州市西湖区某某街202号', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);

-- 7. 确保有订单详情数据（关键：商品信息）
INSERT IGNORE INTO order_detail (order_id, product_id, product_name, product_price, quantity, subtotal) VALUES
(1, 1, '美白精华液', 299.00, 1, 299.00),
(1, 2, '保湿面霜', 199.00, 1, 199.00),
(2, 3, '防晒霜SPF50', 159.00, 1, 159.00),
(2, 2, '保湿面霜', 199.00, 1, 199.00),
(3, 4, '洁面乳', 89.00, 2, 178.00),
(3, 6, '面膜套装', 129.00, 1, 129.00),
(4, 5, '眼霜', 399.00, 1, 399.00),
(4, 7, '精华水', 179.00, 1, 179.00),
(5, 1, '美白精华液', 299.00, 1, 299.00),
(5, 8, '卸妆油', 119.00, 1, 119.00);

-- 8. 确保有评论数据（包含用户头像和时间）
INSERT IGNORE INTO product_comment (product_id, user_phone, user_name, user_avatar, content, rating, images, status, created_time, updated_time) VALUES
(1, '13220248009', '张三', 'avatar1.jpg', '这个美白精华液效果很好，用了一周就看到明显改善！', 5, '', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(2, '13220248010', '李四', 'avatar2.jpg', '保湿效果不错，质地很清爽，不会油腻。', 4, '', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(3, '13220248011', '王五', 'avatar3.jpg', '防晒效果很好，夏天必备！', 5, '', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(1, '13220248012', '赵六', 'avatar4.jpg', '朋友推荐的，确实很好用，会回购！', 5, '', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(4, '13220248013', '孙七', 'avatar5.jpg', '洁面乳很温和，敏感肌也能用', 4, '', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);

-- 9. 确保有收藏数据
INSERT IGNORE INTO product_favorite (user_phone, product_id, created_time) VALUES
('13220248009', 1, UNIX_TIMESTAMP() * 1000),
('13220248009', 2, UNIX_TIMESTAMP() * 1000),
('13220248010', 1, UNIX_TIMESTAMP() * 1000),
('13220248010', 3, UNIX_TIMESTAMP() * 1000),
('13220248011', 2, UNIX_TIMESTAMP() * 1000),
('13220248011', 4, UNIX_TIMESTAMP() * 1000),
('13220248012', 5, UNIX_TIMESTAMP() * 1000),
('13220248013', 6, UNIX_TIMESTAMP() * 1000);

-- 10. 确保有分享数据
INSERT IGNORE INTO product_share (user_phone, product_id, share_type, share_url, created_time) VALUES
('13220248009', 1, 'wechat', 'https://example.com/product/1', UNIX_TIMESTAMP() * 1000),
('13220248009', 2, 'weibo', 'https://example.com/product/2', UNIX_TIMESTAMP() * 1000),
('13220248010', 1, 'qq', 'https://example.com/product/1', UNIX_TIMESTAMP() * 1000),
('13220248010', 3, 'link', 'https://example.com/product/3', UNIX_TIMESTAMP() * 1000),
('13220248011', 2, 'wechat', 'https://example.com/product/2', UNIX_TIMESTAMP() * 1000),
('13220248012', 4, 'weibo', 'https://example.com/product/4', UNIX_TIMESTAMP() * 1000),
('13220248013', 5, 'qq', 'https://example.com/product/5', UNIX_TIMESTAMP() * 1000);

-- 11. 确保有产品图片数据
INSERT IGNORE INTO product_images (product_id, image_url, image_name, is_primary, sort_order, created_time, updated_time) VALUES
(1, '/images/product1.jpg', 'product1.jpg', 1, 0, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(2, '/images/product2.jpg', 'product2.jpg', 1, 0, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(3, '/images/product3.jpg', 'product3.jpg', 1, 0, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(4, '/images/product4.jpg', 'product4.jpg', 1, 0, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(5, '/images/product5.jpg', 'product5.jpg', 1, 0, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(6, '/images/product6.jpg', 'product6.jpg', 1, 0, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(7, '/images/product7.jpg', 'product7.jpg', 1, 0, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(8, '/images/product8.jpg', 'product8.jpg', 1, 0, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);

-- 验证数据完整性
SELECT '=== 最终数据验证结果 ===' as info;
SELECT CONCAT('用户数量: ', COUNT(*)) as users_count FROM user;
SELECT CONCAT('用户详情数量: ', COUNT(*)) as user_details_count FROM user_detail;
SELECT CONCAT('产品数量: ', COUNT(*)) as products_count FROM products;
SELECT CONCAT('分类数量: ', COUNT(*)) as categories_count FROM categories;
SELECT CONCAT('产品分类关联数量: ', COUNT(*)) as product_category_count FROM product_category;
SELECT CONCAT('订单数量: ', COUNT(*)) as orders_count FROM user_order;
SELECT CONCAT('订单详情数量: ', COUNT(*)) as order_details_count FROM order_detail;
SELECT CONCAT('评论数量: ', COUNT(*)) as comments_count FROM product_comment;
SELECT CONCAT('收藏数量: ', COUNT(*)) as favorites_count FROM product_favorite;
SELECT CONCAT('分享数量: ', COUNT(*)) as shares_count FROM product_share;
SELECT CONCAT('产品图片数量: ', COUNT(*)) as product_images_count FROM product_images;

-- 检查关键数据
SELECT '=== 关键数据检查 ===' as info;
SELECT 'order_detail表中的商品信息:' as check_type;
SELECT od.order_id, od.product_name, od.product_price, od.quantity, od.subtotal 
FROM order_detail od 
ORDER BY od.order_id, od.product_id 
LIMIT 10;

SELECT 'product_comment表中的用户头像信息:' as check_type;
SELECT pc.user_name, pc.user_avatar, pc.content, pc.rating 
FROM product_comment pc 
WHERE pc.status = 1 
LIMIT 5;

SELECT 'product_share表中的分享信息:' as check_type;
SELECT ps.user_phone, ps.product_id, ps.share_type, ps.created_time 
FROM product_share ps 
LIMIT 5;
