package cn.gzsf.javawebspringboot.entity;

import lombok.Data;

/**
 * 产品收藏实体类
 */
@Data
public class ProductFavorite {
    private Long id;
    private String userPhone;
    private Long productId;
    private Long createdTime;
    private Long updatedTime;
    
    // 关联查询字段 - 产品信息
    private String productName;
    private String productImageUrl;
    private Double productPrice;
    private String productDescription;

    // 关联查询字段 - 用户信息
    private String userName;
    private String userAvatar;
    private String userAccount;

    // Getter和Setter方法
    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserAvatar() {
        return userAvatar;
    }

    public void setUserAvatar(String userAvatar) {
        this.userAvatar = userAvatar;
    }

    public String getUserAccount() {
        return userAccount;
    }

    public void setUserAccount(String userAccount) {
        this.userAccount = userAccount;
    }
}
