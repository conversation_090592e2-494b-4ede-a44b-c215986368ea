package cn.gzsf.javawebspringboot.service;

import cn.gzsf.javawebspringboot.entity.ProductImage;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 产品图片服务接口
 */
public interface ProductImageService {
    
    /**
     * 根据产品ID获取所有图片
     * @param productId 产品ID
     * @return 图片列表
     */
    List<ProductImage> getImagesByProductId(Long productId);
    
    /**
     * 根据ID获取图片
     * @param id 图片ID
     * @return 图片信息
     */
    ProductImage getImageById(Long id);
    
    /**
     * 添加单张图片
     * @param productImage 图片信息
     * @return 是否成功
     */
    boolean addImage(ProductImage productImage);
    
    /**
     * 批量添加图片
     * @param productImages 图片列表
     * @return 是否成功
     */
    boolean addImages(List<ProductImage> productImages);
    
    /**
     * 上传单张图片文件
     * @param productId 产品ID
     * @param file 图片文件
     * @param isPrimary 是否为主图
     * @return 图片信息
     */
    ProductImage uploadImage(Long productId, MultipartFile file, Boolean isPrimary);
    
    /**
     * 上传多张图片文件
     * @param productId 产品ID
     * @param files 图片文件数组
     * @return 上传结果
     */
    List<ProductImage> uploadImages(Long productId, MultipartFile[] files);
    
    /**
     * 更新图片信息
     * @param productImage 图片信息
     * @return 是否成功
     */
    boolean updateImage(ProductImage productImage);
    
    /**
     * 删除图片
     * @param id 图片ID
     * @return 是否成功
     */
    boolean deleteImage(Long id);
    
    /**
     * 删除产品的所有图片
     * @param productId 产品ID
     * @return 是否成功
     */
    boolean deleteImagesByProductId(Long productId);
    
    /**
     * 设置主图
     * @param id 图片ID
     * @return 是否成功
     */
    boolean setPrimaryImage(Long id);

    /**
     * 清除产品的所有主图标识
     * @param productId 产品ID
     * @return 是否成功
     */
    boolean clearPrimaryImages(Long productId);
    
    /**
     * 获取产品的主图
     * @param productId 产品ID
     * @return 主图信息
     */
    ProductImage getPrimaryImage(Long productId);
    
    /**
     * 获取产品图片数量
     * @param productId 产品ID
     * @return 图片数量
     */
    int getImageCount(Long productId);
}
