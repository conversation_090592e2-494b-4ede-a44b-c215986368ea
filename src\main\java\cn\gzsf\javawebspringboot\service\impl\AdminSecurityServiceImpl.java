package cn.gzsf.javawebspringboot.service.impl;

import cn.gzsf.javawebspringboot.entity.SecurityEvent;
import cn.gzsf.javawebspringboot.entity.IpManagement;
import cn.gzsf.javawebspringboot.service.AdminSecurityService;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 管理员安全服务实现类
 * 注意：这是一个简化的内存实现，生产环境应该使用数据库
 */
@Service
public class AdminSecurityServiceImpl implements AdminSecurityService {
    
    // 使用内存存储，生产环境应该使用数据库
    private final Map<Long, SecurityEvent> securityEventStorage = new ConcurrentHashMap<>();
    private final Map<Long, IpManagement> ipManagementStorage = new ConcurrentHashMap<>();
    private final AtomicLong securityEventIdGenerator = new AtomicLong(1);
    private final AtomicLong ipManagementIdGenerator = new AtomicLong(1);
    
    public AdminSecurityServiceImpl() {
        // 初始化一些示例数据
        initSampleData();
    }
    
    private void initSampleData() {
        // 安全事件示例数据
        SecurityEvent event1 = new SecurityEvent("LOGIN_FAIL", "*************", "连续登录失败5次", "中");
        event1.setId(securityEventIdGenerator.getAndIncrement());
        event1.setTime(LocalDateTime.now().minusHours(2));
        securityEventStorage.put(event1.getId(), event1);
        
        SecurityEvent event2 = new SecurityEvent("SQL_INJECTION", "*********", "检测到SQL注入攻击", "高");
        event2.setId(securityEventIdGenerator.getAndIncrement());
        event2.setTime(LocalDateTime.now().minusMinutes(30));
        securityEventStorage.put(event2.getId(), event2);
        
        SecurityEvent event3 = new SecurityEvent("SUSPICIOUS_ACCESS", "***********", "异常访问模式", "低");
        event3.setId(securityEventIdGenerator.getAndIncrement());
        event3.setTime(LocalDateTime.now().minusMinutes(15));
        securityEventStorage.put(event3.getId(), event3);
        
        // IP管理示例数据
        IpManagement blacklistIP = new IpManagement("*************", "blacklist", "多次恶意登录尝试", "admin");
        blacklistIP.setId(ipManagementIdGenerator.getAndIncrement());
        ipManagementStorage.put(blacklistIP.getId(), blacklistIP);
        
        IpManagement whitelistIP = new IpManagement("127.0.0.1", "whitelist", "本地管理IP", "admin");
        whitelistIP.setId(ipManagementIdGenerator.getAndIncrement());
        ipManagementStorage.put(whitelistIP.getId(), whitelistIP);
    }

    @Override
    public int getTodayAttackCount() {
        LocalDate today = LocalDate.now();
        return (int) securityEventStorage.values().stream()
                .filter(event -> event.getTime().toLocalDate().equals(today))
                .count();
    }

    @Override
    public int getBlacklistIPCount() {
        return (int) ipManagementStorage.values().stream()
                .filter(ip -> "blacklist".equals(ip.getType()) && "active".equals(ip.getStatus()))
                .count();
    }

    @Override
    public int getOnlineUserCount() {
        // 这里应该从会话管理中获取在线用户数
        // 暂时返回模拟数据
        return 5;
    }

    @Override
    public List<SecurityEvent> getSecurityEventsByPage(Map<String, Object> params) {
        int page = (Integer) params.get("page");
        int size = (Integer) params.get("size");
        
        List<SecurityEvent> sortedEvents = securityEventStorage.values().stream()
                .sorted((a, b) -> b.getTime().compareTo(a.getTime())) // 按时间倒序
                .collect(Collectors.toList());
        
        int start = (page - 1) * size;
        int end = Math.min(start + size, sortedEvents.size());
        
        if (start >= sortedEvents.size()) {
            return new ArrayList<>();
        }
        
        return sortedEvents.subList(start, end);
    }

    @Override
    public int getSecurityEventsCount() {
        return securityEventStorage.size();
    }

    @Override
    public List<IpManagement> getBlacklistIPs() {
        return ipManagementStorage.values().stream()
                .filter(ip -> "blacklist".equals(ip.getType()) && "active".equals(ip.getStatus()))
                .sorted((a, b) -> b.getCreateTime().compareTo(a.getCreateTime()))
                .collect(Collectors.toList());
    }

    @Override
    public List<IpManagement> getWhitelistIPs() {
        return ipManagementStorage.values().stream()
                .filter(ip -> "whitelist".equals(ip.getType()) && "active".equals(ip.getStatus()))
                .sorted((a, b) -> b.getCreateTime().compareTo(a.getCreateTime()))
                .collect(Collectors.toList());
    }

    @Override
    public void addIPToList(IpManagement ipManagement) {
        // 检查IP是否已存在
        boolean exists = ipManagementStorage.values().stream()
                .anyMatch(ip -> ip.getIp().equals(ipManagement.getIp()) && 
                               ip.getType().equals(ipManagement.getType()) && 
                               "active".equals(ip.getStatus()));
        
        if (exists) {
            throw new RuntimeException("IP地址已存在于" + 
                ("blacklist".equals(ipManagement.getType()) ? "黑名单" : "白名单") + "中");
        }
        
        ipManagement.setId(ipManagementIdGenerator.getAndIncrement());
        ipManagement.setCreateTime(LocalDateTime.now());
        ipManagement.setStatus("active");
        ipManagementStorage.put(ipManagement.getId(), ipManagement);
    }

    @Override
    public void removeIPFromList(Long id) {
        IpManagement ip = ipManagementStorage.get(id);
        if (ip == null) {
            throw new RuntimeException("IP记录不存在");
        }
        
        ip.setStatus("disabled");
        // 或者直接删除：ipManagementStorage.remove(id);
    }

    @Override
    public void handleSecurityEvent(Long id, String handler) {
        SecurityEvent event = securityEventStorage.get(id);
        if (event == null) {
            throw new RuntimeException("安全事件不存在");
        }
        
        event.setStatus("handled");
        event.setHandler(handler);
        event.setHandleTime(LocalDateTime.now());
    }

    @Override
    public int clearOldSecurityEvents() {
        // 清理30天前的安全事件
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(30);
        
        List<Long> toDelete = securityEventStorage.values().stream()
                .filter(event -> event.getTime().isBefore(cutoffTime))
                .map(SecurityEvent::getId)
                .collect(Collectors.toList());
        
        toDelete.forEach(securityEventStorage::remove);
        return toDelete.size();
    }

    @Override
    public void updateSecuritySettings(Map<String, Object> settings) {
        // 这里应该保存安全设置到配置文件或数据库
        // 暂时只是记录日志
        System.out.println("安全设置已更新: " + settings);
    }

    @Override
    public void recordSecurityEvent(String type, String ip, String description, String severity) {
        SecurityEvent event = new SecurityEvent(type, ip, description, severity);
        event.setId(securityEventIdGenerator.getAndIncrement());
        securityEventStorage.put(event.getId(), event);
    }
}
