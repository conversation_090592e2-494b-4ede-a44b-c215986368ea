package cn.gzsf.javawebspringboot.controller;

import cn.gzsf.javawebspringboot.entity.SystemLog;
import cn.gzsf.javawebspringboot.service.AdminLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 管理员日志管理控制器
 */
@RestController
@RequestMapping("/api/admin/logs")
@CrossOrigin(origins = "*")
public class AdminLogController {

    @Autowired
    private AdminLogService adminLogService;

    /**
     * 获取日志统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getLogStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 今日日志数量
            LocalDate today = LocalDate.now();
            LocalDateTime startOfDay = today.atStartOfDay();
            LocalDateTime endOfDay = today.plusDays(1).atStartOfDay();
            
            stats.put("today", adminLogService.countLogsByTimeRange(startOfDay, endOfDay));
            stats.put("success", adminLogService.countLogsByLevel("INFO"));
            stats.put("warning", adminLogService.countLogsByLevel("WARN"));
            stats.put("error", adminLogService.countLogsByLevel("ERROR"));

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", stats);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取日志统计失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 获取日志列表（分页）
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getLogs(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String level,
            @RequestParam(required = false) String action,
            @RequestParam(required = false) String keyword) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("page", page);
            params.put("size", size);
            params.put("level", level);
            params.put("action", action);
            params.put("keyword", keyword);

            List<SystemLog> logs = adminLogService.getLogsByPage(params);
            int total = adminLogService.countLogs(params);

            Map<String, Object> data = new HashMap<>();
            data.put("list", logs);
            data.put("total", total);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", data);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取日志列表失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 删除日志
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteLog(@PathVariable Long id) {
        try {
            adminLogService.deleteLog(id);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "日志删除成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "删除日志失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 清理旧日志
     */
    @PostMapping("/clear")
    public ResponseEntity<Map<String, Object>> clearOldLogs() {
        try {
            // 清理30天前的日志
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(30);
            int deletedCount = adminLogService.clearLogsBefore(cutoffTime);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "成功清理 " + deletedCount + " 条旧日志");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "清理日志失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 导出日志
     */
    @GetMapping("/export")
    public ResponseEntity<Map<String, Object>> exportLogs(
            @RequestParam(required = false) String level,
            @RequestParam(required = false) String action,
            @RequestParam(required = false) String keyword) {
        try {
            // 这里可以实现CSV或Excel导出逻辑
            // 暂时返回成功响应
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "日志导出功能开发中");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "导出日志失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 添加日志记录
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> addLog(@RequestBody SystemLog log) {
        try {
            adminLogService.addLog(log);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "日志记录成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "记录日志失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }
}
