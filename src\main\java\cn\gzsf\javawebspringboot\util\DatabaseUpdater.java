package cn.gzsf.javawebspringboot.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

/**
 * 数据库更新工具类
 * 用于在应用启动时自动执行数据库结构更新
 */
@Component
public class DatabaseUpdater implements CommandLineRunner {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public void run(String... args) throws Exception {
        try {
            // 检查stock字段是否存在
            String checkColumnSql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS " +
                    "WHERE TABLE_SCHEMA = 'graduation_design' " +
                    "AND TABLE_NAME = 'products' " +
                    "AND COLUMN_NAME = 'stock'";
            
            Integer columnExists = jdbcTemplate.queryForObject(checkColumnSql, Integer.class);
            
            if (columnExists == null || columnExists == 0) {
                System.out.println("正在添加stock字段到products表...");
                
                // 添加stock字段
                String addColumnSql = "ALTER TABLE products ADD COLUMN stock INT NOT NULL DEFAULT 0 COMMENT '库存数量' AFTER price";
                jdbcTemplate.execute(addColumnSql);
                
                // 为现有产品设置默认库存值
                String updateStockSql = "UPDATE products SET stock = 100 WHERE stock = 0";
                int updatedRows = jdbcTemplate.update(updateStockSql);
                
                System.out.println("成功添加stock字段，并为 " + updatedRows + " 个产品设置了默认库存值");
            } else {
                System.out.println("stock字段已存在，跳过数据库更新");
            }
        } catch (Exception e) {
            System.err.println("数据库更新失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
