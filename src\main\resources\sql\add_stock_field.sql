-- 为产品表添加库存字段
ALTER TABLE products ADD COLUMN stock INT NOT NULL DEFAULT 0 COMMENT '库存数量' AFTER price;

-- 为现有产品设置一些默认库存值
UPDATE products SET stock = 100 WHERE id = 1;
UPDATE products SET stock = 50 WHERE id = 2;
UPDATE products SET stock = 75 WHERE id = 3;
UPDATE products SET stock = 200 WHERE id = 4;
UPDATE products SET stock = 30 WHERE id = 5;
UPDATE products SET stock = 150 WHERE id = 6;
UPDATE products SET stock = 80 WHERE id = 7;
UPDATE products SET stock = 120 WHERE id = 8;
UPDATE products SET stock = 60 WHERE id = 9;
UPDATE products SET stock = 90 WHERE id = 10;

-- 为其他可能存在的产品设置默认库存
UPDATE products SET stock = 100 WHERE stock = 0;
