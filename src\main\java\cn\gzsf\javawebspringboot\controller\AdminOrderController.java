package cn.gzsf.javawebspringboot.controller;

import cn.gzsf.javawebspringboot.entity.UserOrder;
import cn.gzsf.javawebspringboot.service.AdminOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 管理员订单管理控制器
 */
@RestController
@RequestMapping("/api/admin/orders")
@CrossOrigin(origins = "*")
public class AdminOrderController {

    @Autowired
    private AdminOrderService adminOrderService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 获取订单列表（分页）
     */
    @GetMapping
    public Map<String, Object> getOrderList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) Integer status) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Object> pageResult = adminOrderService.getOrderList(page, size, status);
            result.put("success", true);
            result.put("data", pageResult.get("data"));
            result.put("total", pageResult.get("total"));
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取订单列表失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 获取订单统计信息
     */
    @GetMapping("/stats")
    public Map<String, Object> getOrderStats() {
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Object> stats = adminOrderService.getOrderStats();
            result.put("success", true);
            result.put("data", stats);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取订单统计失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 发货
     */
    @PostMapping("/{orderNo}/ship")
    public Map<String, Object> shipOrder(@PathVariable String orderNo) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = adminOrderService.shipOrder(orderNo);
            if (success) {
                result.put("success", true);
                result.put("message", "发货成功");
            } else {
                result.put("success", false);
                result.put("message", "发货失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "系统错误：" + e.getMessage());
        }
        return result;
    }

    /**
     * 管理员取消订单
     */
    @PostMapping("/{orderNo}/cancel")
    public Map<String, Object> cancelOrder(@PathVariable String orderNo) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = adminOrderService.cancelOrder(orderNo);
            if (success) {
                result.put("success", true);
                result.put("message", "订单已取消");
            } else {
                result.put("success", false);
                result.put("message", "取消订单失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "系统错误：" + e.getMessage());
        }
        return result;
    }

    /**
     * 获取订单详情
     */
    @GetMapping("/{orderNo}")
    public Map<String, Object> getOrderDetail(@PathVariable String orderNo) {
        Map<String, Object> result = new HashMap<>();
        try {
            UserOrder order = adminOrderService.getOrderDetail(orderNo);
            if (order != null) {
                result.put("success", true);
                result.put("data", order);
            } else {
                result.put("success", false);
                result.put("message", "订单不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "系统错误：" + e.getMessage());
        }
        return result;
    }

    /**
     * 获取订单详情（包含商品信息）
     */
    @GetMapping("/{orderId}/detail")
    public Map<String, Object> getOrderDetailWithItems(@PathVariable Long orderId) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 获取订单基本信息
            String orderSql = "SELECT * FROM user_order WHERE id = ?";
            List<Map<String, Object>> orders = jdbcTemplate.queryForList(orderSql, orderId);

            if (!orders.isEmpty()) {
                Map<String, Object> order = orders.get(0);

                // 获取订单商品明细（包含商品详细信息）
                String itemSql = "SELECT od.*, p.name as product_name, p.description as product_description, " +
                                "COALESCE(pi.image_url, p.image_url, '/images/default-product.svg') as product_image_url, " +
                                "p.price as current_price, p.stock as product_stock " +
                                "FROM order_detail od " +
                                "LEFT JOIN products p ON od.product_id = p.id " +
                                "LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1 " +
                                "WHERE od.order_id = ?";
                List<Map<String, Object>> items = jdbcTemplate.queryForList(itemSql, orderId);
                order.put("items", items);

                result.put("success", true);
                result.put("data", order);
            } else {
                result.put("success", false);
                result.put("message", "订单不存在");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "系统错误：" + e.getMessage());
        }
        return result;
    }
}
