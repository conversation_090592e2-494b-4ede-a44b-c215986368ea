package cn.gzsf.javawebspringboot.service.impl;

import cn.gzsf.javawebspringboot.dao.CategoryDao;
import cn.gzsf.javawebspringboot.entity.Category;
import cn.gzsf.javawebspringboot.service.CategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CategoryServiceImpl implements CategoryService {

    @Autowired
    private CategoryDao categoryDao;

    @Override
    public List<Category> getAllCategories() {
        return categoryDao.findAllCategories();
    }

    @Override
    public Category getCategoryById(Long id) {
        return categoryDao.findCategoryById(id);
    }

    @Override
    public boolean addCategory(Category category) {
        int result = categoryDao.insertCategory(category);
        return result > 0;
    }

    @Override
    public boolean updateCategory(Category category) {
        int result = categoryDao.updateCategory(category);
        return result > 0;
    }

    @Override
    public boolean deleteCategory(Long id) {
        int result = categoryDao.deleteCategory(id);
        return result > 0;
    }

    @Override
    public List<Category> getCategoriesPage(int offset, int size) {
        return categoryDao.findCategoriesPage(offset, size);
    }

    @Override
    public int getTotalCategoryCount() {
        return categoryDao.getTotalCategoryCount();
    }
}