package cn.gzsf.javawebspringboot.dao;

import cn.gzsf.javawebspringboot.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

// MyBatis Mapper接口，用于定义数据库操作方法
@Mapper
public interface UserDao {
    // 根据用户ID或手机号和密码查询用户
    User findUserByIdOrPhoneAndPassword(@Param("idOrPhone") String idOrPhone, @Param("password") String password);
    // 插入新用户
    int insertUser(User user);
    // 根据手机号查询用户
    User findUserByPhone(String phone);

    // 根据用户ID查询用户
    User findUserByUserId(String userId);

    //查询所有用户
    List<User> getAllUsers();


    // 新增删除用户方法
    int deleteUser(@Param("userId") String userId);

    //编辑用户信息
    int updateUser(User user);

    // 分页查询用户
    List<User> findUsersPage(@Param("offset") int offset, @Param("size") int size);

    // 获取用户总数
    int getTotalUserCount();

}