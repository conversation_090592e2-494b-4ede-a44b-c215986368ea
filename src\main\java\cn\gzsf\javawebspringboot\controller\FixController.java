package cn.gzsf.javawebspringboot.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据修复控制器
 */
@RestController
@RequestMapping("/fix")
public class FixController {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 修复产品图片URL - 强制更新为真实图片
     */
    @GetMapping("/product-images")
    public Map<String, Object> fixProductImages() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 检查当前状态
            String checkSql = "SELECT COUNT(*) as total, " +
                    "SUM(CASE WHEN image_url IS NULL OR image_url = '' THEN 1 ELSE 0 END) as without_image " +
                    "FROM products";
            Map<String, Object> stats = jdbcTemplate.queryForMap(checkSql);

            result.put("before", stats);

            // 使用实际存在的图片文件
            String[] imageUrls = {
                "/images/074dc74f-ab1e-4241-ad36-40a3dae3234a.jpg",
                "/images/496ea50b-83b6-438b-8493-2e49911b089e.jpg",
                "/images/acd8ab1a-4e42-41c5-bca6-21d886257b1c.jpg"
            };

            // 强制更新所有产品的图片URL
            String getProductsSql = "SELECT id, name FROM products ORDER BY id";
            List<Map<String, Object>> products = jdbcTemplate.queryForList(getProductsSql);

            int updatedRows = 0;
            for (int i = 0; i < products.size(); i++) {
                Integer productId = (Integer) products.get(i).get("id");
                String productName = (String) products.get(i).get("name");
                String imageUrl = imageUrls[i % imageUrls.length]; // 循环使用图片

                String updateSql = "UPDATE products SET image_url = ? WHERE id = ?";
                jdbcTemplate.update(updateSql, imageUrl, productId);
                updatedRows++;

                System.out.println("更新产品 ID:" + productId + ", 名称:" + productName + ", 图片:" + imageUrl);
            }

            // 验证更新结果
            String verifySql = "SELECT id, name, image_url FROM products ORDER BY id LIMIT 5";
            List<Map<String, Object>> verifyProducts = jdbcTemplate.queryForList(verifySql);
            result.put("verifyData", verifyProducts);

            // 检查修复后状态
            Map<String, Object> afterStats = jdbcTemplate.queryForMap(checkSql);
            result.put("after", afterStats);
            result.put("updatedRows", updatedRows);
            result.put("success", true);
            result.put("message", "强制更新了 " + updatedRows + " 个产品的图片URL为真实封面图片");

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 检查产品数据 - 检查实际的数据库字段
     */
    @GetMapping("/check-products")
    public Map<String, Object> checkProducts() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 首先检查表结构
            String descSql = "DESCRIBE products";
            List<Map<String, Object>> tableStructure = jdbcTemplate.queryForList(descSql);
            result.put("tableStructure", tableStructure);

            // 使用正确的字段名 image_url
            String sql = "SELECT id, name, image_url, stock FROM products LIMIT 5";
            List<Map<String, Object>> products = jdbcTemplate.queryForList(sql);
            result.put("fieldCheck", "Using image_url field (correct)");
            result.put("products", products);

            // 统计图片URL情况
            String statsSql = "SELECT " +
                    "COUNT(*) as total, " +
                    "SUM(CASE WHEN image_url IS NULL OR image_url = '' THEN 1 ELSE 0 END) as without_image, " +
                    "SUM(CASE WHEN image_url IS NOT NULL AND image_url != '' THEN 1 ELSE 0 END) as with_image " +
                    "FROM products";
            Map<String, Object> stats = jdbcTemplate.queryForMap(statsSql);
            result.put("stats", stats);

            result.put("success", true);
            result.put("message", "产品数据检查完成，使用正确的 image_url 字段");

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 测试修复后的字段访问
     */
    @GetMapping("/test-field-fix")
    public Map<String, Object> testFieldFix() {
        Map<String, Object> result = new HashMap<>();

        try {
            System.out.println("🔧 测试修复后的字段访问...");

            // 测试使用正确的字段名
            String sql = "SELECT id, name, image_url, stock FROM products LIMIT 3";
            List<Map<String, Object>> products = jdbcTemplate.queryForList(sql);

            System.out.println("✅ 成功使用 image_url 字段查询产品数据");
            System.out.println("查询到 " + products.size() + " 个产品");

            for (Map<String, Object> product : products) {
                System.out.println("  产品ID: " + product.get("id") +
                                 ", 名称: " + product.get("name") +
                                 ", 图片URL: " + product.get("image_url") +
                                 ", 库存: " + product.get("stock"));
            }

            result.put("success", true);
            result.put("message", "字段修复测试成功");
            result.put("products", products);
            result.put("fieldUsed", "image_url");

        } catch (Exception e) {
            System.err.println("❌ 字段修复测试失败: " + e.getMessage());
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 调试分类产品关联
     */
    @GetMapping("/debug-categories")
    public Map<String, Object> debugCategories() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 先清空现有分类数据
            String deleteCategorySql = "DELETE FROM categories";
            jdbcTemplate.update(deleteCategorySql);

            // 2. 插入新的分类数据（与前端期望的分类名称一致）
            String insertCategorySql = "INSERT INTO categories (id, name, description, sort_order, is_active, created_time, updated_time) VALUES (?, ?, ?, ?, ?, ?, ?)";
            long currentTime = System.currentTimeMillis();

            // 插入前端期望的分类
            jdbcTemplate.update(insertCategorySql, 1, "柔雾集", "柔雾质感美妆产品", 1, true, currentTime, currentTime);
            jdbcTemplate.update(insertCategorySql, 2, "浅草藏", "自然清新护肤系列", 2, true, currentTime, currentTime);
            jdbcTemplate.update(insertCategorySql, 3, "凛风匣", "高端精华护理", 3, true, currentTime, currentTime);
            jdbcTemplate.update(insertCategorySql, 4, "云栖盒", "温和洁面产品", 4, true, currentTime, currentTime);
            jdbcTemplate.update(insertCategorySql, 5, "汲光瓶", "亮肤美白系列", 5, true, currentTime, currentTime);

            // 3. 检查分类表
            String categorySql = "SELECT id, name, description, is_active FROM categories ORDER BY id";
            List<Map<String, Object>> categories = jdbcTemplate.queryForList(categorySql);

            // 4. 检查产品分类关联表
            String relationSql = "SELECT pc.product_id, pc.category_id, p.name as product_name, c.name as category_name " +
                                "FROM product_category pc " +
                                "LEFT JOIN products p ON pc.product_id = p.id " +
                                "LEFT JOIN categories c ON pc.category_id = c.id " +
                                "ORDER BY pc.category_id, pc.product_id";
            List<Map<String, Object>> relations = jdbcTemplate.queryForList(relationSql);

            // 5. 重新分配产品到新分类
            // 先清空现有的产品分类关联
            String deleteRelationsSql = "DELETE FROM product_category";
            jdbcTemplate.update(deleteRelationsSql);

            // 获取所有产品
            String productsSql = "SELECT id FROM products ORDER BY id";
            List<Map<String, Object>> products = jdbcTemplate.queryForList(productsSql);

            // 为每个产品循环分配分类（确保每个分类都有产品）
            String insertRelationSql = "INSERT INTO product_category (product_id, category_id, created_time) VALUES (?, ?, ?)";
            int productIndex = 0;
            int insertedRelations = 0;

            for (Map<String, Object> product : products) {
                Long productId = ((Number) product.get("id")).longValue();
                // 循环分配分类ID 1-5
                Long categoryId = (long) ((productIndex % 5) + 1);

                try {
                    jdbcTemplate.update(insertRelationSql, productId, categoryId, currentTime);
                    insertedRelations++;
                } catch (Exception e) {
                    System.err.println("❌ 插入产品分类关联失败: 产品ID=" + productId + ", 分类ID=" + categoryId + ", 错误=" + e.getMessage());
                }
                productIndex++;
            }

            // 6. 统计每个分类的产品数量
            Map<String, Object> categoryStats = new HashMap<>();
            for (Map<String, Object> category : categories) {
                Long categoryId = ((Number) category.get("id")).longValue();
                String countSql = "SELECT COUNT(*) FROM product_category WHERE category_id = ?";
                int productCount = jdbcTemplate.queryForObject(countSql, Integer.class, categoryId);
                categoryStats.put(category.get("name").toString(), productCount);
            }

            result.put("success", true);
            result.put("message", "分类系统修复完成");
            result.put("categories", categories);
            result.put("categoryStats", categoryStats);
            result.put("insertedRelations", insertedRelations);

            System.out.println("✅ 分类系统修复完成");
            System.out.println("📂 新分类数量: " + categories.size());
            System.out.println("🔗 新增分类关联: " + insertedRelations);
            for (Map.Entry<String, Object> entry : categoryStats.entrySet()) {
                System.out.println("  " + entry.getKey() + ": " + entry.getValue() + " 个产品");
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 快速修复产品分类关联
     */
    @PostMapping("/fix-product-categories")
    public Map<String, Object> fixProductCategories() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 确保基础分类存在
            String[] categoryNames = {"护肤品", "彩妆", "香水", "工具"};
            for (int i = 0; i < categoryNames.length; i++) {
                String checkSql = "SELECT COUNT(*) FROM categories WHERE name = ?";
                int count = jdbcTemplate.queryForObject(checkSql, Integer.class, categoryNames[i]);

                if (count == 0) {
                    String insertSql = "INSERT INTO categories (name, description, is_active, sort_order) VALUES (?, ?, 1, ?)";
                    jdbcTemplate.update(insertSql, categoryNames[i], categoryNames[i] + "类产品", i + 1);
                    System.out.println("✅ 创建分类: " + categoryNames[i]);
                }
            }

            // 2. 为没有分类的产品随机分配分类
            String orphanSql = "SELECT p.id, p.name FROM products p " +
                              "LEFT JOIN product_category pc ON p.id = pc.product_id " +
                              "WHERE pc.product_id IS NULL";
            List<Map<String, Object>> orphanProducts = jdbcTemplate.queryForList(orphanSql);

            // 获取所有分类ID
            String categoriesSql = "SELECT id FROM categories WHERE is_active = 1 ORDER BY id";
            List<Map<String, Object>> categories = jdbcTemplate.queryForList(categoriesSql);

            int assignedCount = 0;
            for (Map<String, Object> product : orphanProducts) {
                Long productId = ((Number) product.get("id")).longValue();

                // 随机分配一个分类
                int randomIndex = (int) (Math.random() * categories.size());
                Long categoryId = ((Number) categories.get(randomIndex).get("id")).longValue();

                String insertRelationSql = "INSERT INTO product_category (product_id, category_id, created_time) VALUES (?, ?, ?)";
                jdbcTemplate.update(insertRelationSql, productId, categoryId, System.currentTimeMillis());

                assignedCount++;
                System.out.println("✅ 产品 " + product.get("name") + " 分配到分类 " + categoryId);
            }

            result.put("success", true);
            result.put("assignedCount", assignedCount);
            result.put("message", "成功为 " + assignedCount + " 个产品分配了分类");

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 获取实际分类数据和产品关联情况
     */
    @GetMapping("/actual-categories")
    public Map<String, Object> getActualCategories() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 获取所有分类
            String categorySql = "SELECT id, name, description, is_active FROM categories ORDER BY id";
            List<Map<String, Object>> categories = jdbcTemplate.queryForList(categorySql);

            // 2. 为每个分类统计产品数量
            for (Map<String, Object> category : categories) {
                Long categoryId = ((Number) category.get("id")).longValue();
                String countSql = "SELECT COUNT(*) FROM product_category WHERE category_id = ?";
                int productCount = jdbcTemplate.queryForObject(countSql, Integer.class, categoryId);
                category.put("productCount", productCount);

                // 获取该分类的前3个产品作为示例
                String sampleSql = "SELECT p.id, p.name FROM products p " +
                                  "JOIN product_category pc ON p.id = pc.product_id " +
                                  "WHERE pc.category_id = ? LIMIT 3";
                List<Map<String, Object>> sampleProducts = jdbcTemplate.queryForList(sampleSql, categoryId);
                category.put("sampleProducts", sampleProducts);
            }

            // 3. 检查没有分类的产品
            String orphanSql = "SELECT COUNT(*) FROM products p " +
                              "LEFT JOIN product_category pc ON p.id = pc.product_id " +
                              "WHERE pc.product_id IS NULL";
            int orphanCount = jdbcTemplate.queryForObject(orphanSql, Integer.class);

            result.put("success", true);
            result.put("categories", categories);
            result.put("orphanProductCount", orphanCount);
            result.put("message", "获取实际分类数据成功");

            System.out.println("📊 实际分类数据:");
            for (Map<String, Object> cat : categories) {
                System.out.println("  分类: " + cat.get("name") + " (ID: " + cat.get("id") + ") - 产品数: " + cat.get("productCount"));
            }
            System.out.println("  孤儿产品数: " + orphanCount);

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 添加stock字段（如果不存在）
     */
    @GetMapping("/add-stock-field")
    public Map<String, Object> addStockField() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查stock字段是否存在
            String checkColumnSql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS " +
                    "WHERE TABLE_SCHEMA = 'graduation_design' " +
                    "AND TABLE_NAME = 'products' " +
                    "AND COLUMN_NAME = 'stock'";
            
            Integer columnExists = jdbcTemplate.queryForObject(checkColumnSql, Integer.class);
            
            if (columnExists == null || columnExists == 0) {
                // 添加stock字段
                String addColumnSql = "ALTER TABLE products ADD COLUMN stock INT NOT NULL DEFAULT 0 COMMENT '库存数量' AFTER price";
                jdbcTemplate.execute(addColumnSql);
                
                // 为现有产品设置默认库存值
                String updateStockSql = "UPDATE products SET stock = 100 WHERE stock = 0";
                int updatedRows = jdbcTemplate.update(updateStockSql);
                
                result.put("success", true);
                result.put("message", "成功添加stock字段，并为 " + updatedRows + " 个产品设置了默认库存值");
                result.put("updatedRows", updatedRows);
            } else {
                result.put("success", true);
                result.put("message", "stock字段已存在，无需添加");
                result.put("updatedRows", 0);
            }
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }
        
        return result;
    }

    /**
     * 修复image_url字段 - 针对数据库实际字段名
     */
    @GetMapping("/fix-image-url")
    public Map<String, Object> fixImageUrl() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 使用实际存在的图片文件
            String[] imageUrls = {
                "/images/074dc74f-ab1e-4241-ad36-40a3dae3234a.jpg",
                "/images/496ea50b-83b6-438b-8493-2e49911b089e.jpg",
                "/images/acd8ab1a-4e42-41c5-bca6-21d886257b1c.jpg"
            };

            // 获取所有产品
            String getProductsSql = "SELECT id, name FROM products ORDER BY id";
            List<Map<String, Object>> products = jdbcTemplate.queryForList(getProductsSql);

            int updatedRows = 0;

            // 尝试更新image_url字段
            for (int i = 0; i < products.size(); i++) {
                Integer productId = (Integer) products.get(i).get("id");
                String productName = (String) products.get(i).get("name");
                String imageUrl = imageUrls[i % imageUrls.length];

                try {
                    // 首先尝试image_url字段
                    String updateSql = "UPDATE products SET image_url = ? WHERE id = ?";
                    jdbcTemplate.update(updateSql, imageUrl, productId);
                    updatedRows++;
                    System.out.println("更新产品 ID:" + productId + ", 名称:" + productName + ", 图片:" + imageUrl + " (使用image_url字段)");
                } catch (Exception e1) {
                    try {
                        // 如果失败，尝试imageUrl字段
                        String updateSql2 = "UPDATE products SET imageUrl = ? WHERE id = ?";
                        jdbcTemplate.update(updateSql2, imageUrl, productId);
                        updatedRows++;
                        System.out.println("更新产品 ID:" + productId + ", 名称:" + productName + ", 图片:" + imageUrl + " (使用imageUrl字段)");
                    } catch (Exception e2) {
                        System.err.println("无法更新产品 ID:" + productId + ", 错误: " + e2.getMessage());
                    }
                }
            }

            // 验证更新结果
            try {
                String verifySql = "SELECT id, name, image_url FROM products ORDER BY id LIMIT 5";
                List<Map<String, Object>> verifyProducts = jdbcTemplate.queryForList(verifySql);
                result.put("verifyData", verifyProducts);
                result.put("fieldUsed", "image_url");
            } catch (Exception e) {
                try {
                    String verifySql2 = "SELECT id, name, imageUrl FROM products ORDER BY id LIMIT 5";
                    List<Map<String, Object>> verifyProducts = jdbcTemplate.queryForList(verifySql2);
                    result.put("verifyData", verifyProducts);
                    result.put("fieldUsed", "imageUrl");
                } catch (Exception e2) {
                    result.put("verifyError", "无法验证更新结果: " + e2.getMessage());
                }
            }

            result.put("updatedRows", updatedRows);
            result.put("success", true);
            result.put("message", "成功更新了 " + updatedRows + " 个产品的图片URL");

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 测试图片文件是否存在
     */
    @GetMapping("/test-images")
    public Map<String, Object> testImages() {
        Map<String, Object> result = new HashMap<>();

        try {
            String[] imageUrls = {
                "/images/074dc74f-ab1e-4241-ad36-40a3dae3234a.jpg",
                "/images/496ea50b-83b6-438b-8493-2e49911b089e.jpg",
                "/images/acd8ab1a-4e42-41c5-bca6-21d886257b1c.jpg"
            };

            // 获取上传目录路径
            String uploadDir = "D:/贵州师范学院/java核心编程/CoDe/Java-web-SpringBoot/java-web-SpringBoot/src/main/resources/static/images";

            List<Map<String, Object>> fileStatus = new ArrayList<>();

            for (String imageUrl : imageUrls) {
                Map<String, Object> status = new HashMap<>();
                String fileName = imageUrl.substring(imageUrl.lastIndexOf("/") + 1);
                String fullPath = uploadDir + "/" + fileName;

                java.io.File file = new java.io.File(fullPath);
                status.put("fileName", fileName);
                status.put("fullPath", fullPath);
                status.put("exists", file.exists());
                status.put("canRead", file.canRead());
                status.put("size", file.exists() ? file.length() : 0);

                fileStatus.add(status);
            }

            result.put("uploadDir", uploadDir);
            result.put("fileStatus", fileStatus);
            result.put("success", true);

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 调试产品数据 - 检查实际返回的产品数据
     */
    @GetMapping("/debug-products")
    public Map<String, Object> debugProducts() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 直接查询数据库
            String sql = "SELECT id, name, image_url, stock FROM products LIMIT 3";
            List<Map<String, Object>> dbProducts = jdbcTemplate.queryForList(sql);
            result.put("dbProducts", dbProducts);

            // 通过ProductService查询
            String serviceUrl = "http://localhost:8082/products/page?page=1&size=3";
            result.put("serviceUrl", serviceUrl);
            result.put("note", "请访问上述URL查看ProductService返回的数据");

            result.put("success", true);

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 检查products表和product_images表的数据一致性
     */
    @GetMapping("/check-image-consistency")
    public Map<String, Object> checkImageConsistency() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 检查products表的image_url
            String productsSql = "SELECT id, name, image_url FROM products ORDER BY id LIMIT 5";
            List<Map<String, Object>> productsData = jdbcTemplate.queryForList(productsSql);
            result.put("productsTable", productsData);

            // 检查product_images表的数据
            String imagesSql = "SELECT product_id, image_url, is_primary FROM product_images ORDER BY product_id, id LIMIT 10";
            List<Map<String, Object>> imagesData = jdbcTemplate.queryForList(imagesSql);
            result.put("productImagesTable", imagesData);

            // 分析数据不一致的情况
            List<Map<String, Object>> inconsistencies = new ArrayList<>();

            for (Map<String, Object> product : productsData) {
                Integer productId = (Integer) product.get("id");
                String productImageUrl = (String) product.get("image_url");

                // 查找该产品在product_images表中的主图片
                String primaryImageSql = "SELECT image_url FROM product_images WHERE product_id = ? AND is_primary = 1 LIMIT 1";
                try {
                    Map<String, Object> primaryImage = jdbcTemplate.queryForMap(primaryImageSql, productId);
                    String primaryImageUrl = (String) primaryImage.get("image_url");

                    if (!java.util.Objects.equals(productImageUrl, primaryImageUrl)) {
                        Map<String, Object> inconsistency = new HashMap<>();
                        inconsistency.put("productId", productId);
                        inconsistency.put("productName", product.get("name"));
                        inconsistency.put("productsTableImageUrl", productImageUrl);
                        inconsistency.put("productImagesTableImageUrl", primaryImageUrl);
                        inconsistency.put("issue", "图片URL不一致");
                        inconsistencies.add(inconsistency);
                    }
                } catch (Exception e) {
                    // 如果没有找到主图片
                    Map<String, Object> inconsistency = new HashMap<>();
                    inconsistency.put("productId", productId);
                    inconsistency.put("productName", product.get("name"));
                    inconsistency.put("productsTableImageUrl", productImageUrl);
                    inconsistency.put("productImagesTableImageUrl", null);
                    inconsistency.put("issue", "product_images表中没有主图片");
                    inconsistencies.add(inconsistency);
                }
            }

            result.put("inconsistencies", inconsistencies);
            result.put("inconsistencyCount", inconsistencies.size());
            result.put("success", true);

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 同步products表和product_images表的图片URL
     */
    @GetMapping("/sync-image-urls")
    public Map<String, Object> syncImageUrls() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 使用实际存在的图片文件
            String[] availableImages = {
                "/images/074dc74f-ab1e-4241-ad36-40a3dae3234a.jpg",
                "/images/496ea50b-83b6-438b-8493-2e49911b089e.jpg",
                "/images/acd8ab1a-4e42-41c5-bca6-21d886257b1c.jpg"
            };

            // 获取所有产品
            String getProductsSql = "SELECT id, name FROM products ORDER BY id";
            List<Map<String, Object>> products = jdbcTemplate.queryForList(getProductsSql);

            int updatedProducts = 0;
            int updatedImages = 0;

            for (int i = 0; i < products.size(); i++) {
                Integer productId = (Integer) products.get(i).get("id");
                String productName = (String) products.get(i).get("name");
                String imageUrl = availableImages[i % availableImages.length];

                // 1. 更新products表的image_url
                String updateProductSql = "UPDATE products SET image_url = ? WHERE id = ?";
                jdbcTemplate.update(updateProductSql, imageUrl, productId);
                updatedProducts++;

                // 2. 删除该产品在product_images表中的旧记录
                String deleteImagesSql = "DELETE FROM product_images WHERE product_id = ?";
                jdbcTemplate.update(deleteImagesSql, productId);

                // 3. 在product_images表中插入新的主图片记录
                String insertImageSql = "INSERT INTO product_images (product_id, image_url, is_primary) VALUES (?, ?, 1)";
                jdbcTemplate.update(insertImageSql, productId, imageUrl);
                updatedImages++;

                System.out.println("同步产品 ID:" + productId + ", 名称:" + productName + ", 图片:" + imageUrl);
            }

            // 验证同步结果
            String verifySql = "SELECT p.id, p.name, p.image_url as products_image_url, " +
                             "pi.image_url as product_images_image_url, pi.is_primary " +
                             "FROM products p " +
                             "LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1 " +
                             "ORDER BY p.id LIMIT 5";
            List<Map<String, Object>> verifyData = jdbcTemplate.queryForList(verifySql);

            result.put("updatedProducts", updatedProducts);
            result.put("updatedImages", updatedImages);
            result.put("verifyData", verifyData);
            result.put("success", true);
            result.put("message", "成功同步了 " + updatedProducts + " 个产品的图片URL");

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 强化产品封面图片同步 - 确保每个产品都有封面图片显示
     */
    @GetMapping("/force-sync-cover-images")
    public Map<String, Object> forceSyncCoverImages() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 使用实际存在的图片文件
            String[] availableImages = {
                "/images/074dc74f-ab1e-4241-ad36-40a3dae3234a.jpg",
                "/images/496ea50b-83b6-438b-8493-2e49911b089e.jpg",
                "/images/acd8ab1a-4e42-41c5-bca6-21d886257b1c.jpg"
            };

            // 获取所有产品
            String getProductsSql = "SELECT id, name FROM products ORDER BY id";
            List<Map<String, Object>> products = jdbcTemplate.queryForList(getProductsSql);

            int updatedProducts = 0;
            List<Map<String, Object>> updateLog = new ArrayList<>();

            for (int i = 0; i < products.size(); i++) {
                Integer productId = (Integer) products.get(i).get("id");
                String productName = (String) products.get(i).get("name");
                String imageUrl = availableImages[i % availableImages.length];

                // 1. 强制更新products表的image_url
                String updateProductSql = "UPDATE products SET image_url = ? WHERE id = ?";
                jdbcTemplate.update(updateProductSql, imageUrl, productId);

                // 2. 删除该产品在product_images表中的所有记录
                String deleteImagesSql = "DELETE FROM product_images WHERE product_id = ?";
                jdbcTemplate.update(deleteImagesSql, productId);

                // 3. 在product_images表中插入新的主图片记录
                String insertImageSql = "INSERT INTO product_images (product_id, image_url, is_primary) VALUES (?, ?, 1)";
                jdbcTemplate.update(insertImageSql, productId, imageUrl);

                updatedProducts++;

                Map<String, Object> logEntry = new HashMap<>();
                logEntry.put("productId", productId);
                logEntry.put("productName", productName);
                logEntry.put("imageUrl", imageUrl);
                logEntry.put("status", "成功");
                updateLog.add(logEntry);

                System.out.println("强制同步产品 ID:" + productId + ", 名称:" + productName + ", 图片:" + imageUrl);
            }

            // 验证同步结果 - 检查products表
            String verifyProductsSql = "SELECT id, name, image_url FROM products ORDER BY id LIMIT 5";
            List<Map<String, Object>> verifyProducts = jdbcTemplate.queryForList(verifyProductsSql);

            // 验证同步结果 - 检查product_images表
            String verifyImagesSql = "SELECT product_id, image_url, is_primary FROM product_images WHERE is_primary = 1 ORDER BY product_id LIMIT 5";
            List<Map<String, Object>> verifyImages = jdbcTemplate.queryForList(verifyImagesSql);

            result.put("updatedProducts", updatedProducts);
            result.put("updateLog", updateLog);
            result.put("verifyProducts", verifyProducts);
            result.put("verifyImages", verifyImages);
            result.put("success", true);
            result.put("message", "强制同步了 " + updatedProducts + " 个产品的封面图片，确保数据一致性");

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 强制刷新产品数据 - 绕过缓存直接从数据库获取
     */
    @GetMapping("/refresh-product-data")
    public Map<String, Object> refreshProductData() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 直接从数据库查询最新的产品数据
            String sql = "SELECT id, name, description, price, stock, image_url, is_new FROM products ORDER BY id";
            List<Map<String, Object>> products = jdbcTemplate.queryForList(sql);

            result.put("products", products);
            result.put("count", products.size());
            result.put("success", true);
            result.put("message", "成功获取 " + products.size() + " 个产品的最新数据");

            // 检查图片URL是否正确
            int hasImageCount = 0;
            for (Map<String, Object> product : products) {
                String imageUrl = (String) product.get("image_url");
                if (imageUrl != null && !imageUrl.isEmpty()) {
                    hasImageCount++;
                }
            }

            result.put("hasImageCount", hasImageCount);
            result.put("noImageCount", products.size() - hasImageCount);

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 立即更新所有产品的图片URL - 使用不同的图片
     */
    @GetMapping("/immediate-update-images")
    public Map<String, Object> immediateUpdateImages() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 使用更多不同的图片
            String[] imageUrls = {
                "/images/074dc74f-ab1e-4241-ad36-40a3dae3234a.jpg",
                "/images/496ea50b-83b6-438b-8493-2e49911b089e.jpg",
                "/images/acd8ab1a-4e42-41c5-bca6-21d886257b1c.jpg"
            };

            // 获取所有产品ID
            String getIdsSql = "SELECT id FROM products ORDER BY id";
            List<Map<String, Object>> productIds = jdbcTemplate.queryForList(getIdsSql);

            int updated = 0;
            List<Map<String, Object>> updateDetails = new ArrayList<>();

            for (int i = 0; i < productIds.size(); i++) {
                Integer productId = (Integer) productIds.get(i).get("id");
                String imageUrl = imageUrls[i % imageUrls.length];

                // 立即更新
                String updateSql = "UPDATE products SET image_url = ? WHERE id = ?";
                int rows = jdbcTemplate.update(updateSql, imageUrl, productId);

                if (rows > 0) {
                    updated++;
                    Map<String, Object> detail = new HashMap<>();
                    detail.put("productId", productId);
                    detail.put("imageUrl", imageUrl);
                    detail.put("updated", true);
                    updateDetails.add(detail);

                    System.out.println("立即更新产品 " + productId + " 的图片为: " + imageUrl);
                }
            }

            // 立即验证更新结果
            String verifySql = "SELECT id, name, image_url FROM products ORDER BY id LIMIT 10";
            List<Map<String, Object>> verifyData = jdbcTemplate.queryForList(verifySql);

            result.put("updated", updated);
            result.put("updateDetails", updateDetails);
            result.put("verifyData", verifyData);
            result.put("success", true);
            result.put("message", "立即更新了 " + updated + " 个产品的图片URL");

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 修复产品封面同步问题 - 从product_images表同步主图到products表
     */
    @GetMapping("/fix-cover-sync")
    public Map<String, Object> fixCoverSync() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取所有有主图的产品图片
            String getPrimaryImagesSql = "SELECT product_id, image_url FROM product_images WHERE is_primary = 1 ORDER BY product_id";
            List<Map<String, Object>> primaryImages = jdbcTemplate.queryForList(getPrimaryImagesSql);

            int syncedCount = 0;
            List<Map<String, Object>> syncDetails = new ArrayList<>();

            for (Map<String, Object> imageRow : primaryImages) {
                Long productId = ((Number) imageRow.get("product_id")).longValue();
                String imageUrl = (String) imageRow.get("image_url");

                // 更新products表的image_url字段
                String updateProductSql = "UPDATE products SET image_url = ? WHERE id = ?";
                int rows = jdbcTemplate.update(updateProductSql, imageUrl, productId);

                if (rows > 0) {
                    syncedCount++;
                    Map<String, Object> detail = new HashMap<>();
                    detail.put("productId", productId);
                    detail.put("imageUrl", imageUrl);
                    detail.put("synced", true);
                    syncDetails.add(detail);

                    System.out.println("🔄 同步产品 " + productId + " 的封面: " + imageUrl);
                }
            }

            // 验证同步结果
            String verifySql = "SELECT p.id, p.name, p.image_url as product_image, pi.image_url as primary_image " +
                              "FROM products p " +
                              "LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1 " +
                              "ORDER BY p.id LIMIT 10";
            List<Map<String, Object>> verifyData = jdbcTemplate.queryForList(verifySql);

            result.put("syncedCount", syncedCount);
            result.put("syncDetails", syncDetails);
            result.put("verifyData", verifyData);
            result.put("success", true);
            result.put("message", "成功同步了 " + syncedCount + " 个产品的封面图片");

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 检查产品封面同步状态
     */
    @GetMapping("/check-cover-sync")
    public Map<String, Object> checkCoverSync() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 检查products表和product_images表的数据一致性
            String checkSql = "SELECT " +
                             "p.id, " +
                             "p.name, " +
                             "p.image_url as product_image, " +
                             "pi.image_url as primary_image, " +
                             "CASE WHEN p.image_url = pi.image_url THEN '一致' ELSE '不一致' END as sync_status " +
                             "FROM products p " +
                             "LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1 " +
                             "ORDER BY p.id";

            List<Map<String, Object>> checkData = jdbcTemplate.queryForList(checkSql);

            int totalProducts = checkData.size();
            int syncedProducts = 0;
            int unsyncedProducts = 0;

            for (Map<String, Object> row : checkData) {
                String syncStatus = (String) row.get("sync_status");
                if ("一致".equals(syncStatus)) {
                    syncedProducts++;
                } else {
                    unsyncedProducts++;
                }
            }

            result.put("totalProducts", totalProducts);
            result.put("syncedProducts", syncedProducts);
            result.put("unsyncedProducts", unsyncedProducts);
            result.put("checkData", checkData);
            result.put("success", true);
            result.put("message", "检查完成：" + totalProducts + " 个产品中，" + syncedProducts + " 个已同步，" + unsyncedProducts + " 个未同步");

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 检查数据库表结构
     */
    @GetMapping("/check-tables")
    public Map<String, Object> checkTables() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 检查所有表
            String showTablesSql = "SHOW TABLES";
            List<Map<String, Object>> tables = jdbcTemplate.queryForList(showTablesSql);
            result.put("tables", tables);

            // 检查categories表结构
            try {
                String categoriesSql = "DESCRIBE categories";
                List<Map<String, Object>> categoriesStructure = jdbcTemplate.queryForList(categoriesSql);
                result.put("categoriesTable", categoriesStructure);
            } catch (Exception e) {
                try {
                    String categorySql = "DESCRIBE category";
                    List<Map<String, Object>> categoryStructure = jdbcTemplate.queryForList(categorySql);
                    result.put("categoryTable", categoryStructure);
                } catch (Exception e2) {
                    result.put("categoryTableError", "category/categories表不存在");
                }
            }

            // 检查product_category关联表
            try {
                String productCategorySql = "DESCRIBE product_category";
                List<Map<String, Object>> productCategoryStructure = jdbcTemplate.queryForList(productCategorySql);
                result.put("productCategoryTable", productCategoryStructure);
            } catch (Exception e) {
                result.put("productCategoryTableError", "product_category表不存在");
            }

            // 检查products表结构
            String productsSql = "DESCRIBE products";
            List<Map<String, Object>> productsStructure = jdbcTemplate.queryForList(productsSql);
            result.put("productsTable", productsStructure);

            result.put("success", true);
            result.put("message", "表结构检查完成");

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 初始化分类系统
     */
    @GetMapping("/init-category-system")
    public Map<String, Object> initCategorySystem() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 读取SQL文件并执行
            String[] sqlStatements = {
                // 创建categories表
                "CREATE TABLE IF NOT EXISTS `categories` (" +
                "  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID'," +
                "  `name` varchar(100) NOT NULL COMMENT '分类名称'," +
                "  `description` text COMMENT '分类描述'," +
                "  `sort_order` int(11) DEFAULT '0' COMMENT '排序顺序'," +
                "  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用'," +
                "  `created_time` bigint(20) DEFAULT NULL COMMENT '创建时间'," +
                "  `updated_time` bigint(20) DEFAULT NULL COMMENT '更新时间'," +
                "  PRIMARY KEY (`id`)," +
                "  UNIQUE KEY `uk_name` (`name`)" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品分类表'",

                // 创建product_category关联表
                "CREATE TABLE IF NOT EXISTS `product_category` (" +
                "  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '关联ID'," +
                "  `product_id` bigint(20) NOT NULL COMMENT '产品ID'," +
                "  `category_id` bigint(20) NOT NULL COMMENT '分类ID'," +
                "  `created_time` bigint(20) DEFAULT NULL COMMENT '创建时间'," +
                "  PRIMARY KEY (`id`)," +
                "  UNIQUE KEY `uk_product_category` (`product_id`, `category_id`)" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品分类关联表'",

                // 创建轮播图表
                "CREATE TABLE IF NOT EXISTS `carousel_images` (" +
                "  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '轮播图ID'," +
                "  `name` varchar(100) NOT NULL COMMENT '轮播图名称'," +
                "  `image_url` varchar(500) NOT NULL COMMENT '图片URL'," +
                "  `title` varchar(200) DEFAULT NULL COMMENT '轮播图标题'," +
                "  `description` text COMMENT '轮播图描述'," +
                "  `sort_order` int(11) DEFAULT '0' COMMENT '排序顺序'," +
                "  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用'," +
                "  `category_id` bigint(20) DEFAULT NULL COMMENT '关联分类ID'," +
                "  `created_time` bigint(20) DEFAULT NULL COMMENT '创建时间'," +
                "  `updated_time` bigint(20) DEFAULT NULL COMMENT '更新时间'," +
                "  PRIMARY KEY (`id`)" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='轮播图表'"
            };

            // 执行建表语句
            for (String sql : sqlStatements) {
                jdbcTemplate.execute(sql);
            }

            // 插入默认分类数据
            String insertCategoriesSql = "INSERT IGNORE INTO `categories` (`name`, `description`, `sort_order`, `created_time`, `updated_time`) VALUES " +
                "('护肤品', '各类护肤产品', 1, ?, ?), " +
                "('彩妆', '化妆品和彩妆用品', 2, ?, ?), " +
                "('香水', '各种香水产品', 3, ?, ?), " +
                "('面膜', '面膜护理产品', 4, ?, ?), " +
                "('洁面', '洁面清洁产品', 5, ?, ?), " +
                "('精华', '精华液产品', 6, ?, ?)";

            long currentTime = System.currentTimeMillis();
            jdbcTemplate.update(insertCategoriesSql,
                currentTime, currentTime, currentTime, currentTime, currentTime, currentTime,
                currentTime, currentTime, currentTime, currentTime, currentTime, currentTime);

            // 插入轮播图数据
            String insertCarouselSql = "INSERT IGNORE INTO `carousel_images` (`name`, `image_url`, `title`, `description`, `sort_order`, `category_id`, `created_time`, `updated_time`) VALUES " +
                "('轮播图', '/images/074dc74f-ab1e-4241-ad36-40a3dae3234a.jpg', '护肤精品推荐', '优质护肤产品，呵护您的肌肤', 1, 1, ?, ?), " +
                "('轮播图', '/images/496ea50b-83b6-438b-8493-2e49911b089e.jpg', '彩妆新品上市', '时尚彩妆，展现您的美丽', 2, 2, ?, ?), " +
                "('轮播图', '/images/acd8ab1a-4e42-41c5-bca6-21d886257b1c.jpg', '香水特惠活动', '迷人香氛，留住美好时光', 3, 3, ?, ?)";

            jdbcTemplate.update(insertCarouselSql,
                currentTime, currentTime, currentTime, currentTime, currentTime, currentTime);

            // 为现有产品分配默认分类
            String assignCategoriesSql = "INSERT IGNORE INTO `product_category` (`product_id`, `category_id`, `created_time`) " +
                "SELECT p.id, " +
                "CASE " +
                "  WHEN p.id % 6 = 1 THEN 1 " +  // 护肤品
                "  WHEN p.id % 6 = 2 THEN 2 " +  // 彩妆
                "  WHEN p.id % 6 = 3 THEN 3 " +  // 香水
                "  WHEN p.id % 6 = 4 THEN 4 " +  // 面膜
                "  WHEN p.id % 6 = 5 THEN 5 " +  // 洁面
                "  ELSE 6 " +                    // 精华
                "END as category_id, " +
                "? " +
                "FROM `products` p " +
                "WHERE NOT EXISTS (SELECT 1 FROM `product_category` pc WHERE pc.product_id = p.id)";

            int assignedProducts = jdbcTemplate.update(assignCategoriesSql, currentTime);

            // 验证结果
            String categoriesCountSql = "SELECT COUNT(*) FROM categories";
            int categoriesCount = jdbcTemplate.queryForObject(categoriesCountSql, Integer.class);

            String carouselCountSql = "SELECT COUNT(*) FROM carousel_images";
            int carouselCount = jdbcTemplate.queryForObject(carouselCountSql, Integer.class);

            String relationCountSql = "SELECT COUNT(*) FROM product_category";
            int relationCount = jdbcTemplate.queryForObject(relationCountSql, Integer.class);

            result.put("success", true);
            result.put("message", "分类系统初始化完成");
            result.put("categoriesCount", categoriesCount);
            result.put("carouselCount", carouselCount);
            result.put("relationCount", relationCount);
            result.put("assignedProducts", assignedProducts);

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 测试产品更新功能
     */
    @PostMapping("/test-product-update")
    public Map<String, Object> testProductUpdate(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<>();

        try {
            System.out.println("🔄 收到产品更新测试请求:");
            System.out.println("请求数据: " + requestData);

            // 模拟成功响应
            result.put("success", true);
            result.put("message", "测试更新成功");
            result.put("receivedData", requestData);
            result.put("timestamp", System.currentTimeMillis());

            System.out.println("✅ 测试响应: " + result);

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 调试产品分类关联功能
     */
    @PostMapping("/debug-product-category")
    public Map<String, Object> debugProductCategory(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<>();

        try {
            System.out.println("🔄 调试产品分类关联:");
            System.out.println("请求数据: " + requestData);

            Long productId = Long.valueOf(requestData.get("productId").toString());
            @SuppressWarnings("unchecked")
            List<Integer> categoryIdsInt = (List<Integer>) requestData.get("categoryIds");

            // 转换为Long类型
            List<Long> categoryIds = new ArrayList<>();
            if (categoryIdsInt != null) {
                for (Integer id : categoryIdsInt) {
                    categoryIds.add(id.longValue());
                }
            }

            System.out.println("产品ID: " + productId);
            System.out.println("分类IDs: " + categoryIds);

            // 1. 检查product_category表是否存在
            try {
                String checkTableSql = "SELECT COUNT(*) FROM product_category LIMIT 1";
                jdbcTemplate.queryForObject(checkTableSql, Integer.class);
                result.put("tableExists", true);
            } catch (Exception e) {
                result.put("tableExists", false);
                result.put("tableError", e.getMessage());
                System.err.println("❌ product_category表不存在: " + e.getMessage());
            }

            // 2. 尝试删除现有关联
            try {
                String deleteSql = "DELETE FROM product_category WHERE product_id = ?";
                int deletedRows = jdbcTemplate.update(deleteSql, productId);
                result.put("deletedRows", deletedRows);
                System.out.println("✅ 删除了 " + deletedRows + " 条现有关联");
            } catch (Exception e) {
                result.put("deleteError", e.getMessage());
                System.err.println("❌ 删除现有关联失败: " + e.getMessage());
            }

            // 3. 尝试插入新关联
            if (categoryIds != null && !categoryIds.isEmpty()) {
                int insertedRows = 0;
                long currentTime = System.currentTimeMillis();

                for (Long categoryId : categoryIds) {
                    try {
                        String insertSql = "INSERT INTO product_category (product_id, category_id, created_time) VALUES (?, ?, ?)";
                        jdbcTemplate.update(insertSql, productId, categoryId, currentTime);
                        insertedRows++;
                        System.out.println("✅ 插入关联: 产品" + productId + " -> 分类" + categoryId);
                    } catch (Exception e) {
                        System.err.println("❌ 插入关联失败: 产品" + productId + " -> 分类" + categoryId + ", 错误: " + e.getMessage());
                        result.put("insertError_" + categoryId, e.getMessage());
                    }
                }

                result.put("insertedRows", insertedRows);
            }

            // 4. 验证结果
            try {
                String verifySql = "SELECT product_id, category_id, created_time FROM product_category WHERE product_id = ?";
                List<Map<String, Object>> verifyData = jdbcTemplate.queryForList(verifySql, productId);
                result.put("verifyData", verifyData);
                System.out.println("✅ 验证结果: " + verifyData);
            } catch (Exception e) {
                result.put("verifyError", e.getMessage());
                System.err.println("❌ 验证失败: " + e.getMessage());
            }

            result.put("success", true);
            result.put("message", "调试完成");

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 简化测试产品分类关联
     */
    @GetMapping("/test-category-simple")
    public Map<String, Object> testCategorySimple() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 测试数据
            Long productId = 1L;
            List<Long> categoryIds = new ArrayList<>();
            categoryIds.add(1L);
            categoryIds.add(2L);

            System.out.println("🔄 简化测试: 产品ID=" + productId + ", 分类IDs=" + categoryIds);

            // 直接使用JdbcTemplate测试
            // 1. 删除现有关联
            String deleteSql = "DELETE FROM product_category WHERE product_id = ?";
            int deletedRows = jdbcTemplate.update(deleteSql, productId);
            System.out.println("🗑️ 删除了 " + deletedRows + " 条现有关联");

            // 2. 插入新关联
            long currentTime = System.currentTimeMillis();
            int insertedRows = 0;

            for (Long categoryId : categoryIds) {
                String insertSql = "INSERT INTO product_category (product_id, category_id, created_time) VALUES (?, ?, ?)";
                jdbcTemplate.update(insertSql, productId, categoryId, currentTime);
                insertedRows++;
                System.out.println("✅ 插入关联: 产品" + productId + " -> 分类" + categoryId);
            }

            // 3. 验证结果
            String verifySql = "SELECT product_id, category_id, created_time FROM product_category WHERE product_id = ?";
            List<Map<String, Object>> verifyData = jdbcTemplate.queryForList(verifySql, productId);

            result.put("success", true);
            result.put("deletedRows", deletedRows);
            result.put("insertedRows", insertedRows);
            result.put("verifyData", verifyData);
            result.put("message", "简化测试成功");

            System.out.println("✅ 简化测试成功: 插入了 " + insertedRows + " 条关联");

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            System.err.println("❌ 简化测试失败: " + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 直接测试产品更新功能
     */
    @PostMapping("/test-product-update-direct")
    public Map<String, Object> testProductUpdateDirect() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 模拟产品更新请求
            Long productId = 1L;
            List<Long> categoryIds = new ArrayList<>();
            categoryIds.add(1L);
            categoryIds.add(2L);

            System.out.println("🔄 直接测试产品更新: 产品ID=" + productId + ", 分类IDs=" + categoryIds);

            // 1. 检查产品是否存在
            String checkProductSql = "SELECT id, name FROM products WHERE id = ?";
            try {
                Map<String, Object> product = jdbcTemplate.queryForMap(checkProductSql, productId);
                result.put("productExists", true);
                result.put("productData", product);
                System.out.println("✅ 产品存在: " + product);
            } catch (Exception e) {
                result.put("productExists", false);
                result.put("productError", e.getMessage());
                System.err.println("❌ 产品不存在: " + e.getMessage());
            }

            // 2. 检查分类是否存在
            String checkCategoriesSql = "SELECT id, name FROM categories WHERE id IN (?, ?)";
            try {
                List<Map<String, Object>> categories = jdbcTemplate.queryForList(checkCategoriesSql, 1L, 2L);
                result.put("categoriesExist", true);
                result.put("categoriesData", categories);
                System.out.println("✅ 分类存在: " + categories);
            } catch (Exception e) {
                result.put("categoriesExist", false);
                result.put("categoriesError", e.getMessage());
                System.err.println("❌ 分类检查失败: " + e.getMessage());
            }

            // 3. 直接测试分类关联保存
            try {
                // 删除现有关联
                String deleteSql = "DELETE FROM product_category WHERE product_id = ?";
                int deletedRows = jdbcTemplate.update(deleteSql, productId);
                System.out.println("🗑️ 删除了 " + deletedRows + " 条现有关联");

                // 插入新关联
                long currentTime = System.currentTimeMillis();
                int insertedRows = 0;

                for (Long categoryId : categoryIds) {
                    String insertSql = "INSERT INTO product_category (product_id, category_id, created_time) VALUES (?, ?, ?)";
                    jdbcTemplate.update(insertSql, productId, categoryId, currentTime);
                    insertedRows++;
                    System.out.println("✅ 插入关联: 产品" + productId + " -> 分类" + categoryId);
                }

                // 验证结果
                String verifySql = "SELECT product_id, category_id, created_time FROM product_category WHERE product_id = ?";
                List<Map<String, Object>> verifyData = jdbcTemplate.queryForList(verifySql, productId);

                result.put("categoryUpdateSuccess", true);
                result.put("deletedRows", deletedRows);
                result.put("insertedRows", insertedRows);
                result.put("verifyData", verifyData);
                System.out.println("✅ 分类关联更新成功: " + verifyData);

            } catch (Exception e) {
                result.put("categoryUpdateSuccess", false);
                result.put("categoryUpdateError", e.getMessage());
                System.err.println("❌ 分类关联更新失败: " + e.getMessage());
                e.printStackTrace();
            }

            result.put("success", true);
            result.put("message", "直接测试完成");

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            System.err.println("❌ 直接测试失败: " + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 修复product_category表结构
     */
    @GetMapping("/fix-product-category-table")
    public Map<String, Object> fixProductCategoryTable() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 检查表是否存在
            String checkTableSql = "SHOW TABLES LIKE 'product_category'";
            List<Map<String, Object>> tables = jdbcTemplate.queryForList(checkTableSql);

            if (tables.isEmpty()) {
                // 表不存在，创建表
                String createTableSql = "CREATE TABLE product_category (" +
                    "id BIGINT AUTO_INCREMENT PRIMARY KEY," +
                    "product_id BIGINT NOT NULL," +
                    "category_id BIGINT NOT NULL," +
                    "created_time BIGINT DEFAULT NULL," +
                    "UNIQUE KEY unique_product_category (product_id, category_id)," +
                    "KEY idx_product_id (product_id)," +
                    "KEY idx_category_id (category_id)" +
                    ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品分类关联表'";

                jdbcTemplate.execute(createTableSql);
                result.put("tableCreated", true);
                result.put("message", "成功创建product_category表");
                System.out.println("✅ 成功创建product_category表");

            } else {
                // 表存在，检查字段
                String descTableSql = "DESCRIBE product_category";
                List<Map<String, Object>> columns = jdbcTemplate.queryForList(descTableSql);

                result.put("tableExists", true);
                result.put("currentColumns", columns);

                // 检查是否有created_time字段
                boolean hasCreatedTime = columns.stream()
                    .anyMatch(col -> "created_time".equals(col.get("Field")));

                if (!hasCreatedTime) {
                    // 添加created_time字段
                    String addColumnSql = "ALTER TABLE product_category ADD COLUMN created_time BIGINT DEFAULT NULL COMMENT '创建时间'";
                    jdbcTemplate.execute(addColumnSql);
                    result.put("createdTimeAdded", true);
                    System.out.println("✅ 成功添加created_time字段");
                } else {
                    result.put("createdTimeExists", true);
                    System.out.println("✅ created_time字段已存在");
                }

                // 检查是否有id字段
                boolean hasId = columns.stream()
                    .anyMatch(col -> "id".equals(col.get("Field")));

                if (!hasId) {
                    // 添加id字段作为主键
                    String addIdSql = "ALTER TABLE product_category ADD COLUMN id BIGINT AUTO_INCREMENT PRIMARY KEY FIRST";
                    jdbcTemplate.execute(addIdSql);
                    result.put("idAdded", true);
                    System.out.println("✅ 成功添加id主键字段");
                } else {
                    result.put("idExists", true);
                    System.out.println("✅ id字段已存在");
                }
            }

            // 2. 验证最终表结构
            String finalDescSql = "DESCRIBE product_category";
            List<Map<String, Object>> finalColumns = jdbcTemplate.queryForList(finalDescSql);
            result.put("finalTableStructure", finalColumns);

            // 3. 测试插入数据
            try {
                long currentTime = System.currentTimeMillis();
                String testInsertSql = "INSERT INTO product_category (product_id, category_id, created_time) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE created_time = VALUES(created_time)";
                jdbcTemplate.update(testInsertSql, 1L, 1L, currentTime);
                result.put("testInsertSuccess", true);
                System.out.println("✅ 测试插入成功");

                // 删除测试数据
                String deleteTestSql = "DELETE FROM product_category WHERE product_id = 1 AND category_id = 1";
                jdbcTemplate.update(deleteTestSql);

            } catch (Exception e) {
                result.put("testInsertSuccess", false);
                result.put("testInsertError", e.getMessage());
                System.err.println("❌ 测试插入失败: " + e.getMessage());
            }

            result.put("success", true);
            result.put("message", "product_category表结构修复完成");

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            System.err.println("❌ 修复product_category表失败: " + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 数据库表结构分析和优化
     */
    @GetMapping("/analyze-database")
    public Map<String, Object> analyzeDatabase() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 获取所有表
            String showTablesSql = "SHOW TABLES";
            List<Map<String, Object>> tables = jdbcTemplate.queryForList(showTablesSql);

            List<Map<String, Object>> tableAnalysis = new ArrayList<>();

            for (Map<String, Object> table : tables) {
                String tableName = (String) table.values().iterator().next();
                Map<String, Object> tableInfo = new HashMap<>();
                tableInfo.put("tableName", tableName);

                // 获取表结构
                String descSql = "DESCRIBE " + tableName;
                List<Map<String, Object>> columns = jdbcTemplate.queryForList(descSql);
                tableInfo.put("columns", columns);

                // 获取表数据量
                String countSql = "SELECT COUNT(*) as count FROM " + tableName;
                Map<String, Object> countResult = jdbcTemplate.queryForMap(countSql);
                tableInfo.put("rowCount", countResult.get("count"));

                // 获取表注释
                String commentSql = "SELECT TABLE_COMMENT FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?";
                try {
                    Map<String, Object> commentResult = jdbcTemplate.queryForMap(commentSql, tableName);
                    tableInfo.put("comment", commentResult.get("TABLE_COMMENT"));
                } catch (Exception e) {
                    tableInfo.put("comment", "");
                }

                tableAnalysis.add(tableInfo);
            }

            result.put("tables", tableAnalysis);
            result.put("totalTables", tables.size());
            result.put("success", true);

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 优化数据库表结构
     */
    @PostMapping("/optimize-database")
    public Map<String, Object> optimizeDatabase(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();

        try {
            @SuppressWarnings("unchecked")
            List<String> tablesToDrop = (List<String>) request.get("tablesToDrop");

            List<String> droppedTables = new ArrayList<>();
            List<String> errors = new ArrayList<>();

            if (tablesToDrop != null) {
                for (String tableName : tablesToDrop) {
                    try {
                        // 安全检查：只允许删除特定的表
                        if (isTableSafeToDelete(tableName)) {
                            String dropSql = "DROP TABLE IF EXISTS " + tableName;
                            jdbcTemplate.execute(dropSql);
                            droppedTables.add(tableName);
                            System.out.println("✅ 删除表: " + tableName);
                        } else {
                            errors.add("表 " + tableName + " 不允许删除（核心表）");
                        }
                    } catch (Exception e) {
                        errors.add("删除表 " + tableName + " 失败: " + e.getMessage());
                    }
                }
            }

            result.put("droppedTables", droppedTables);
            result.put("errors", errors);
            result.put("success", true);
            result.put("message", "数据库优化完成");

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 检查表是否可以安全删除
     */
    private boolean isTableSafeToDelete(String tableName) {
        // 核心表列表，不允许删除
        List<String> coreTables = Arrays.asList(
            "users", "products", "categories", "product_category",
            "user_details", "product_images", "carousel_images"
        );

        return !coreTables.contains(tableName.toLowerCase());
    }

    /**
     * 创建标准化的数据库表结构
     */
    @PostMapping("/create-standard-tables")
    public Map<String, Object> createStandardTables() {
        Map<String, Object> result = new HashMap<>();

        try {
            List<String> createdTables = new ArrayList<>();
            List<String> errors = new ArrayList<>();

            // 1. 用户表
            try {
                String usersSql = "CREATE TABLE IF NOT EXISTS users (" +
                    "id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID'," +
                    "user_id VARCHAR(50) UNIQUE NOT NULL COMMENT '用户账号'," +
                    "username VARCHAR(100) NOT NULL COMMENT '用户名'," +
                    "phone VARCHAR(20) UNIQUE NOT NULL COMMENT '手机号'," +
                    "password VARCHAR(255) NOT NULL COMMENT '密码'," +
                    "register_time BIGINT NOT NULL COMMENT '注册时间'," +
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'," +
                    "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'," +
                    "INDEX idx_phone (phone)," +
                    "INDEX idx_user_id (user_id)" +
                    ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户基础信息表'";
                jdbcTemplate.execute(usersSql);
                createdTables.add("users");
            } catch (Exception e) {
                errors.add("创建users表失败: " + e.getMessage());
            }

            // 2. 用户详情表
            try {
                String userDetailsSql = "CREATE TABLE IF NOT EXISTS user_details (" +
                    "id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '详情ID'," +
                    "phone VARCHAR(20) UNIQUE NOT NULL COMMENT '手机号（外键）'," +
                    "avatar VARCHAR(500) DEFAULT NULL COMMENT '头像URL'," +
                    "signature VARCHAR(200) DEFAULT NULL COMMENT '个性签名'," +
                    "friends TEXT DEFAULT NULL COMMENT '好友列表（JSON）'," +
                    "addresses TEXT DEFAULT NULL COMMENT '收货地址（JSON）'," +
                    "cart TEXT DEFAULT NULL COMMENT '购物车（JSON）'," +
                    "orders TEXT DEFAULT NULL COMMENT '订单信息（JSON）'," +
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'," +
                    "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'," +
                    "FOREIGN KEY (phone) REFERENCES users(phone) ON DELETE CASCADE ON UPDATE CASCADE" +
                    ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户详细信息表'";
                jdbcTemplate.execute(userDetailsSql);
                createdTables.add("user_details");
            } catch (Exception e) {
                errors.add("创建user_details表失败: " + e.getMessage());
            }

            // 3. 分类表
            try {
                String categoriesSql = "CREATE TABLE IF NOT EXISTS categories (" +
                    "id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '分类ID'," +
                    "name VARCHAR(100) NOT NULL COMMENT '分类名称'," +
                    "description TEXT DEFAULT NULL COMMENT '分类描述'," +
                    "sort_order INT DEFAULT 0 COMMENT '排序权重'," +
                    "is_active TINYINT(1) DEFAULT 1 COMMENT '是否启用'," +
                    "created_time BIGINT DEFAULT NULL COMMENT '创建时间戳'," +
                    "updated_time BIGINT DEFAULT NULL COMMENT '更新时间戳'," +
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'," +
                    "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'," +
                    "UNIQUE KEY unique_name (name)," +
                    "INDEX idx_sort_order (sort_order)" +
                    ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品分类表'";
                jdbcTemplate.execute(categoriesSql);
                createdTables.add("categories");
            } catch (Exception e) {
                errors.add("创建categories表失败: " + e.getMessage());
            }

            // 4. 产品表
            try {
                String productsSql = "CREATE TABLE IF NOT EXISTS products (" +
                    "id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '产品ID'," +
                    "name VARCHAR(200) NOT NULL COMMENT '产品名称'," +
                    "description TEXT DEFAULT NULL COMMENT '产品描述'," +
                    "price DECIMAL(10,2) NOT NULL COMMENT '产品价格'," +
                    "stock INT DEFAULT 0 COMMENT '库存数量'," +
                    "image_url VARCHAR(500) DEFAULT NULL COMMENT '主图片URL'," +
                    "is_new TINYINT(1) DEFAULT 0 COMMENT '是否新品'," +
                    "is_active TINYINT(1) DEFAULT 1 COMMENT '是否启用'," +
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'," +
                    "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'," +
                    "INDEX idx_name (name)," +
                    "INDEX idx_price (price)," +
                    "INDEX idx_is_new (is_new)" +
                    ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品信息表'";
                jdbcTemplate.execute(productsSql);
                createdTables.add("products");
            } catch (Exception e) {
                errors.add("创建products表失败: " + e.getMessage());
            }

            // 5. 产品分类关联表
            try {
                String productCategorySql = "CREATE TABLE IF NOT EXISTS product_category (" +
                    "id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '关联ID'," +
                    "product_id BIGINT NOT NULL COMMENT '产品ID'," +
                    "category_id BIGINT NOT NULL COMMENT '分类ID'," +
                    "created_time BIGINT DEFAULT NULL COMMENT '创建时间戳'," +
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'," +
                    "UNIQUE KEY unique_product_category (product_id, category_id)," +
                    "INDEX idx_product_id (product_id)," +
                    "INDEX idx_category_id (category_id)," +
                    "FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE," +
                    "FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE" +
                    ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品分类关联表'";
                jdbcTemplate.execute(productCategorySql);
                createdTables.add("product_category");
            } catch (Exception e) {
                errors.add("创建product_category表失败: " + e.getMessage());
            }

            // 6. 产品图片表
            try {
                String productImagesSql = "CREATE TABLE IF NOT EXISTS product_images (" +
                    "id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '图片ID'," +
                    "product_id BIGINT NOT NULL COMMENT '产品ID'," +
                    "image_url VARCHAR(500) NOT NULL COMMENT '图片URL'," +
                    "image_name VARCHAR(200) DEFAULT NULL COMMENT '图片名称'," +
                    "is_primary TINYINT(1) DEFAULT 0 COMMENT '是否主图'," +
                    "sort_order INT DEFAULT 0 COMMENT '排序权重'," +
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'," +
                    "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'," +
                    "INDEX idx_product_id (product_id)," +
                    "INDEX idx_is_primary (is_primary)," +
                    "FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE" +
                    ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品图片表'";
                jdbcTemplate.execute(productImagesSql);
                createdTables.add("product_images");
            } catch (Exception e) {
                errors.add("创建product_images表失败: " + e.getMessage());
            }

            // 7. 轮播图表
            try {
                String carouselImagesSql = "CREATE TABLE IF NOT EXISTS carousel_images (" +
                    "id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '轮播图ID'," +
                    "name VARCHAR(100) NOT NULL COMMENT '轮播图名称'," +
                    "image_url VARCHAR(500) NOT NULL COMMENT '图片URL'," +
                    "title VARCHAR(200) DEFAULT NULL COMMENT '标题'," +
                    "category_id BIGINT DEFAULT NULL COMMENT '关联分类ID'," +
                    "sort_order INT DEFAULT 0 COMMENT '排序权重'," +
                    "is_active TINYINT(1) DEFAULT 1 COMMENT '是否启用'," +
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'," +
                    "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'," +
                    "INDEX idx_name (name)," +
                    "INDEX idx_category_id (category_id)," +
                    "INDEX idx_sort_order (sort_order)," +
                    "FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL" +
                    ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='轮播图管理表'";
                jdbcTemplate.execute(carouselImagesSql);
                createdTables.add("carousel_images");
            } catch (Exception e) {
                errors.add("创建carousel_images表失败: " + e.getMessage());
            }

            result.put("createdTables", createdTables);
            result.put("errors", errors);
            result.put("success", true);
            result.put("message", "标准化表结构创建完成");

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 创建product_images表和测试数据
     */
    @PostMapping("/create-product-images-table")
    public Map<String, Object> createProductImagesTable() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 创建product_images表（如果不存在）
            String createTableSql = "CREATE TABLE IF NOT EXISTS `product_images` (" +
                "  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '图片ID'," +
                "  `product_id` bigint(20) NOT NULL COMMENT '产品ID'," +
                "  `image_url` varchar(500) NOT NULL COMMENT '图片URL'," +
                "  `image_name` varchar(255) DEFAULT NULL COMMENT '图片名称'," +
                "  `is_primary` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为主图(0:否, 1:是)'," +
                "  `sort_order` int(11) DEFAULT '0' COMMENT '排序顺序'," +
                "  `created_time` bigint(20) DEFAULT NULL COMMENT '创建时间'," +
                "  `updated_time` bigint(20) DEFAULT NULL COMMENT '更新时间'," +
                "  PRIMARY KEY (`id`)," +
                "  KEY `idx_product_id` (`product_id`)," +
                "  KEY `idx_is_primary` (`is_primary`)," +
                "  KEY `idx_sort_order` (`sort_order`)" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品图片表'";

            jdbcTemplate.execute(createTableSql);

            // 2. 清空现有数据
            String clearSql = "DELETE FROM product_images";
            jdbcTemplate.update(clearSql);

            // 3. 插入测试数据
            String[] imageUrls = {
                "/images/074dc74f-ab1e-4241-ad36-40a3dae3234a.jpg",
                "/images/496ea50b-83b6-438b-8493-2e49911b089e.jpg",
                "/images/acd8ab1a-4e42-41c5-bca6-21d886257b1c.jpg"
            };

            long currentTime = System.currentTimeMillis();
            int insertedCount = 0;

            // 为前10个产品创建图片数据
            for (int productId = 1; productId <= 10; productId++) {
                // 每个产品2-3张图片
                int imageCount = 2 + (productId % 2); // 2或3张图片

                for (int i = 0; i < imageCount; i++) {
                    String imageUrl = imageUrls[i % imageUrls.length];
                    String imageName = "产品" + productId + "图片" + (i + 1) + ".jpg";
                    boolean isPrimary = (i == 0); // 第一张设为主图

                    String insertSql = "INSERT INTO product_images (product_id, image_url, image_name, is_primary, sort_order, created_time, updated_time) " +
                                     "VALUES (?, ?, ?, ?, ?, ?, ?)";

                    jdbcTemplate.update(insertSql, productId, imageUrl, imageName, isPrimary ? 1 : 0, i, currentTime, currentTime);
                    insertedCount++;
                }
            }

            // 4. 同步更新products表的image_url字段
            String syncSql = "UPDATE products p " +
                           "JOIN product_images pi ON p.id = pi.product_id " +
                           "SET p.image_url = pi.image_url " +
                           "WHERE pi.is_primary = 1";
            int syncedCount = jdbcTemplate.update(syncSql);

            // 5. 验证结果
            String verifySql = "SELECT p.id, p.name, p.image_url, " +
                             "(SELECT COUNT(*) FROM product_images pi WHERE pi.product_id = p.id) as image_count " +
                             "FROM products p ORDER BY p.id LIMIT 10";
            List<Map<String, Object>> verifyData = jdbcTemplate.queryForList(verifySql);

            result.put("success", true);
            result.put("message", "成功创建product_images表并插入测试数据");
            result.put("insertedImages", insertedCount);
            result.put("syncedProducts", syncedCount);
            result.put("verifyData", verifyData);

            System.out.println("✅ 成功创建product_images表，插入 " + insertedCount + " 张图片，同步 " + syncedCount + " 个产品");

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            System.err.println("❌ 创建product_images表失败: " + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 创建轮播图测试数据
     */
    @PostMapping("/create-carousel-data")
    public Map<String, Object> createCarouselData() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 确保carousel_images表存在
            String createTableSql = "CREATE TABLE IF NOT EXISTS `carousel_images` (" +
                "  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '轮播图ID'," +
                "  `name` varchar(100) NOT NULL COMMENT '轮播图名称'," +
                "  `image_url` varchar(500) NOT NULL COMMENT '图片URL'," +
                "  `title` varchar(200) DEFAULT NULL COMMENT '轮播图标题'," +
                "  `description` text COMMENT '轮播图描述'," +
                "  `sort_order` int(11) DEFAULT '0' COMMENT '排序顺序'," +
                "  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用(0:否, 1:是)'," +
                "  `category_id` bigint(20) DEFAULT NULL COMMENT '关联分类ID'," +
                "  `created_time` bigint(20) DEFAULT NULL COMMENT '创建时间'," +
                "  `updated_time` bigint(20) DEFAULT NULL COMMENT '更新时间'," +
                "  PRIMARY KEY (`id`)," +
                "  KEY `idx_name` (`name`)," +
                "  KEY `idx_sort_order` (`sort_order`)," +
                "  KEY `idx_is_active` (`is_active`)" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='轮播图表'";

            jdbcTemplate.execute(createTableSql);

            // 2. 清空现有轮播图数据
            String clearSql = "DELETE FROM carousel_images WHERE name = '轮播图'";
            jdbcTemplate.update(clearSql);

            // 3. 插入测试轮播图数据
            long currentTime = System.currentTimeMillis();
            String insertSql = "INSERT INTO carousel_images (name, image_url, title, description, sort_order, category_id, is_active, created_time, updated_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

            // 轮播图数据 - 使用banner路径
            Object[][] carouselData = {
                {"轮播图", "/images/banner/074dc74f-ab1e-4241-ad36-40a3dae3234a.jpg", "护肤精品推荐", "优质护肤产品，呵护您的肌肤健康", 1, 1, true},
                {"轮播图", "/images/banner/496ea50b-83b6-438b-8493-2e49911b089e.jpg", "彩妆新品上市", "时尚彩妆，展现您的美丽风采", 2, 2, true},
                {"轮播图", "/images/banner/acd8ab1a-4e42-41c5-bca6-21d886257b1c.jpg", "香水特惠活动", "迷人香氛，留住美好时光", 3, 3, true},
                {"轮播图", "/images/banner/074dc74f-ab1e-4241-ad36-40a3dae3234a.jpg", "面膜护理专区", "深层滋养，焕发肌肤光彩", 4, 4, false},
                {"轮播图", "/images/banner/496ea50b-83b6-438b-8493-2e49911b089e.jpg", "洁面清洁系列", "温和清洁，呵护肌肤屏障", 5, 5, false}
            };

            int insertedCount = 0;
            for (Object[] data : carouselData) {
                jdbcTemplate.update(insertSql,
                    data[0], data[1], data[2], data[3], data[4], data[5], data[6], currentTime, currentTime);
                insertedCount++;
            }

            // 4. 验证结果
            String verifySql = "SELECT id, name, title, description, image_url, sort_order, is_active FROM carousel_images WHERE name = '轮播图' ORDER BY sort_order";
            List<Map<String, Object>> verifyData = jdbcTemplate.queryForList(verifySql);

            result.put("success", true);
            result.put("message", "轮播图测试数据创建成功");
            result.put("insertedCount", insertedCount);
            result.put("verifyData", verifyData);

            System.out.println("✅ 成功创建轮播图测试数据，共插入 " + insertedCount + " 条记录");

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            System.err.println("❌ 创建轮播图测试数据失败: " + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 检查产品封面图片同步状态
     */
    @GetMapping("/check-product-covers")
    public Map<String, Object> checkProductCovers() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 获取所有产品及其主图片信息
            String productsSql = "SELECT p.id, p.name, p.image_url as product_image_url FROM products p ORDER BY p.id";
            List<Map<String, Object>> products = jdbcTemplate.queryForList(productsSql);

            List<Map<String, Object>> coverAnalysis = new ArrayList<>();
            int syncedCount = 0;
            int unsyncedCount = 0;

            for (Map<String, Object> product : products) {
                Long productId = ((Number) product.get("id")).longValue();
                String productImageUrl = (String) product.get("product_image_url");

                Map<String, Object> analysis = new HashMap<>();
                analysis.put("productId", productId);
                analysis.put("productName", product.get("name"));
                analysis.put("productImageUrl", productImageUrl);

                // 2. 获取该产品的主图片（is_primary=1）
                String primaryImageSql = "SELECT image_url FROM product_images WHERE product_id = ? AND is_primary = 1 LIMIT 1";
                try {
                    Map<String, Object> primaryImage = jdbcTemplate.queryForMap(primaryImageSql, productId);
                    String primaryImageUrl = (String) primaryImage.get("image_url");
                    analysis.put("primaryImageUrl", primaryImageUrl);

                    // 3. 检查是否同步
                    boolean isSynced = (productImageUrl != null && productImageUrl.equals(primaryImageUrl)) ||
                                     (productImageUrl == null && primaryImageUrl == null);
                    analysis.put("isSynced", isSynced);

                    if (isSynced) {
                        syncedCount++;
                    } else {
                        unsyncedCount++;
                    }

                } catch (Exception e) {
                    // 没有主图片
                    analysis.put("primaryImageUrl", null);
                    analysis.put("isSynced", productImageUrl == null);
                    analysis.put("error", "没有设置主图片");

                    if (productImageUrl == null) {
                        syncedCount++;
                    } else {
                        unsyncedCount++;
                    }
                }

                // 4. 获取该产品的所有图片
                String allImagesSql = "SELECT image_url, is_primary, sort_order FROM product_images WHERE product_id = ? ORDER BY sort_order, id";
                List<Map<String, Object>> allImages = jdbcTemplate.queryForList(allImagesSql, productId);
                analysis.put("allImages", allImages);
                analysis.put("totalImages", allImages.size());

                coverAnalysis.add(analysis);
            }

            result.put("products", coverAnalysis);
            result.put("totalProducts", products.size());
            result.put("syncedCount", syncedCount);
            result.put("unsyncedCount", unsyncedCount);
            result.put("syncRate", products.size() > 0 ? (double) syncedCount / products.size() * 100 : 0);
            result.put("success", true);

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 修复产品封面图片同步
     */
    @PostMapping("/fix-product-covers")
    public Map<String, Object> fixProductCovers() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 获取所有产品
            String productsSql = "SELECT id, name, image_url FROM products ORDER BY id";
            List<Map<String, Object>> products = jdbcTemplate.queryForList(productsSql);

            int fixedCount = 0;
            int errorCount = 0;
            List<String> fixedProducts = new ArrayList<>();
            List<String> errors = new ArrayList<>();

            for (Map<String, Object> product : products) {
                Long productId = ((Number) product.get("id")).longValue();
                String productName = (String) product.get("name");
                String currentImageUrl = (String) product.get("image_url");

                try {
                    // 2. 查找该产品的主图片
                    String primaryImageSql = "SELECT image_url FROM product_images WHERE product_id = ? AND is_primary = 1 LIMIT 1";
                    String primaryImageUrl = null;

                    try {
                        Map<String, Object> primaryImage = jdbcTemplate.queryForMap(primaryImageSql, productId);
                        primaryImageUrl = (String) primaryImage.get("image_url");
                    } catch (Exception e) {
                        // 没有主图片，尝试获取第一张图片
                        String firstImageSql = "SELECT image_url FROM product_images WHERE product_id = ? ORDER BY sort_order, id LIMIT 1";
                        try {
                            Map<String, Object> firstImage = jdbcTemplate.queryForMap(firstImageSql, productId);
                            primaryImageUrl = (String) firstImage.get("image_url");

                            // 将第一张图片设为主图
                            String setPrimarySql = "UPDATE product_images SET is_primary = 0 WHERE product_id = ?";
                            jdbcTemplate.update(setPrimarySql, productId);

                            String setFirstPrimarySql = "UPDATE product_images SET is_primary = 1 WHERE product_id = ? AND image_url = ?";
                            jdbcTemplate.update(setFirstPrimarySql, productId, primaryImageUrl);

                            System.out.println("✅ 设置产品 " + productId + " 的第一张图片为主图: " + primaryImageUrl);
                        } catch (Exception e2) {
                            // 没有任何图片
                            primaryImageUrl = null;
                        }
                    }

                    // 3. 同步产品表的image_url字段
                    if (primaryImageUrl != null && !primaryImageUrl.equals(currentImageUrl)) {
                        String updateProductSql = "UPDATE products SET image_url = ? WHERE id = ?";
                        jdbcTemplate.update(updateProductSql, primaryImageUrl, productId);

                        fixedProducts.add(productName + " (ID: " + productId + ")");
                        fixedCount++;
                        System.out.println("✅ 修复产品封面: " + productName + " -> " + primaryImageUrl);

                    } else if (primaryImageUrl == null && currentImageUrl != null) {
                        // 产品表有图片但product_images表没有，清空产品表的图片
                        String clearProductSql = "UPDATE products SET image_url = NULL WHERE id = ?";
                        jdbcTemplate.update(clearProductSql, productId);

                        fixedProducts.add(productName + " (ID: " + productId + ") - 清空无效图片");
                        fixedCount++;
                        System.out.println("✅ 清空产品无效封面: " + productName);
                    }

                } catch (Exception e) {
                    errors.add("修复产品 " + productName + " (ID: " + productId + ") 失败: " + e.getMessage());
                    errorCount++;
                    System.err.println("❌ 修复产品封面失败: " + productName + " - " + e.getMessage());
                }
            }

            result.put("fixedCount", fixedCount);
            result.put("errorCount", errorCount);
            result.put("fixedProducts", fixedProducts);
            result.put("errors", errors);
            result.put("totalProducts", products.size());
            result.put("success", true);
            result.put("message", "封面修复完成，共修复 " + fixedCount + " 个产品");

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 测试产品列表主图显示
     */
    @GetMapping("/test-product-list-images")
    public Map<String, Object> testProductListImages() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 获取产品列表（模拟前端调用）
            String sql = "SELECT " +
                "p.id, p.name, p.description, p.price, p.stock, p.is_new, " +
                "p.image_url as product_table_image, " +
                "pi.image_url as primary_image_url, " +
                "pi.is_primary, " +
                "COALESCE(pi.image_url, p.image_url) as display_image_url " +
                "FROM products p " +
                "LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1 " +
                "ORDER BY p.id DESC " +
                "LIMIT 10";

            List<Map<String, Object>> products = jdbcTemplate.queryForList(sql);

            // 2. 统计信息
            int totalProducts = products.size();
            int hasProductImage = 0;
            int hasPrimaryImage = 0;
            int imagesSynced = 0;

            for (Map<String, Object> product : products) {
                String productTableImage = (String) product.get("product_table_image");
                String primaryImageUrl = (String) product.get("primary_image_url");

                if (productTableImage != null) hasProductImage++;
                if (primaryImageUrl != null) hasPrimaryImage++;
                if (productTableImage != null && productTableImage.equals(primaryImageUrl)) imagesSynced++;
            }

            // 3. 获取所有产品的图片统计
            String statsSql = "SELECT " +
                "COUNT(DISTINCT p.id) as total_products, " +
                "COUNT(DISTINCT CASE WHEN p.image_url IS NOT NULL THEN p.id END) as products_with_image, " +
                "COUNT(DISTINCT CASE WHEN pi.is_primary = 1 THEN p.id END) as products_with_primary " +
                "FROM products p " +
                "LEFT JOIN product_images pi ON p.id = pi.product_id";

            Map<String, Object> stats = jdbcTemplate.queryForMap(statsSql);

            result.put("products", products);
            result.put("totalProducts", totalProducts);
            result.put("hasProductImage", hasProductImage);
            result.put("hasPrimaryImage", hasPrimaryImage);
            result.put("imagesSynced", imagesSynced);
            result.put("syncRate", totalProducts > 0 ? (double) imagesSynced / totalProducts * 100 : 0);
            result.put("globalStats", stats);
            result.put("success", true);

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 修复用户表问题
     */
    @GetMapping("/fix-user-table")
    public Map<String, Object> fixUserTable() {
        Map<String, Object> result = new HashMap<>();
        List<String> operations = new ArrayList<>();
        List<String> errors = new ArrayList<>();

        try {
            // 1. 创建user表（如果不存在）
            String createUserTableSql = "CREATE TABLE IF NOT EXISTS `user` (" +
                "`id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID'," +
                "`user_id` VARCHAR(50) UNIQUE NOT NULL COMMENT '用户账号'," +
                "`username` VARCHAR(100) NOT NULL COMMENT '用户名'," +
                "`phone` VARCHAR(20) UNIQUE NOT NULL COMMENT '手机号'," +
                "`password` VARCHAR(255) NOT NULL COMMENT '密码'," +
                "`avatar` VARCHAR(255) DEFAULT '/images/avatar/default-avatar.jpg' COMMENT '用户头像'," +
                "`signature` VARCHAR(500) DEFAULT '' COMMENT '用户签名'," +
                "`register_time` BIGINT NOT NULL COMMENT '注册时间'," +
                "`created_time` BIGINT DEFAULT 0 COMMENT '创建时间'," +
                "`created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'," +
                "`updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'," +
                "INDEX `idx_phone` (`phone`)," +
                "INDEX `idx_user_id` (`user_id`)" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户基础信息表'";

            jdbcTemplate.execute(createUserTableSql);
            operations.add("创建user表成功");

            // 2. 检查是否有users表，如果有则迁移数据
            try {
                String checkUsersTableSql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'users'";
                Integer usersTableExists = jdbcTemplate.queryForObject(checkUsersTableSql, Integer.class);

                if (usersTableExists != null && usersTableExists > 0) {
                    String migrateSql = "INSERT IGNORE INTO `user` (`user_id`, `username`, `phone`, `password`, `register_time`, `created_time`) " +
                        "SELECT `user_id`, `username`, `phone`, `password`, `register_time`, `register_time` FROM `users`";
                    int migratedRows = jdbcTemplate.update(migrateSql);
                    operations.add("从users表迁移了 " + migratedRows + " 条数据");
                }
            } catch (Exception e) {
                operations.add("users表不存在，跳过数据迁移");
            }

            // 3. 插入测试用户数据（如果表为空）
            String countSql = "SELECT COUNT(*) FROM `user`";
            Integer userCount = jdbcTemplate.queryForObject(countSql, Integer.class);

            if (userCount == null || userCount == 0) {
                String insertTestDataSql = "INSERT INTO `user` (`user_id`, `username`, `phone`, `password`, `register_time`, `created_time`) VALUES " +
                    "('admin', '管理员', '15120248009', 'admin123', ?, ?), " +
                    "('user001', '小雯', '18823912577', '123456', ?, ?), " +
                    "('user002', 'KKKK', '18796247689', '123456', ?, ?), " +
                    "('user003', '测试用户1', '13800138001', '123456', ?, ?), " +
                    "('user004', '测试用户2', '13800138002', '123456', ?, ?)";

                long currentTime = System.currentTimeMillis();
                int insertedRows = jdbcTemplate.update(insertTestDataSql,
                    currentTime, currentTime, currentTime, currentTime, currentTime, currentTime,
                    currentTime, currentTime, currentTime, currentTime);
                operations.add("插入了 " + insertedRows + " 条测试用户数据");
            } else {
                operations.add("用户表已有 " + userCount + " 条数据，跳过测试数据插入");
            }

            // 4. 更新created_time字段
            String updateCreatedTimeSql = "UPDATE `user` SET `created_time` = `register_time` WHERE `created_time` = 0";
            int updatedRows = jdbcTemplate.update(updateCreatedTimeSql);
            if (updatedRows > 0) {
                operations.add("更新了 " + updatedRows + " 条记录的created_time字段");
            }

            // 5. 获取最终用户数量
            Integer finalUserCount = jdbcTemplate.queryForObject(countSql, Integer.class);
            operations.add("用户表修复完成，当前共有 " + finalUserCount + " 个用户");

            result.put("success", true);
            result.put("message", "用户表修复成功");
            result.put("operations", operations);

        } catch (Exception e) {
            e.printStackTrace();
            errors.add("修复用户表失败: " + e.getMessage());
            result.put("success", false);
            result.put("message", "用户表修复失败");
            result.put("errors", errors);
        }

        result.put("operations", operations);
        result.put("errors", errors);
        return result;
    }
}
