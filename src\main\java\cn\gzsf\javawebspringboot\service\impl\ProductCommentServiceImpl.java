package cn.gzsf.javawebspringboot.service.impl;

import cn.gzsf.javawebspringboot.entity.ProductComment;
import cn.gzsf.javawebspringboot.mapper.ProductCommentMapper;
import cn.gzsf.javawebspringboot.service.ProductCommentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 产品评论服务实现类
 */
@Service
public class ProductCommentServiceImpl implements ProductCommentService {
    
    @Autowired
    private ProductCommentMapper commentMapper;
    
    @Override
    public boolean addComment(ProductComment comment) {
        try {
            comment.setCreatedTime(System.currentTimeMillis());
            comment.setUpdatedTime(System.currentTimeMillis());
            comment.setStatus(0); // 默认待审核状态
            
            return commentMapper.addComment(comment) > 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    @Override
    public boolean updateCommentStatus(Long id, Integer status) {
        try {
            return commentMapper.updateCommentStatus(id, status) > 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    @Override
    public boolean deleteComment(Long id) {
        try {
            return commentMapper.deleteComment(id) > 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    @Override
    public Map<String, Object> getProductComments(Long productId, Integer status, int page, int size) {
        Map<String, Object> result = new HashMap<>();
        try {
            int offset = (page - 1) * size;
            List<ProductComment> comments = commentMapper.getProductComments(productId, status, offset, size);
            int total = commentMapper.getProductCommentCount(productId, status);
            
            result.put("success", true);
            result.put("data", comments);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取评论列表失败");
        }
        return result;
    }
    
    @Override
    public Map<String, Object> getUserComments(String userPhone, int page, int size) {
        Map<String, Object> result = new HashMap<>();
        try {
            int offset = (page - 1) * size;
            List<ProductComment> comments = commentMapper.getUserComments(userPhone, offset, size);
            int total = commentMapper.getUserCommentCount(userPhone);
            
            result.put("success", true);
            result.put("data", comments);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取评论列表失败");
        }
        return result;
    }
    
    @Override
    public Map<String, Object> getAllComments(Integer status, int page, int size) {
        Map<String, Object> result = new HashMap<>();
        try {
            int offset = (page - 1) * size;
            List<ProductComment> comments = commentMapper.getAllComments(status, offset, size);
            int total = commentMapper.getTotalCommentCount(status);
            
            result.put("success", true);
            result.put("data", comments);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取评论列表失败");
        }
        return result;
    }
    
    @Override
    public Double getProductAverageRating(Long productId) {
        try {
            Double rating = commentMapper.getProductAverageRating(productId);
            return rating != null ? rating : 0.0;
        } catch (Exception e) {
            e.printStackTrace();
            return 0.0;
        }
    }
    
    @Override
    public ProductComment getCommentById(Long id) {
        try {
            return commentMapper.getCommentById(id);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public Map<String, Object> getCommentStats() {
        Map<String, Object> stats = new HashMap<>();
        try {
            // 总评论数
            int total = commentMapper.getTotalCommentCount(null);

            // 待审核评论数
            int pending = commentMapper.getTotalCommentCount(0);

            // 已通过评论数
            int approved = commentMapper.getTotalCommentCount(1);

            // 已拒绝评论数
            int rejected = commentMapper.getTotalCommentCount(2);

            stats.put("total", total);
            stats.put("pending", pending);
            stats.put("approved", approved);
            stats.put("rejected", rejected);
        } catch (Exception e) {
            e.printStackTrace();
            stats.put("total", 0);
            stats.put("pending", 0);
            stats.put("approved", 0);
            stats.put("rejected", 0);
        }
        return stats;
    }
}
