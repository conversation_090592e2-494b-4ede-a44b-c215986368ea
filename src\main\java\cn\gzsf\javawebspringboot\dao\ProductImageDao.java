package cn.gzsf.javawebspringboot.dao;

import cn.gzsf.javawebspringboot.entity.ProductImage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品图片数据访问接口
 */
@Mapper
public interface ProductImageDao {
    
    /**
     * 根据产品ID查询所有图片
     * @param productId 产品ID
     * @return 图片列表
     */
    List<ProductImage> findByProductId(@Param("productId") Long productId);
    
    /**
     * 根据ID查询图片
     * @param id 图片ID
     * @return 图片信息
     */
    ProductImage findById(@Param("id") Long id);
    
    /**
     * 插入新图片
     * @param productImage 图片信息
     * @return 影响行数
     */
    int insert(ProductImage productImage);
    
    /**
     * 批量插入图片
     * @param productImages 图片列表
     * @return 影响行数
     */
    int insertBatch(@Param("list") List<ProductImage> productImages);
    
    /**
     * 更新图片信息
     * @param productImage 图片信息
     * @return 影响行数
     */
    int update(ProductImage productImage);
    
    /**
     * 删除图片
     * @param id 图片ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 根据产品ID删除所有图片
     * @param productId 产品ID
     * @return 影响行数
     */
    int deleteByProductId(@Param("productId") Long productId);
    
    /**
     * 设置主图
     * @param id 图片ID
     * @param productId 产品ID
     * @return 影响行数
     */
    int setPrimary(@Param("id") Long id, @Param("productId") Long productId);
    
    /**
     * 取消产品的所有主图设置
     * @param productId 产品ID
     * @return 影响行数
     */
    int clearPrimary(@Param("productId") Long productId);
    
    /**
     * 获取产品的主图
     * @param productId 产品ID
     * @return 主图信息
     */
    ProductImage findPrimaryByProductId(@Param("productId") Long productId);
    
    /**
     * 获取产品图片数量
     * @param productId 产品ID
     * @return 图片数量
     */
    int countByProductId(@Param("productId") Long productId);
}
