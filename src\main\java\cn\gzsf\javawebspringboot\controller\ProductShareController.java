package cn.gzsf.javawebspringboot.controller;

import cn.gzsf.javawebspringboot.service.ProductShareService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 产品分享控制器
 */
@RestController
@RequestMapping("/api/shares")
public class ProductShareController {
    
    @Autowired
    private ProductShareService shareService;
    
    /**
     * 添加分享记录
     */
    @PostMapping("/add")
    public Map<String, Object> addShare(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();
        try {
            String userPhone = (String) request.get("userPhone");
            Long productId = Long.valueOf(request.get("productId").toString());
            String shareType = (String) request.get("shareType");
            
            boolean success = shareService.addShare(userPhone, productId, shareType);
            if (success) {
                result.put("success", true);
                result.put("message", "分享记录添加成功");
            } else {
                result.put("success", false);
                result.put("message", "分享记录添加失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "分享失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取产品分享数量
     */
    @GetMapping("/count/{productId}")
    public Map<String, Object> getProductShareCount(@PathVariable Long productId) {
        Map<String, Object> result = new HashMap<>();
        try {
            int count = shareService.getProductShareCount(productId);
            result.put("success", true);
            result.put("count", count);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取分享数量失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取用户分享列表
     */
    @GetMapping("/user/{userPhone}")
    public Map<String, Object> getUserShares(@PathVariable String userPhone,
                                           @RequestParam(defaultValue = "1") int page,
                                           @RequestParam(defaultValue = "10") int size) {
        return shareService.getUserShares(userPhone, page, size);
    }
    
    /**
     * 获取热门分享产品
     */
    @GetMapping("/popular")
    public Map<String, Object> getPopularShares(@RequestParam(defaultValue = "10") int limit) {
        Map<String, Object> result = new HashMap<>();
        try {
            result.put("success", true);
            result.put("data", shareService.getPopularShares(limit));
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取热门分享失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取分享统计
     */
    @GetMapping("/stats")
    public Map<String, Object> getShareStats() {
        Map<String, Object> result = new HashMap<>();
        try {
            result.put("success", true);
            result.put("data", shareService.getShareStatsByType());
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取分享统计失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取所有分享记录（管理员用）
     */
    @GetMapping("/admin/all")
    public Map<String, Object> getAllShares(@RequestParam(defaultValue = "1") int page,
                                          @RequestParam(defaultValue = "10") int size) {
        return shareService.getAllShares(page, size);
    }

    /**
     * 删除分享记录（管理员用）
     */
    @DeleteMapping("/admin/{id}")
    public Map<String, Object> deleteShare(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = shareService.deleteShareById(id);
            if (success) {
                result.put("success", true);
                result.put("message", "删除成功");
            } else {
                result.put("success", false);
                result.put("message", "删除失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除失败：" + e.getMessage());
        }
        return result;
    }
}
