package cn.gzsf.javawebspringboot.controller;

import cn.gzsf.javawebspringboot.entity.SystemBackup;
import cn.gzsf.javawebspringboot.service.AdminBackupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 管理员备份管理控制器
 */
@RestController
@RequestMapping("/api/admin/backup")
@CrossOrigin(origins = "*")
public class AdminBackupController {

    @Autowired
    private AdminBackupService adminBackupService;

    /**
     * 获取备份统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getBackupStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            stats.put("total", adminBackupService.getTotalBackupCount());
            stats.put("latest", adminBackupService.getLatestBackupTime());
            stats.put("totalSize", adminBackupService.getTotalBackupSize());
            stats.put("autoEnabled", adminBackupService.isAutoBackupEnabled());

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", stats);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取备份统计失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 获取备份列表（分页）
     */
    @GetMapping("/list")
    public ResponseEntity<Map<String, Object>> getBackupList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("page", page);
            params.put("size", size);

            List<SystemBackup> backups = adminBackupService.getBackupsByPage(params);
            int total = adminBackupService.getTotalBackupCount();

            Map<String, Object> data = new HashMap<>();
            data.put("list", backups);
            data.put("total", total);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", data);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取备份列表失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 创建手动备份
     */
    @PostMapping("/create")
    public ResponseEntity<Map<String, Object>> createBackup(@RequestBody Map<String, Object> request) {
        try {
            String name = (String) request.get("name");
            String type = (String) request.get("type");
            
            if (name == null || name.trim().isEmpty()) {
                name = "手动备份_" + System.currentTimeMillis();
            }
            
            SystemBackup backup = adminBackupService.createBackup(name, type, "admin");
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "备份创建成功");
            response.put("data", backup);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "创建备份失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 更新自动备份设置
     */
    @PostMapping("/auto-settings")
    public ResponseEntity<Map<String, Object>> updateAutoBackupSettings(@RequestBody Map<String, Object> settings) {
        try {
            adminBackupService.updateAutoBackupSettings(settings);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "自动备份设置更新成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "更新设置失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 下载备份文件
     */
    @GetMapping("/download/{id}")
    public ResponseEntity<Map<String, Object>> downloadBackup(@PathVariable Long id) {
        try {
            // 这里应该实现文件下载逻辑
            // 暂时返回成功响应
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "备份下载功能开发中");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "下载备份失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 恢复备份
     */
    @PostMapping("/restore/{id}")
    public ResponseEntity<Map<String, Object>> restoreBackup(@PathVariable Long id) {
        try {
            adminBackupService.restoreBackup(id);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "备份恢复成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "恢复备份失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 删除备份
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteBackup(@PathVariable Long id) {
        try {
            adminBackupService.deleteBackup(id);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "备份删除成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "删除备份失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 清理旧备份
     */
    @PostMapping("/clean")
    public ResponseEntity<Map<String, Object>> cleanOldBackups() {
        try {
            int deletedCount = adminBackupService.cleanOldBackups();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "成功清理 " + deletedCount + " 个旧备份");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "清理备份失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }
}
