<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gzsf.javawebspringboot.dao.ProductCategoryDao">

    <!-- 根据产品ID获取分类ID列表 -->
    <select id="getCategoryIdsByProductId" resultType="java.lang.Long">
        SELECT category_id FROM product_category WHERE product_id = #{productId}
    </select>

    <!-- 根据分类ID获取产品ID列表 -->
    <select id="getProductIdsByCategoryId" resultType="java.lang.Long">
        SELECT product_id FROM product_category WHERE category_id = #{categoryId}
    </select>

    <!-- 添加产品分类关联 -->
    <insert id="insert" parameterType="cn.gzsf.javawebspringboot.entity.ProductCategory">
        INSERT INTO product_category (product_id, category_id, created_time)
        VALUES (#{productId}, #{categoryId}, #{createdTime})
    </insert>

    <!-- 批量添加产品分类关联 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO product_category (product_id, category_id, created_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.productId}, #{item.categoryId}, #{item.createdTime})
        </foreach>
    </insert>

    <!-- 删除产品的所有分类关联 -->
    <delete id="deleteByProductId">
        DELETE FROM product_category WHERE product_id = #{productId}
    </delete>

    <!-- 删除分类的所有产品关联 -->
    <delete id="deleteByCategoryId">
        DELETE FROM product_category WHERE category_id = #{categoryId}
    </delete>

    <!-- 删除特定的产品分类关联 -->
    <delete id="deleteByProductIdAndCategoryId">
        DELETE FROM product_category 
        WHERE product_id = #{productId} AND category_id = #{categoryId}
    </delete>

    <!-- 检查产品分类关联是否存在 -->
    <select id="existsByProductIdAndCategoryId" resultType="int">
        SELECT COUNT(*) FROM product_category 
        WHERE product_id = #{productId} AND category_id = #{categoryId}
    </select>

    <!-- 获取产品的分类信息（包含分类名称） -->
    <select id="getCategoryNamesByProductId" resultType="java.lang.String">
        SELECT c.name 
        FROM product_category pc 
        JOIN categories c ON pc.category_id = c.id 
        WHERE pc.product_id = #{productId}
    </select>

    <!-- 获取分类下的产品数量 -->
    <select id="getProductCountByCategoryId" resultType="int">
        SELECT COUNT(*) FROM product_category WHERE category_id = #{categoryId}
    </select>

</mapper>
