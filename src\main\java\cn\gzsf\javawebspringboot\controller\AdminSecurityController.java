package cn.gzsf.javawebspringboot.controller;

import cn.gzsf.javawebspringboot.entity.SecurityEvent;
import cn.gzsf.javawebspringboot.entity.IpManagement;
import cn.gzsf.javawebspringboot.service.AdminSecurityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 管理员安全中心控制器
 */
@RestController
@RequestMapping("/api/admin/security")
@CrossOrigin(origins = "*")
public class AdminSecurityController {

    @Autowired
    private AdminSecurityService adminSecurityService;

    /**
     * 获取安全统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getSecurityStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            stats.put("level", "高");
            stats.put("todayAttacks", adminSecurityService.getTodayAttackCount());
            stats.put("blacklistIPs", adminSecurityService.getBlacklistIPCount());
            stats.put("onlineUsers", adminSecurityService.getOnlineUserCount());

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", stats);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取安全统计失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 获取安全事件日志（分页）
     */
    @GetMapping("/logs")
    public ResponseEntity<Map<String, Object>> getSecurityLogs(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("page", page);
            params.put("size", size);

            List<SecurityEvent> logs = adminSecurityService.getSecurityEventsByPage(params);
            int total = adminSecurityService.getSecurityEventsCount();

            Map<String, Object> data = new HashMap<>();
            data.put("list", logs);
            data.put("total", total);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", data);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取安全日志失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 获取黑名单IP列表
     */
    @GetMapping("/blacklist")
    public ResponseEntity<Map<String, Object>> getBlacklistIPs() {
        try {
            List<IpManagement> blacklistIPs = adminSecurityService.getBlacklistIPs();

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", blacklistIPs);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取黑名单IP失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 获取白名单IP列表
     */
    @GetMapping("/whitelist")
    public ResponseEntity<Map<String, Object>> getWhitelistIPs() {
        try {
            List<IpManagement> whitelistIPs = adminSecurityService.getWhitelistIPs();

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", whitelistIPs);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取白名单IP失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 添加黑名单IP
     */
    @PostMapping("/blacklist")
    public ResponseEntity<Map<String, Object>> addBlacklistIP(@RequestBody IpManagement ipManagement) {
        try {
            ipManagement.setType("blacklist");
            ipManagement.setCreator("admin");
            adminSecurityService.addIPToList(ipManagement);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "IP已添加到黑名单");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "添加黑名单IP失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 添加白名单IP
     */
    @PostMapping("/whitelist")
    public ResponseEntity<Map<String, Object>> addWhitelistIP(@RequestBody IpManagement ipManagement) {
        try {
            ipManagement.setType("whitelist");
            ipManagement.setCreator("admin");
            adminSecurityService.addIPToList(ipManagement);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "IP已添加到白名单");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "添加白名单IP失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 从黑名单移除IP
     */
    @DeleteMapping("/blacklist/{id}")
    public ResponseEntity<Map<String, Object>> removeFromBlacklist(@PathVariable Long id) {
        try {
            adminSecurityService.removeIPFromList(id);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "IP已从黑名单移除");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "移除黑名单IP失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 从白名单移除IP
     */
    @DeleteMapping("/whitelist/{id}")
    public ResponseEntity<Map<String, Object>> removeFromWhitelist(@PathVariable Long id) {
        try {
            adminSecurityService.removeIPFromList(id);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "IP已从白名单移除");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "移除白名单IP失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 处理安全事件
     */
    @PostMapping("/events/{id}/handle")
    public ResponseEntity<Map<String, Object>> handleSecurityEvent(@PathVariable Long id) {
        try {
            adminSecurityService.handleSecurityEvent(id, "admin");

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "安全事件处理成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "处理安全事件失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 清理安全日志
     */
    @PostMapping("/logs/clear")
    public ResponseEntity<Map<String, Object>> clearSecurityLogs() {
        try {
            int deletedCount = adminSecurityService.clearOldSecurityEvents();

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "成功清理 " + deletedCount + " 条安全日志");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "清理安全日志失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 更新安全设置
     */
    @PostMapping("/settings")
    public ResponseEntity<Map<String, Object>> updateSecuritySettings(@RequestBody Map<String, Object> settings) {
        try {
            adminSecurityService.updateSecuritySettings(settings);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "安全设置更新成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "更新安全设置失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }
}
