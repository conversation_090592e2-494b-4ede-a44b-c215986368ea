package cn.gzsf.javawebspringboot.service.impl;

import cn.gzsf.javawebspringboot.entity.SystemLog;
import cn.gzsf.javawebspringboot.service.AdminLogService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 管理员日志服务实现类
 * 注意：这是一个简化的内存实现，生产环境应该使用数据库
 */
@Service
public class AdminLogServiceImpl implements AdminLogService {
    
    // 使用内存存储，生产环境应该使用数据库
    private final Map<Long, SystemLog> logStorage = new ConcurrentHashMap<>();
    private final AtomicLong idGenerator = new AtomicLong(1);
    
    public AdminLogServiceImpl() {
        // 初始化一些示例数据
        initSampleData();
    }
    
    private void initSampleData() {
        addLog(new SystemLog("INFO", "LOGIN", "admin", "管理员登录系统", "127.0.0.1"));
        addLog(new SystemLog("INFO", "CREATE", "admin", "新增产品：美白精华液", "127.0.0.1"));
        addLog(new SystemLog("WARN", "UPDATE", "admin", "修改用户信息", "127.0.0.1"));
        addLog(new SystemLog("ERROR", "DELETE", "admin", "删除操作失败", "127.0.0.1"));
        addLog(new SystemLog("INFO", "SELECT", "admin", "查询用户列表", "127.0.0.1"));
    }

    @Override
    public int countLogsByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return (int) logStorage.values().stream()
                .filter(log -> log.getTime().isAfter(startTime) && log.getTime().isBefore(endTime))
                .count();
    }

    @Override
    public int countLogsByLevel(String level) {
        return (int) logStorage.values().stream()
                .filter(log -> level.equals(log.getLevel()))
                .count();
    }

    @Override
    public List<SystemLog> getLogsByPage(Map<String, Object> params) {
        int page = (Integer) params.get("page");
        int size = (Integer) params.get("size");
        String level = (String) params.get("level");
        String action = (String) params.get("action");
        String keyword = (String) params.get("keyword");
        
        List<SystemLog> filteredLogs = logStorage.values().stream()
                .filter(log -> level == null || level.isEmpty() || level.equals(log.getLevel()))
                .filter(log -> action == null || action.isEmpty() || action.equals(log.getAction()))
                .filter(log -> keyword == null || keyword.isEmpty() || 
                        log.getDescription().contains(keyword) || 
                        log.getUser().contains(keyword) || 
                        log.getIp().contains(keyword))
                .sorted((a, b) -> b.getTime().compareTo(a.getTime())) // 按时间倒序
                .collect(Collectors.toList());
        
        int start = (page - 1) * size;
        int end = Math.min(start + size, filteredLogs.size());
        
        if (start >= filteredLogs.size()) {
            return new ArrayList<>();
        }
        
        return filteredLogs.subList(start, end);
    }

    @Override
    public int countLogs(Map<String, Object> params) {
        String level = (String) params.get("level");
        String action = (String) params.get("action");
        String keyword = (String) params.get("keyword");
        
        return (int) logStorage.values().stream()
                .filter(log -> level == null || level.isEmpty() || level.equals(log.getLevel()))
                .filter(log -> action == null || action.isEmpty() || action.equals(log.getAction()))
                .filter(log -> keyword == null || keyword.isEmpty() || 
                        log.getDescription().contains(keyword) || 
                        log.getUser().contains(keyword) || 
                        log.getIp().contains(keyword))
                .count();
    }

    @Override
    public void deleteLog(Long id) {
        logStorage.remove(id);
    }

    @Override
    public int clearLogsBefore(LocalDateTime cutoffTime) {
        List<Long> toDelete = logStorage.values().stream()
                .filter(log -> log.getTime().isBefore(cutoffTime))
                .map(SystemLog::getId)
                .collect(Collectors.toList());
        
        toDelete.forEach(logStorage::remove);
        return toDelete.size();
    }

    @Override
    public void addLog(SystemLog log) {
        if (log.getId() == null) {
            log.setId(idGenerator.getAndIncrement());
        }
        if (log.getTime() == null) {
            log.setTime(LocalDateTime.now());
        }
        logStorage.put(log.getId(), log);
    }

    @Override
    public void logOperation(String level, String action, String user, String description, String ip) {
        SystemLog log = new SystemLog(level, action, user, description, ip);
        addLog(log);
    }
}
