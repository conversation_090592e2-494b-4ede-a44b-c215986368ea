-- Admin后台管理系统新功能数据表初始化脚本

-- 1. 系统日志表
CREATE TABLE IF NOT EXISTS system_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    level VARCHAR(10) NOT NULL COMMENT '日志级别：INFO, WARN, ERROR, DEBUG',
    action VARCHAR(20) NOT NULL COMMENT '操作类型：LOGIN, CREATE, UPDATE, DELETE, SELECT',
    user VARCHAR(50) NOT NULL COMMENT '操作用户',
    description TEXT NOT NULL COMMENT '操作描述',
    ip VARCHAR(45) NOT NULL COMMENT '操作IP地址',
    user_agent TEXT COMMENT '用户代理',
    time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    details JSON COMMENT '详细信息（JSON格式）',
    INDEX idx_level (level),
    INDEX idx_action (action),
    INDEX idx_user (user),
    INDEX idx_time (time),
    INDEX idx_ip (ip)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统操作日志表';

-- 2. 系统备份表
CREATE TABLE IF NOT EXISTS system_backups (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '备份ID',
    name VARCHAR(200) NOT NULL COMMENT '备份名称',
    type VARCHAR(20) NOT NULL COMMENT '备份类型：full, data, structure',
    file_path VARCHAR(500) NOT NULL COMMENT '备份文件路径',
    file_size BIGINT DEFAULT 0 COMMENT '文件大小（字节）',
    status VARCHAR(20) NOT NULL DEFAULT 'processing' COMMENT '备份状态：success, failed, processing',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    description TEXT COMMENT '备份描述',
    creator VARCHAR(50) NOT NULL COMMENT '创建者',
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time),
    INDEX idx_creator (creator)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统备份表';

-- 3. 安全事件表
CREATE TABLE IF NOT EXISTS security_events (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '事件ID',
    type VARCHAR(50) NOT NULL COMMENT '事件类型：LOGIN_FAIL, SQL_INJECTION, XSS_ATTACK, BRUTE_FORCE, SUSPICIOUS_ACCESS',
    ip VARCHAR(45) NOT NULL COMMENT '来源IP',
    description TEXT NOT NULL COMMENT '事件描述',
    severity VARCHAR(10) NOT NULL COMMENT '严重程度：高, 中, 低',
    status VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '处理状态：pending, handled',
    time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发生时间',
    user_agent TEXT COMMENT '用户代理',
    details JSON COMMENT '详细信息（JSON格式）',
    handler VARCHAR(50) COMMENT '处理人',
    handle_time DATETIME COMMENT '处理时间',
    INDEX idx_type (type),
    INDEX idx_ip (ip),
    INDEX idx_severity (severity),
    INDEX idx_status (status),
    INDEX idx_time (time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='安全事件表';

-- 4. IP管理表
CREATE TABLE IF NOT EXISTS ip_management (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'IP管理ID',
    ip VARCHAR(45) NOT NULL COMMENT 'IP地址',
    type VARCHAR(20) NOT NULL COMMENT '类型：blacklist, whitelist',
    reason TEXT COMMENT '原因/描述',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    expire_time DATETIME COMMENT '到期时间（可选）',
    creator VARCHAR(50) NOT NULL COMMENT '创建者',
    status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '状态：active, expired, disabled',
    INDEX idx_ip (ip),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time),
    INDEX idx_expire_time (expire_time),
    UNIQUE KEY uk_ip_type (ip, type, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='IP管理表';

-- 5. 自动备份配置表
CREATE TABLE IF NOT EXISTS auto_backup_config (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    enabled BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否启用自动备份',
    frequency VARCHAR(20) NOT NULL DEFAULT 'daily' COMMENT '备份频率：daily, weekly, monthly',
    backup_time TIME NOT NULL DEFAULT '02:00:00' COMMENT '备份时间',
    retention_days INT NOT NULL DEFAULT 30 COMMENT '保留天数',
    backup_type VARCHAR(20) NOT NULL DEFAULT 'full' COMMENT '备份类型',
    last_backup_time DATETIME COMMENT '最后备份时间',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='自动备份配置表';

-- 6. 安全配置表
CREATE TABLE IF NOT EXISTS security_config (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type VARCHAR(20) NOT NULL DEFAULT 'string' COMMENT '配置类型：string, number, boolean',
    description TEXT COMMENT '配置描述',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_config_key (config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='安全配置表';

-- 插入默认的自动备份配置
INSERT IGNORE INTO auto_backup_config (enabled, frequency, backup_time, retention_days, backup_type) 
VALUES (FALSE, 'daily', '02:00:00', 30, 'full');

-- 插入默认的安全配置
INSERT IGNORE INTO security_config (config_key, config_value, config_type, description) VALUES
('password_min_length', '8', 'number', '密码最小长度'),
('password_require_special', 'false', 'boolean', '密码是否需要特殊字符'),
('login_max_attempts', '5', 'number', '登录最大尝试次数'),
('session_timeout', '30', 'number', '会话超时时间（分钟）'),
('enable_captcha', 'true', 'boolean', '是否启用验证码'),
('rate_limit_enabled', 'true', 'boolean', 'API限流是否启用'),
('requests_per_minute', '100', 'number', '每分钟请求数限制'),
('ip_whitelist_enabled', 'false', 'boolean', 'IP白名单是否启用'),
('cors_enabled', 'true', 'boolean', 'CORS跨域是否启用'),
('sql_injection_protection', 'true', 'boolean', 'SQL注入防护是否启用');

-- 插入一些示例数据

-- 示例系统日志
INSERT IGNORE INTO system_logs (level, action, user, description, ip, time) VALUES
('INFO', 'LOGIN', 'admin', '管理员登录系统', '127.0.0.1', NOW()),
('INFO', 'CREATE', 'admin', '新增产品：美白精华液', '127.0.0.1', NOW() - INTERVAL 1 HOUR),
('WARN', 'UPDATE', 'admin', '修改用户信息', '127.0.0.1', NOW() - INTERVAL 2 HOUR),
('ERROR', 'DELETE', 'admin', '删除操作失败', '127.0.0.1', NOW() - INTERVAL 3 HOUR),
('INFO', 'SELECT', 'admin', '查询用户列表', '127.0.0.1', NOW() - INTERVAL 4 HOUR);

-- 示例备份记录
INSERT IGNORE INTO system_backups (name, type, file_path, file_size, status, creator, create_time) VALUES
('自动备份_20241217', 'full', '/backups/auto_20241217.sql', 52428800, 'success', 'system', NOW() - INTERVAL 1 DAY),
('手动备份_测试', 'data', '/backups/manual_test.sql', 31457280, 'success', 'admin', NOW() - INTERVAL 6 HOUR);

-- 示例安全事件
INSERT IGNORE INTO security_events (type, ip, description, severity, time) VALUES
('LOGIN_FAIL', '*************', '连续登录失败5次', '中', NOW() - INTERVAL 2 HOUR),
('SQL_INJECTION', '*********', '检测到SQL注入攻击', '高', NOW() - INTERVAL 30 MINUTE),
('SUSPICIOUS_ACCESS', '***********', '异常访问模式', '低', NOW() - INTERVAL 15 MINUTE);

-- 示例IP管理
INSERT IGNORE INTO ip_management (ip, type, reason, creator) VALUES
('*************', 'blacklist', '多次恶意登录尝试', 'admin'),
('127.0.0.1', 'whitelist', '本地管理IP', 'admin');

-- 创建视图：日志统计视图
CREATE OR REPLACE VIEW log_stats_view AS
SELECT 
    DATE(time) as log_date,
    level,
    COUNT(*) as count
FROM system_logs 
WHERE time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(time), level;

-- 创建视图：安全统计视图
CREATE OR REPLACE VIEW security_stats_view AS
SELECT 
    DATE(time) as event_date,
    type,
    severity,
    COUNT(*) as count
FROM security_events 
WHERE time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(time), type, severity;

-- 创建存储过程：清理旧日志
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS CleanOldLogs(IN days_to_keep INT)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    DELETE FROM system_logs WHERE time < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    DELETE FROM security_events WHERE time < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    COMMIT;
END //
DELIMITER ;

-- 创建存储过程：清理旧备份
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS CleanOldBackups(IN days_to_keep INT)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    DELETE FROM system_backups WHERE create_time < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    COMMIT;
END //
DELIMITER ;

-- 创建触发器：自动更新IP管理状态
DELIMITER //
CREATE TRIGGER IF NOT EXISTS update_ip_status_on_expire
BEFORE UPDATE ON ip_management
FOR EACH ROW
BEGIN
    IF NEW.expire_time IS NOT NULL AND NEW.expire_time <= NOW() THEN
        SET NEW.status = 'expired';
    END IF;
END //
DELIMITER ;

-- 创建事件：定期清理过期IP
SET GLOBAL event_scheduler = ON;

CREATE EVENT IF NOT EXISTS cleanup_expired_ips
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
UPDATE ip_management 
SET status = 'expired' 
WHERE expire_time IS NOT NULL 
  AND expire_time <= NOW() 
  AND status = 'active';

COMMIT;
