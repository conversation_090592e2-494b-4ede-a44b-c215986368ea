package cn.gzsf.javawebspringboot.controller;

import cn.gzsf.javawebspringboot.service.ProductFavoriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 产品收藏控制器
 */
@RestController
@RequestMapping("/api/favorites")
public class ProductFavoriteController {
    
    @Autowired
    private ProductFavoriteService favoriteService;
    
    /**
     * 添加收藏
     */
    @PostMapping("/add")
    public Map<String, Object> addFavorite(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();
        try {
            String userPhone = (String) request.get("userPhone");
            Long productId = Long.valueOf(request.get("productId").toString());
            
            boolean success = favoriteService.addFavorite(userPhone, productId);
            if (success) {
                result.put("success", true);
                result.put("message", "收藏成功");
            } else {
                result.put("success", false);
                result.put("message", "已收藏或收藏失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "收藏失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 取消收藏
     */
    @PostMapping("/remove")
    public Map<String, Object> removeFavorite(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();
        try {
            String userPhone = (String) request.get("userPhone");
            Long productId = Long.valueOf(request.get("productId").toString());
            
            boolean success = favoriteService.removeFavorite(userPhone, productId);
            if (success) {
                result.put("success", true);
                result.put("message", "取消收藏成功");
            } else {
                result.put("success", false);
                result.put("message", "取消收藏失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "取消收藏失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 检查是否已收藏
     */
    @GetMapping("/check")
    public Map<String, Object> checkFavorite(@RequestParam String userPhone, @RequestParam Long productId) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean isFavorited = favoriteService.isFavorited(userPhone, productId);
            result.put("success", true);
            result.put("isFavorited", isFavorited);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "检查收藏状态失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取用户收藏列表
     */
    @GetMapping("/user/{userPhone}")
    public Map<String, Object> getUserFavorites(@PathVariable String userPhone,
                                              @RequestParam(defaultValue = "1") int page,
                                              @RequestParam(defaultValue = "10") int size) {
        return favoriteService.getUserFavorites(userPhone, page, size);
    }
    
    /**
     * 获取产品收藏数量
     */
    @GetMapping("/count/{productId}")
    public Map<String, Object> getProductFavoriteCount(@PathVariable Long productId) {
        Map<String, Object> result = new HashMap<>();
        try {
            int count = favoriteService.getProductFavoriteCount(productId);
            result.put("success", true);
            result.put("count", count);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取收藏数量失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取热门收藏产品
     */
    @GetMapping("/popular")
    public Map<String, Object> getPopularFavorites(@RequestParam(defaultValue = "10") int limit) {
        Map<String, Object> result = new HashMap<>();
        try {
            result.put("success", true);
            result.put("data", favoriteService.getPopularFavorites(limit));
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取热门收藏失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取所有收藏记录（管理员用）
     */
    @GetMapping("/admin/all")
    public Map<String, Object> getAllFavorites(@RequestParam(defaultValue = "1") int page,
                                             @RequestParam(defaultValue = "10") int size) {
        return favoriteService.getAllFavorites(page, size);
    }

    /**
     * 获取收藏统计（管理员用）
     */
    @GetMapping("/admin/stats")
    public Map<String, Object> getFavoriteStats() {
        Map<String, Object> result = new HashMap<>();
        try {
            result.put("success", true);
            result.put("data", favoriteService.getFavoriteStats());
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取收藏统计失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 删除收藏记录（管理员用）
     */
    @DeleteMapping("/admin/{id}")
    public Map<String, Object> deleteFavorite(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = favoriteService.deleteFavoriteById(id);
            if (success) {
                result.put("success", true);
                result.put("message", "删除成功");
            } else {
                result.put("success", false);
                result.put("message", "删除失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除失败：" + e.getMessage());
        }
        return result;
    }
}
