package cn.gzsf.javawebspringboot.entity;

import java.time.LocalDateTime;

/**
 * IP管理实体类
 */
public class IpManagement {
    private Long id;
    private String ip;              // IP地址
    private String type;            // 类型：blacklist, whitelist
    private String reason;          // 原因/描述
    private LocalDateTime createTime; // 创建时间
    private LocalDateTime expireTime; // 到期时间（可选）
    private String creator;         // 创建者
    private String status;          // 状态：active, expired, disabled

    public IpManagement() {}

    public IpManagement(String ip, String type, String reason, String creator) {
        this.ip = ip;
        this.type = type;
        this.reason = reason;
        this.creator = creator;
        this.createTime = LocalDateTime.now();
        this.status = "active";
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(LocalDateTime expireTime) {
        this.expireTime = expireTime;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "IpManagement{" +
                "id=" + id +
                ", ip='" + ip + '\'' +
                ", type='" + type + '\'' +
                ", reason='" + reason + '\'' +
                ", createTime=" + createTime +
                ", expireTime=" + expireTime +
                ", creator='" + creator + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
