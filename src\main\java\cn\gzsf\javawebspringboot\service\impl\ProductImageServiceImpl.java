package cn.gzsf.javawebspringboot.service.impl;

import cn.gzsf.javawebspringboot.dao.ProductImageDao;
import cn.gzsf.javawebspringboot.entity.ProductImage;
import cn.gzsf.javawebspringboot.service.ProductImageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 产品图片服务实现类
 */
@Service
public class ProductImageServiceImpl implements ProductImageService {
    
    @Autowired
    private ProductImageDao productImageDao;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Value("${file.upload.dir}")
    private String uploadDir;
    
    @Override
    public List<ProductImage> getImagesByProductId(Long productId) {
        return productImageDao.findByProductId(productId);
    }
    
    @Override
    public ProductImage getImageById(Long id) {
        return productImageDao.findById(id);
    }
    
    @Override
    public boolean addImage(ProductImage productImage) {
        if (productImage.getCreatedTime() == null) {
            productImage.setCreatedTime(System.currentTimeMillis());
        }
        if (productImage.getUpdatedTime() == null) {
            productImage.setUpdatedTime(System.currentTimeMillis());
        }

        int result = productImageDao.insert(productImage);

        // 🔥 关键修复：如果是主图，同步更新products表的image_url字段
        if (result > 0 && Boolean.TRUE.equals(productImage.getIsPrimary())) {
            try {
                String updateProductSql = "UPDATE products SET image_url = ? WHERE id = ?";
                jdbcTemplate.update(updateProductSql, productImage.getImageUrl(), productImage.getProductId());

                System.out.println("✅ 成功同步更新产品 " + productImage.getProductId() + " 的封面图片: " + productImage.getImageUrl());
            } catch (Exception e) {
                System.err.println("❌ 同步更新products表失败: " + e.getMessage());
                e.printStackTrace();
            }
        }

        return result > 0;
    }
    
    @Override
    @Transactional
    public boolean addImages(List<ProductImage> productImages) {
        if (productImages == null || productImages.isEmpty()) {
            return false;
        }
        
        long currentTime = System.currentTimeMillis();
        for (int i = 0; i < productImages.size(); i++) {
            ProductImage image = productImages.get(i);
            if (image.getCreatedTime() == null) {
                image.setCreatedTime(currentTime);
            }
            if (image.getUpdatedTime() == null) {
                image.setUpdatedTime(currentTime);
            }
            if (image.getSortOrder() == null) {
                image.setSortOrder(i);
            }
        }
        
        int result = productImageDao.insertBatch(productImages);
        return result > 0;
    }
    
    @Override
    @Transactional
    public ProductImage uploadImage(Long productId, MultipartFile file, Boolean isPrimary) {
        if (file.isEmpty()) {
            return null;
        }

        // 添加重试机制处理死锁
        int maxRetries = 3;
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return uploadImageInternal(productId, file, isPrimary);
            } catch (Exception e) {
                if (e.getMessage() != null && e.getMessage().contains("Deadlock")) {
                    System.out.println("⚠️ 检测到死锁，第 " + attempt + " 次重试...");
                    if (attempt < maxRetries) {
                        try {
                            // 等待随机时间后重试，避免重复冲突
                            Thread.sleep(100 + (int)(Math.random() * 200));
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                        continue;
                    }
                }
                System.err.println("❌ 图片上传失败: " + e.getMessage());
                e.printStackTrace();
                break;
            }
        }

        return null;
    }

    /**
     * 内部上传图片方法
     */
    private ProductImage uploadImageInternal(Long productId, MultipartFile file, Boolean isPrimary) throws IOException {
        // 生成唯一文件名
        String originalFilename = file.getOriginalFilename();
        String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        String filename = UUID.randomUUID().toString() + extension;

        // 获取绝对路径
        String currentDir = System.getProperty("user.dir");
        String absoluteUploadDir = currentDir + "/" + uploadDir;

        // 确保上传目录存在
        File uploadDirFile = new File(absoluteUploadDir);
        if (!uploadDirFile.exists()) {
            boolean created = uploadDirFile.mkdirs();
            System.out.println("📁 创建上传目录: " + (created ? "成功" : "失败") + " - " + absoluteUploadDir);
        }

        // 保存文件
        File destFile = new File(uploadDirFile, filename);
        file.transferTo(destFile);

        System.out.println("📸 文件保存成功: " + destFile.getAbsolutePath());

        // 生成访问URL
        String imageUrl = "/images/" + filename;

        // 创建图片记录
        ProductImage productImage = new ProductImage();
        productImage.setProductId(productId);
        productImage.setImageUrl(imageUrl);
        productImage.setImageName(originalFilename);
        productImage.setIsPrimary(isPrimary != null ? isPrimary : false);
        productImage.setSortOrder(0);

        // 如果设置为主图，先清除其他主图
        if (Boolean.TRUE.equals(isPrimary)) {
            productImageDao.clearPrimary(productId);
        }

        // 保存到数据库
        if (addImage(productImage)) {
            return productImage;
        }

        return null;
    }
    
    @Override
    @Transactional
    public List<ProductImage> uploadImages(Long productId, MultipartFile[] files) {
        List<ProductImage> uploadedImages = new ArrayList<>();

        if (files == null || files.length == 0) {
            return uploadedImages;
        }

        // 先清除主图设置，避免在循环中重复清除导致死锁
        boolean hasPrimaryImage = false;
        for (MultipartFile file : files) {
            if (!file.isEmpty()) {
                hasPrimaryImage = true;
                break;
            }
        }

        if (hasPrimaryImage) {
            productImageDao.clearPrimary(productId);
        }

        for (int i = 0; i < files.length; i++) {
            MultipartFile file = files[i];
            if (!file.isEmpty()) {
                // 第一张图片设为主图
                boolean isPrimary = (i == 0);
                ProductImage uploadedImage = uploadImageWithoutClearPrimary(productId, file, isPrimary);
                if (uploadedImage != null) {
                    uploadedImages.add(uploadedImage);
                }
            }
        }

        return uploadedImages;
    }

    /**
     * 上传图片但不清除主图设置（用于批量上传）
     */
    private ProductImage uploadImageWithoutClearPrimary(Long productId, MultipartFile file, Boolean isPrimary) {
        if (file.isEmpty()) {
            return null;
        }

        try {
            // 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String filename = UUID.randomUUID().toString() + extension;

            // 获取绝对路径
            String currentDir = System.getProperty("user.dir");
            String absoluteUploadDir = currentDir + "/" + uploadDir;

            // 确保上传目录存在
            File uploadDirFile = new File(absoluteUploadDir);
            if (!uploadDirFile.exists()) {
                boolean created = uploadDirFile.mkdirs();
                System.out.println("📁 创建上传目录: " + (created ? "成功" : "失败") + " - " + absoluteUploadDir);
            }

            // 保存文件
            File destFile = new File(uploadDirFile, filename);
            file.transferTo(destFile);

            System.out.println("📸 批量文件保存成功: " + destFile.getAbsolutePath());

            // 生成访问URL
            String imageUrl = "/images/" + filename;

            // 创建图片记录
            ProductImage productImage = new ProductImage();
            productImage.setProductId(productId);
            productImage.setImageUrl(imageUrl);
            productImage.setImageName(originalFilename);
            productImage.setIsPrimary(isPrimary != null ? isPrimary : false);
            productImage.setSortOrder(0);

            // 注意：这里不清除主图设置，由调用方统一处理

            // 保存到数据库
            if (addImage(productImage)) {
                return productImage;
            }

        } catch (IOException e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public boolean updateImage(ProductImage productImage) {
        productImage.setUpdatedTime(System.currentTimeMillis());
        int result = productImageDao.update(productImage);
        return result > 0;
    }
    
    @Override
    public boolean deleteImage(Long id) {
        int result = productImageDao.deleteById(id);
        return result > 0;
    }
    
    @Override
    public boolean deleteImagesByProductId(Long productId) {
        int result = productImageDao.deleteByProductId(productId);
        return result > 0;
    }
    
    @Override
    @Transactional
    public boolean setPrimaryImage(Long id) {
        ProductImage image = productImageDao.findById(id);
        if (image == null) {
            return false;
        }

        // 先清除该产品的所有主图设置，再设置新的主图
        productImageDao.clearPrimary(image.getProductId());

        image.setIsPrimary(true);
        image.setUpdatedTime(System.currentTimeMillis());

        int result = productImageDao.update(image);

        // 🔥 关键修复：同步更新products表的image_url字段
        if (result > 0) {
            try {
                // 使用JdbcTemplate直接更新products表，确保产品列表显示正确的封面
                String updateProductSql = "UPDATE products SET image_url = ? WHERE id = ?";
                jdbcTemplate.update(updateProductSql, image.getImageUrl(), image.getProductId());

                System.out.println("✅ 成功同步更新产品 " + image.getProductId() + " 的封面图片: " + image.getImageUrl());
            } catch (Exception e) {
                System.err.println("❌ 同步更新products表失败: " + e.getMessage());
                e.printStackTrace();
            }
        }

        return result > 0;
    }
    
    @Override
    public ProductImage getPrimaryImage(Long productId) {
        return productImageDao.findPrimaryByProductId(productId);
    }
    
    @Override
    public int getImageCount(Long productId) {
        return productImageDao.countByProductId(productId);
    }

    @Override
    public boolean clearPrimaryImages(Long productId) {
        try {
            productImageDao.clearPrimary(productId);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
