// 检查依赖是否加载成功
function checkDependencies() {
    const missing = [];

    if (typeof Vue === 'undefined') {
        missing.push('Vue.js');
    }
    if (typeof axios === 'undefined') {
        missing.push('Axios');
    }
    if (typeof ELEMENT === 'undefined') {
        missing.push('Element UI');
    }

    if (missing.length > 0) {
        console.error('以下依赖加载失败:', missing.join(', '));
        document.body.innerHTML = `
            <div style="display: flex; justify-content: center; align-items: center; height: 100vh; flex-direction: column; font-family: Arial, sans-serif;">
                <div style="text-align: center; padding: 40px; border: 2px solid #f56565; border-radius: 10px; background: #fed7d7; color: #c53030; max-width: 500px;">
                    <h2 style="margin-bottom: 20px;">⚠️ 资源加载失败</h2>
                    <p style="margin-bottom: 15px;">以下依赖库加载失败：</p>
                    <ul style="text-align: left; margin-bottom: 20px;">
                        ${missing.map(dep => `<li>${dep}</li>`).join('')}
                    </ul>
                    <p style="margin-bottom: 20px;">可能的原因：</p>
                    <ul style="text-align: left; margin-bottom: 20px;">
                        <li>网络连接问题</li>
                        <li>CDN服务不可用</li>
                        <li>防火墙或代理设置</li>
                    </ul>
                    <button onclick="location.reload()" style="padding: 10px 20px; background: #3182ce; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        🔄 重新加载页面
                    </button>
                </div>
            </div>
        `;
        return false;
    }
    return true;
}

// 等待依赖加载完成后初始化Vue应用
setTimeout(() => {
    if (!checkDependencies()) {
        return;
    }

    // 依赖加载成功，初始化Vue应用
    new Vue({
    el: '#app',
    data: {
        // 下拉菜单显示状态
        dropdownVisible: false,
        // 初始激活仪表盘页面
        activeTab: 'dashboard',
        // 用户列表
        userList: [],
        // 对话框显示状态
        dialogVisible: false,
        // 表单数据
        formData: {
            userId: '',
            username: '',
            phone: '',
            password: ''
        },
        // 搜索表单数据
        searchForm: {
            userId: '',
            username: ''
        },
        // 用户分页信息
        userPagination: {
            currentPage: 1,
            pageSize: 10,
            total: 0
        },
        // 产品分页信息
        productPagination: {
            currentPage: 1,
            pageSize: 10,
            total: 0
        },
        // 分类分页信息
        categoryPagination: {
            currentPage: 1,
            pageSize: 10,
            total: 0
        },
        // 表单验证规则
        rules: {
            username: [
                { required: true, message: '请输入用户名', trigger: 'blur' }
            ],
            phone: [
                { required: true, message: '请输入手机号', trigger: 'blur' },
                { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
            ],
            password: [
                { required: true, message: '请输入密码', trigger: 'blur' }
            ]
        },
        // 产品列表
        productList: [],
        // 分类列表
        categoryList: [],
        // 编辑产品表单数据
        editProductForm: {
            name: '',
            description: '',
            price: 0,
            stock: 0,
            imageUrl: '',
            isNew: false,
            categoryIds: []
        },
        // 编辑产品对话框显示状态
        editProductDialogVisible: false,

        // 新增用户数据
        newUser: {
            userId: '',
            username: '',
            phone: '',
            password: '',
            registerTime: ''
        },
        addUserInfo: {
            username: '',
            password: '',
            email: '',
            phone: '',
            role: '',
            registerTime: null, // 确保registerTime属性存在
        },
        // 新增产品数据
        newProduct: {
            name: '',
            description: '',
            price: 0,
            stock: 0,
            imageUrl: '',
            isNew: false,
            categoryIds: []
        },
        // 新增分类数据
        newCategory: {
            name: ''
        },
        // 新增用户对话框显示状态
        addUserDialogVisible: false,
        // 新增产品对话框显示状态
        addProductDialogVisible: false,
        // 新增分类对话框显示状态
        addCategoryDialogVisible: false,
        // 编辑分类对话框显示状态
        editCategoryDialogVisible: false,
        editCategoryForm: {
            name: ''
        },
        // 销售图表实例
        salesChart: null,
        // 分类图表实例
        categoryChart: null,
        // 编辑表单数据
        editForm: {
            userId: '',
            username: '',
            phone: '',
            password: ''
        },
        // 新增属性，初始显示所有面板
        activePanel: 'all',
        // 用户详情对话框显示状态
        userDetailDialogVisible: false,
        // 当前查看的用户详情
        currentUserDetail: null,
        // 编辑用户表单数据
        editForm: {
            userId: '',
            username: '',
            phone: '',
            password: '',
            registerTime: ''
        },
        // 控制密码显示/隐藏
        showPassword: false,

        // 多图片管理相关数据
        newProductImages: [],        // 新增产品的图片数组
        editProductImages: [],       // 编辑产品的图片数组

        // 轮播图管理相关数据
        carouselList: [],            // 轮播图列表
        addCarouselDialogVisible: false,  // 新增轮播图对话框显示状态
        editCarouselDialogVisible: false, // 编辑轮播图对话框显示状态
        newCarousel: {               // 新增轮播图数据
            title: '',
            description: '',
            imageUrl: '',
            categoryId: null,
            sortOrder: 0
        },
        editCarouselForm: {          // 编辑轮播图表单数据
            id: null,
            title: '',
            description: '',
            imageUrl: '',
            categoryId: null,
            sortOrder: 0
        },

        // 订单管理相关数据
        orderList: [],               // 订单列表
        orderLoading: false,         // 订单加载状态
        orderCurrentPage: 1,         // 订单当前页
        orderPageSize: 10,           // 订单每页大小
        orderTotal: 0,               // 订单总数
        orderStatusFilter: '',       // 订单状态筛选
        orderStats: {                // 订单统计
            totalOrders: 0,
            pendingOrders: 0,
            completedOrders: 0,
            totalAmount: 0
        },
        orderDetailDialogVisible: false,  // 订单详情对话框显示状态
        currentOrderDetail: null,    // 当前查看的订单详情

        // 数据分析相关数据
        analysisData: {              // 分析数据
            todaySales: '0',
            monthlySales: '0',
            yearlySales: '0',
            todayOrders: 0,
            monthlyOrders: 0,
            newUsers: 0,
            activeUsers: 0
        },

        // 产品管理增强功能
        productSearchKeyword: '',        // 产品搜索关键词
        productCategoryFilter: '',       // 产品分类筛选
        productStockFilter: '',          // 产品库存筛选
        selectedProducts: [],            // 选中的产品列表
        productLoading: false,           // 产品加载状态
        filteredProductList: [],         // 筛选后的产品列表

        // 产品搜索表单
        productSearchForm: {
            name: '',                    // 产品名称搜索
            category: ''                 // 分类搜索
        },

        // 产品详情相关
        productDetailDialogVisible: false,  // 产品详情对话框显示状态
        currentProductDetail: null,      // 当前查看的产品详情

        // 产品图片管理相关
        productImageDialogVisible: false,    // 产品图片管理对话框显示状态
        currentProductForImages: null,   // 当前管理图片的产品
        productImages: [],               // 产品图片列表

        // 图片预览相关
        imagePreviewDialogVisible: false,    // 图片预览对话框显示状态
        previewImageUrl: '',             // 预览图片URL

        // 用户管理增强功能
        userStats: {                     // 用户统计数据
            total: 0,
            active: 0,
            todayNew: 0,
            vip: 0
        },
        userSearchForm: {                // 用户搜索表单
            keyword: '',                 // 搜索关键词
            status: '',                  // 用户状态
            dateRange: null              // 注册时间范围
        },
        userLoading: false,              // 用户加载状态

        // 订单管理增强功能
        orderSearchForm: {               // 订单搜索表单
            keyword: '',                 // 搜索关键词
            status: '',                  // 订单状态
            dateRange: null              // 下单时间范围
        },
        orderDetailDialogVisible: false, // 订单详情对话框显示状态
        currentOrderDetail: null,        // 当前查看的订单详情

        // 搜索计时器
        userSearchTimer: null,           // 用户搜索防抖计时器
        productSearchTimer: null,        // 产品搜索防抖计时器

        // 全局搜索
        globalSearchKeyword: '',         // 全局搜索关键词
        globalSearchResults: [],         // 全局搜索结果
        globalSearchVisible: false,      // 全局搜索结果显示状态
        globalSearchTimer: null,         // 全局搜索防抖计时器

        // 收藏管理相关数据
        favoriteList: [],                // 收藏列表
        favoriteLoading: false,          // 收藏加载状态
        favoritePagination: {            // 收藏分页信息
            currentPage: 1,
            pageSize: 10,
            total: 0
        },
        favoriteStats: {                 // 收藏统计数据
            total: 0,
            todayNew: 0,
            popularProduct: 0,
            activeUsers: 0
        },
        favoriteSearchForm: {            // 收藏搜索表单
            keyword: '',
            category: '',
            dateRange: null
        },
        favoriteSearchTimer: null,       // 收藏搜索防抖计时器

        // 评论管理相关数据
        commentList: [],                 // 评论列表
        commentLoading: false,           // 评论加载状态
        commentPagination: {             // 评论分页信息
            currentPage: 1,
            pageSize: 10,
            total: 0
        },
        commentStats: {                  // 评论统计数据
            total: 0,
            pending: 0,
            approved: 0,
            rejected: 0
        },
        commentSearchForm: {             // 评论搜索表单
            keyword: '',
            status: '',
            rating: '',
            dateRange: null
        },
        commentSearchTimer: null,        // 评论搜索防抖计时器
        selectedComments: [],            // 选中的评论列表
        commentDetailDialogVisible: false, // 评论详情对话框显示状态
        currentCommentDetail: null,      // 当前查看的评论详情

        // 分享管理相关数据
        shareList: [],                   // 分享列表
        shareLoading: false,             // 分享加载状态
        sharePagination: {               // 分享分页信息
            currentPage: 1,
            pageSize: 10,
            total: 0
        },
        shareStats: {                    // 分享统计数据
            total: 0,
            wechat: 0,
            weibo: 0,
            qq: 0
        },
        shareSearchForm: {               // 分享搜索表单
            keyword: '',
            shareType: '',
            dateRange: null
        },
        shareSearchTimer: null,          // 分享搜索防抖计时器

        // 系统设置
        systemInfo: {                    // 系统信息
            version: 'v1.2.0',
            uptime: '15天',
            storage: '3.8GB',
            memory: '768MB',
            database: 'MySQL 8.0',
            javaVersion: 'JDK 1.8',
            lastBackup: '2024-01-15 02:00:00',
            activeUsers: 0,
            todayOrders: 0
        },
        recentLogs: [                    // 最近操作日志
            {
                time: new Date().getTime() - 1000 * 60 * 5,
                user: '管理员',
                action: '新增',
                description: '新增产品：美白精华液',
                ip: '*************'
            },
            {
                time: new Date().getTime() - 1000 * 60 * 15,
                user: '管理员',
                action: '编辑',
                description: '修改用户信息：用户ID 12345',
                ip: '*************'
            },
            {
                time: new Date().getTime() - 1000 * 60 * 30,
                user: '管理员',
                action: '删除',
                description: '删除过期轮播图',
                ip: '*************'
            }
        ],
        settingsDialogVisible: false,    // 设置对话框显示状态
        currentSettingsType: '',         // 当前设置类型
        settingsForm: {},                // 设置表单数据

        // 系统设置相关数据
        currentSettingsTab: 'general',   // 当前设置标签页
        systemSettings: {                // 系统设置数据
            siteName: '息壤集',
            siteDescription: '天然美妆购物平台',
            contactEmail: '<EMAIL>',
            themeColor: '#ff6b6b',
            emailNotification: true,
            orderNotification: true
        },

        // 日志管理相关数据
        logList: [],                     // 日志列表
        logLoading: false,               // 日志加载状态
        logPagination: {                 // 日志分页信息
            currentPage: 1,
            pageSize: 20,
            total: 0
        },
        logStats: {                      // 日志统计数据
            today: 0,
            success: 0,
            warning: 0,
            error: 0
        },
        logSearchForm: {                 // 日志搜索表单
            level: '',
            action: '',
            keyword: '',
            dateRange: null
        },
        logSearchTimer: null,            // 日志搜索防抖计时器

        // 备份管理相关数据
        backupList: [],                  // 备份列表
        backupListLoading: false,        // 备份列表加载状态
        backupLoading: false,            // 备份操作加载状态
        backupPagination: {              // 备份分页信息
            currentPage: 1,
            pageSize: 10,
            total: 0
        },
        backupStats: {                   // 备份统计数据
            total: 0,
            latest: '无',
            totalSize: '0 MB',
            autoEnabled: false
        },
        manualBackupForm: {              // 手动备份表单
            name: '',
            type: 'full'
        },
        autoBackupForm: {                // 自动备份表单
            enabled: false,
            frequency: 'daily',
            time: null,
            retentionDays: 30
        },

        // 安全中心相关数据
        securityStats: {                 // 安全统计数据
            level: '高',
            todayAttacks: 0,
            blacklistIPs: 0,
            onlineUsers: 0
        },
        loginSecurityForm: {             // 登录安全表单
            passwordStrength: 'medium',
            maxFailAttempts: 5,
            lockDuration: 30,
            forcePasswordChange: false,
            twoFactorAuth: false
        },
        apiSecurityForm: {               // API安全表单
            rateLimitEnabled: true,
            requestsPerMinute: 100,
            ipWhitelistEnabled: false,
            corsEnabled: true,
            sqlInjectionProtection: true
        },
        activeIPTab: 'blacklist',        // 当前IP管理标签页
        blacklistIPs: [],                // 黑名单IP列表
        whitelistIPs: [],                // 白名单IP列表
        addIPDialogVisible: false,       // 添加IP对话框显示状态
        securityLogs: [],                // 安全日志列表
        securityLogLoading: false,       // 安全日志加载状态
        securityLogPagination: {         // 安全日志分页信息
            currentPage: 1,
            pageSize: 10,
            total: 0
        },
        addIPForm: {                     // 添加IP表单
            ip: '',
            type: 'blacklist',
            description: '',
            expireTime: null
        }
    },
    computed: {
        // 格式化编辑表单中的注册时间
        formattedRegisterTime() {
            if (!this.editForm.registerTime) return '';
            return this.formatDateTime(this.editForm.registerTime);
        },
        // 格式化新用户表单中的注册时间
        formattedNewUserRegisterTime() {
            if (!this.newUser.registerTime) return '';
            return this.formatDateTime(this.newUser.registerTime);
        },
        // 筛选后的产品列表
        filteredProductList() {
            let filtered = [...this.productList];

            // 按关键词搜索
            if (this.productSearchKeyword) {
                const keyword = this.productSearchKeyword.toLowerCase();
                filtered = filtered.filter(product =>
                    product.name.toLowerCase().includes(keyword) ||
                    (product.description && product.description.toLowerCase().includes(keyword))
                );
            }

            // 按分类筛选
            if (this.productCategoryFilter) {
                filtered = filtered.filter(product =>
                    product.categoryIds && product.categoryIds.includes(this.productCategoryFilter)
                );
            }

            // 按库存状态筛选
            if (this.productStockFilter) {
                filtered = filtered.filter(product => {
                    const stock = product.stock || 0;
                    switch (this.productStockFilter) {
                        case 'sufficient': return stock > 50;
                        case 'low': return stock >= 20 && stock <= 50;
                        case 'insufficient': return stock < 20;
                        default: return true;
                    }
                });
            }

            return filtered;
        }
    },
    created() {
        this.loadUserList();
        this.loadProductList();
        this.loadCategoryList();
        this.loadOrders();
        this.loadOrderStats();
        this.loadAnalysisData();
        this.loadSystemSettings();
        this.loadLogStats();
        this.loadBackupStats();
        this.loadSecurityStats();
    },
    mounted() {
        // 设置当前日期
        this.setCurrentDate();
        // 加载数据
        this.loadData();
        // 添加点击页面其他区域关闭下拉菜单的事件
        document.addEventListener('click', this.handleDocumentClick);
    },
    beforeDestroy() {
        // 移除事件监听器，防止内存泄漏
        document.removeEventListener('click', this.handleDocumentClick);
    },
    methods: {
        // 切换下拉菜单显示状态
        toggleDropdown() {
            this.dropdownVisible = !this.dropdownVisible;
        },
        // 关闭下拉菜单
        closeDropdown() {
            this.dropdownVisible = false;
        },
        // 处理文档点击事件
        handleDocumentClick(event) {
            // 如果点击的元素不是下拉菜单的一部分，则关闭下拉菜单
            const dropdownElement = document.querySelector('.dropdown-content');
            const triggerElement = document.querySelector('.user-menu > div');

            if (dropdownElement && triggerElement) {
                if (!dropdownElement.contains(event.target) && !triggerElement.contains(event.target)) {
                    this.dropdownVisible = false;
                }
            }
        },
        // 切换激活的侧边栏项目
        changeTab(tab) {
            this.activeTab = tab;

            // 根据切换的tab加载对应的数据
            if (tab === 'dashboard') {
                // 仪表盘需要加载所有统计数据
                this.loadUserList();
                this.loadProductList();
                this.loadCategoryList();
            } else if (tab === 'userManagement') {
                this.loadUserList();
            } else if (tab === 'productManagement') {
                this.loadProductList();
            } else if (tab === 'categoryManagement') {
                this.loadCategoryList();
            } else if (tab === 'carouselManagement') {
                this.loadCarouselList();
            } else if (tab === 'orderManagement') {
                this.loadOrders();
                this.loadOrderStats();
            } else if (tab === 'favoriteManagement') {
                this.loadFavorites();
                this.loadFavoriteStats();
            } else if (tab === 'commentManagement') {
                this.loadComments();
                this.loadCommentStats();
            } else if (tab === 'shareManagement') {
                this.loadShares();
                this.loadShareStats();
            } else if (tab === 'dataAnalysis') {
                // 延迟初始化图表，确保DOM已渲染
                this.$nextTick(() => {
                    this.initCharts();
                    // 加载数据分析相关数据
                    this.loadAnalysisData();
                    this.loadOrderStats();
                });
            } else if (tab === 'logManagement') {
                this.loadLogs();
                this.loadLogStats();
            } else if (tab === 'backupManagement') {
                this.loadBackupList();
                this.loadBackupStats();
            } else if (tab === 'securityCenter') {
                this.loadSecurityLogs();
                this.loadSecurityStats();
                this.loadBlacklistIPs();
                this.loadWhitelistIPs();
            }

            // 触发重绘，确保样式更新
            setTimeout(() => {
                window.dispatchEvent(new Event('resize'));
            }, 100);
        },
        // 设置当前日期
        setCurrentDate() {
            const date = new Date();
            const currentDateElement = document.getElementById('current-date');
            if (currentDateElement) {
                currentDateElement.textContent = date.toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    weekday: 'long'
                });
            }
        },
        // 加载数据
        loadData() {
            this.loadUserList();
            this.loadProductList();
            this.loadCategoryList();
        },
        // 加载用户列表
        loadUserList() {
            this.userLoading = true;
            const params = {
                page: this.userPagination.currentPage,
                size: this.userPagination.pageSize
            };

            // 添加搜索条件
            if (this.userSearchForm.keyword) {
                params.keyword = this.userSearchForm.keyword;
            }
            if (this.userSearchForm.status) {
                params.status = this.userSearchForm.status;
            }
            if (this.userSearchForm.dateRange && this.userSearchForm.dateRange.length === 2) {
                params.startDate = this.formatDate(this.userSearchForm.dateRange[0]);
                params.endDate = this.formatDate(this.userSearchForm.dateRange[1]);
            }

            axios.get('/user/userList/page', { params })
                .then(response => {
                    console.log('返回的用户分页数据:', response.data);
                    this.userList = response.data.data;
                    this.userPagination.total = response.data.total;
                    console.log('更新后的用户列表:', this.userList);

                    // 更新用户统计
                    this.loadUserStats();

                    // 更新用户统计（仅在仪表盘页面存在时）
                    const totalUsersElement = document.getElementById('total-users');
                    if (totalUsersElement) {
                        totalUsersElement.textContent = response.data.total;
                    }
                    // 更新数据分析页面的用户统计
                    const totalUsersAnalysisElement = document.getElementById('total-users-analysis');
                    if (totalUsersAnalysisElement) {
                        totalUsersAnalysisElement.textContent = response.data.total;
                    }
                })
                .catch(error => {
                    console.error('获取用户列表出错:', error);
                    this.$message.error('获取用户列表失败');
                })
                .finally(() => {
                    this.userLoading = false;
                });
        },

        // 加载用户统计数据
        loadUserStats() {
            axios.get('/api/user-detail/stats')
                .then(response => {
                    if (response.data.success) {
                        this.userStats = response.data.data;
                    }
                })
                .catch(error => {
                    console.warn('获取用户统计失败:', error);
                    // 使用默认统计数据
                    this.userStats = {
                        total: this.userList.length,
                        active: Math.floor(this.userList.length * 0.8),
                        todayNew: Math.floor(Math.random() * 10),
                        vip: Math.floor(this.userList.length * 0.1)
                    };
                });
        },

        // 搜索用户
        searchUsers() {
            console.log('🔍 搜索用户，关键词:', this.userSearchForm.keyword);
            this.userPagination.currentPage = 1;
            this.loadUserList();
        },

        // 实时搜索用户（输入时触发）
        onUserSearchInput() {
            console.log('🔍 用户搜索输入:', this.userSearchForm.keyword);
            // 防抖处理，避免频繁请求
            clearTimeout(this.userSearchTimer);
            this.userSearchTimer = setTimeout(() => {
                console.log('⏰ 用户搜索防抖时间到，执行搜索');
                this.searchUsers();
            }, 300); // 减少防抖时间，提高响应速度
        },

        // 重置用户搜索
        resetUserSearch() {
            this.userSearchForm = {
                keyword: '',
                status: '',
                dateRange: null
            };
            this.searchUsers();
        },

        // 导出用户数据
        exportUsers() {
            this.$confirm('确定要导出用户数据吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'info'
            }).then(() => {
                // 创建CSV数据
                const csvData = this.generateUserCSV();
                this.downloadCSV(csvData, '用户数据.csv');
                this.$message.success('用户数据导出成功');
            }).catch(() => {
                this.$message.info('已取消导出');
            });
        },

        // 生成用户CSV数据
        generateUserCSV() {
            const headers = ['用户ID', '用户名', '手机号', '注册时间', '状态'];
            const rows = this.userList.map(user => [
                user.userId || '',
                user.username || '',
                user.phone || '',
                this.formatDateTime(user.registerTime) || '',
                this.getUserStatus(user) || ''
            ]);

            return [headers, ...rows].map(row =>
                row.map(cell => `"${cell}"`).join(',')
            ).join('\n');
        },

        // 下载CSV文件
        downloadCSV(csvData, filename) {
            const blob = new Blob(['\uFEFF' + csvData], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        },

        // 获取用户状态
        getUserStatus(user) {
            // 这里可以根据实际业务逻辑判断用户状态
            if (user.status) {
                return user.status;
            }
            return '正常';
        },

        // 格式化日期
        formatDate(date) {
            if (!date) return '';
            const d = new Date(date);
            return d.getFullYear() + '-' +
                   String(d.getMonth() + 1).padStart(2, '0') + '-' +
                   String(d.getDate()).padStart(2, '0');
        },

        // ==================== 产品管理增强功能 ====================

        // 处理产品选择变化
        handleProductSelectionChange(selection) {
            this.selectedProducts = selection;
        },

        // 批量删除产品
        batchDeleteProducts() {
            if (this.selectedProducts.length === 0) {
                this.$message.warning('请先选择要删除的产品');
                return;
            }

            this.$confirm(`确定要删除选中的 ${this.selectedProducts.length} 个产品吗？`, '批量删除确认', {
                confirmButtonText: '确定删除',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const productIds = this.selectedProducts.map(product => product.id);

                axios.post('/api/products/batch-delete', { ids: productIds })
                    .then(response => {
                        if (response.data.success) {
                            this.$message.success(`成功删除 ${productIds.length} 个产品`);
                            this.loadProductList();
                            this.selectedProducts = [];
                        } else {
                            this.$message.error('批量删除失败: ' + response.data.message);
                        }
                    })
                    .catch(error => {
                        console.error('批量删除失败:', error);
                        this.$message.error('批量删除失败，请重试');
                    });
            }).catch(() => {
                this.$message.info('已取消删除');
            });
        },

        // 批量更新库存
        batchUpdateStock() {
            if (this.selectedProducts.length === 0) {
                this.$message.warning('请先选择要更新库存的产品');
                return;
            }

            this.$prompt('请输入新的库存数量', '批量更新库存', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                inputPattern: /^\d+$/,
                inputErrorMessage: '请输入有效的数字'
            }).then(({ value }) => {
                const stock = parseInt(value);
                const productIds = this.selectedProducts.map(product => product.id);

                axios.post('/api/products/batch-update-stock', {
                    ids: productIds,
                    stock: stock
                })
                .then(response => {
                    if (response.data.success) {
                        this.$message.success(`成功更新 ${productIds.length} 个产品的库存`);
                        this.loadProductList();
                        this.selectedProducts = [];
                    } else {
                        this.$message.error('批量更新库存失败: ' + response.data.message);
                    }
                })
                .catch(error => {
                    console.error('批量更新库存失败:', error);
                    this.$message.error('批量更新库存失败，请重试');
                });
            }).catch(() => {
                this.$message.info('已取消更新');
            });
        },

        // 批量更新分类
        batchUpdateCategory() {
            if (this.selectedProducts.length === 0) {
                this.$message.warning('请先选择要更新分类的产品');
                return;
            }

            // 创建分类选择对话框
            this.$prompt('请选择新的分类', '批量更新分类', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                inputType: 'select',
                inputOptions: this.categoryList.map(cat => ({
                    value: cat.id,
                    label: cat.name
                }))
            }).then(({ value }) => {
                const categoryId = parseInt(value);
                const productIds = this.selectedProducts.map(product => product.id);

                axios.post('/api/products/batch-update-category', {
                    ids: productIds,
                    categoryId: categoryId
                })
                .then(response => {
                    if (response.data.success) {
                        this.$message.success(`成功更新 ${productIds.length} 个产品的分类`);
                        this.loadProductList();
                        this.selectedProducts = [];
                    } else {
                        this.$message.error('批量更新分类失败: ' + response.data.message);
                    }
                })
                .catch(error => {
                    console.error('批量更新分类失败:', error);
                    this.$message.error('批量更新分类失败，请重试');
                });
            }).catch(() => {
                this.$message.info('已取消更新');
            });
        },

        // 导出产品数据
        exportProducts() {
            this.$confirm('确定要导出产品数据吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'info'
            }).then(() => {
                // 创建CSV数据
                const csvData = this.generateProductCSV();
                this.downloadCSV(csvData, '产品数据.csv');
                this.$message.success('产品数据导出成功');
            }).catch(() => {
                this.$message.info('已取消导出');
            });
        },

        // 生成产品CSV数据
        generateProductCSV() {
            const headers = ['产品ID', '产品名称', '描述', '价格', '库存', '分类', '状态', '是否新品'];
            const rows = this.productList.map(product => [
                product.id || '',
                product.name || '',
                product.description || '',
                product.price || '',
                product.stock || '',
                this.getCategoryName(product.categoryIds) || '',
                product.status === 1 ? '上架' : '下架',
                product.isNew ? '是' : '否'
            ]);

            return [headers, ...rows].map(row =>
                row.map(cell => `"${cell}"`).join(',')
            ).join('\n');
        },

        // 获取分类名称
        getCategoryName(categoryIds) {
            if (!categoryIds || !Array.isArray(categoryIds)) return '';
            return categoryIds.map(id => {
                const category = this.categoryList.find(cat => cat.id === id);
                return category ? category.name : '';
            }).filter(name => name).join(', ');
        },

        // 处理产品操作
        handleProductAction(command) {
            const { action, row } = command;

            switch (action) {
                case 'toggleStatus':
                    this.toggleProductStatus(row);
                    break;
                case 'copy':
                    this.copyProduct(row);
                    break;
                case 'delete':
                    this.deleteProduct(row.id);
                    break;
            }
        },

        // 切换产品状态
        toggleProductStatus(product) {
            const newStatus = product.status === 1 ? 0 : 1;
            const statusText = newStatus === 1 ? '上架' : '下架';

            this.$confirm(`确定要${statusText}产品"${product.name}"吗？`, '确认操作', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'info'
            }).then(() => {
                axios.post(`/api/products/${product.id}/toggle-status`, { status: newStatus })
                    .then(response => {
                        if (response.data.success) {
                            this.$message.success(`产品已${statusText}`);
                            this.loadProductList();
                        } else {
                            this.$message.error('操作失败: ' + response.data.message);
                        }
                    })
                    .catch(error => {
                        console.error('切换产品状态失败:', error);
                        this.$message.error('操作失败，请重试');
                    });
            }).catch(() => {
                this.$message.info('已取消操作');
            });
        },

        // 复制产品
        copyProduct(product) {
            this.$confirm(`确定要复制产品"${product.name}"吗？`, '确认复制', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'info'
            }).then(() => {
                const newProduct = {
                    ...product,
                    name: product.name + ' (副本)',
                    id: undefined // 移除ID，让后端生成新ID
                };

                axios.post('/products/add', newProduct)
                    .then(response => {
                        if (response.data.success) {
                            this.$message.success('产品复制成功');
                            this.loadProductList();
                        } else {
                            this.$message.error('复制失败: ' + response.data.message);
                        }
                    })
                    .catch(error => {
                        console.error('复制产品失败:', error);
                        this.$message.error('复制失败，请重试');
                    });
            }).catch(() => {
                this.$message.info('已取消复制');
            });
        },

        // ==================== 订单管理增强功能 ====================

        // 搜索订单
        searchOrders() {
            this.orderCurrentPage = 1;
            this.loadOrders();
        },

        // 重置订单搜索
        resetOrderSearch() {
            this.orderSearchForm = {
                keyword: '',
                status: '',
                dateRange: null
            };
            this.searchOrders();
        },

        // 查看订单详情
        viewOrderDetail(order) {
            console.log('🔍 查看订单详情:', order);

            // 获取订单详细信息（包含商品详情）
            axios.get(`/api/admin/orders/${order.id}/detail`)
                .then(response => {
                    if (response.data.success) {
                        this.currentOrderDetail = response.data.data;
                        console.log('📦 订单详情数据:', this.currentOrderDetail);
                        this.orderDetailDialogVisible = true;
                    } else {
                        this.$message.error('获取订单详情失败: ' + response.data.message);
                    }
                })
                .catch(error => {
                    console.error('获取订单详情失败:', error);
                    this.$message.error('网络错误，请重试');
                });
        },

        // 删除订单
        deleteOrder(order) {
            this.$confirm(`确定要删除订单"${order.orderNo}"吗？`, '删除确认', {
                confirmButtonText: '确定删除',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.delete(`/api/orders/${order.id}`)
                    .then(response => {
                        if (response.data.success) {
                            this.$message.success('订单删除成功');
                            this.loadOrders();
                        } else {
                            this.$message.error('删除失败: ' + response.data.message);
                        }
                    })
                    .catch(error => {
                        console.error('删除订单失败:', error);
                        this.$message.error('删除失败，请重试');
                    });
            }).catch(() => {
                this.$message.info('已取消删除');
            });
        },

        // 导出订单数据
        exportOrders() {
            this.$confirm('确定要导出订单数据吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'info'
            }).then(() => {
                // 创建CSV数据
                const csvData = this.generateOrderCSV();
                this.downloadCSV(csvData, '订单数据.csv');
                this.$message.success('订单数据导出成功');
            }).catch(() => {
                this.$message.info('已取消导出');
            });
        },

        // 生成订单CSV数据
        generateOrderCSV() {
            const headers = ['订单号', '用户手机', '订单金额', '订单状态', '收货人', '收货电话', '下单时间'];
            const rows = this.orderList.map(order => [
                order.orderNo || '',
                order.userPhone || '',
                order.totalAmount || '',
                this.getOrderStatusText(order.status) || '',
                order.receiverName || '',
                order.receiverPhone || '',
                this.formatTime(order.createdTime) || ''
            ]);

            return [headers, ...rows].map(row =>
                row.map(cell => `"${cell}"`).join(',')
            ).join('\n');
        },

        // 刷新订单列表
        refreshOrders() {
            this.loadOrders();
            this.$message.success('订单列表已刷新');
        },

        // ==================== 全局搜索功能 ====================

        // 全局搜索
        performGlobalSearch() {
            if (!this.globalSearchKeyword.trim()) {
                this.globalSearchResults = [];
                this.globalSearchVisible = false;
                return;
            }

            console.log('🔍 执行全局搜索:', this.globalSearchKeyword);

            // 防抖处理
            clearTimeout(this.globalSearchTimer);
            this.globalSearchTimer = setTimeout(() => {
                this.executeGlobalSearch();
            }, 300);
        },

        // 执行全局搜索
        executeGlobalSearch() {
            const keyword = this.globalSearchKeyword.trim();
            if (!keyword) return;

            const results = [];

            // 搜索用户
            const matchedUsers = this.userList.filter(user =>
                user.username?.toLowerCase().includes(keyword.toLowerCase()) ||
                user.phone?.includes(keyword) ||
                user.userId?.includes(keyword)
            ).slice(0, 3);

            matchedUsers.forEach(user => {
                results.push({
                    type: 'user',
                    title: user.username || user.phone,
                    subtitle: `用户 - ${user.phone}`,
                    action: () => {
                        this.activeTab = 'userManagement';
                        this.userSearchForm.keyword = keyword;
                        this.searchUsers();
                        this.globalSearchVisible = false;
                    }
                });
            });

            // 搜索产品
            const matchedProducts = this.productList.filter(product =>
                product.name?.toLowerCase().includes(keyword.toLowerCase()) ||
                product.description?.toLowerCase().includes(keyword.toLowerCase())
            ).slice(0, 3);

            matchedProducts.forEach(product => {
                results.push({
                    type: 'product',
                    title: product.name,
                    subtitle: `产品 - ¥${product.price}`,
                    action: () => {
                        this.activeTab = 'productManagement';
                        this.productSearchForm.name = keyword;
                        this.searchProducts();
                        this.globalSearchVisible = false;
                    }
                });
            });

            // 搜索分类
            const matchedCategories = this.categoryList.filter(category =>
                category.name?.toLowerCase().includes(keyword.toLowerCase())
            ).slice(0, 2);

            matchedCategories.forEach(category => {
                results.push({
                    type: 'category',
                    title: category.name,
                    subtitle: `分类 - ${category.product_count || 0} 个产品`,
                    action: () => {
                        this.activeTab = 'categoryManagement';
                        this.globalSearchVisible = false;
                    }
                });
            });

            // 功能页面搜索
            const pages = [
                { name: '用户管理', tab: 'userManagement', keywords: ['用户', '会员', '客户'] },
                { name: '产品管理', tab: 'productManagement', keywords: ['产品', '商品', '货品'] },
                { name: '分类管理', tab: 'categoryManagement', keywords: ['分类', '类别'] },
                { name: '订单管理', tab: 'orderManagement', keywords: ['订单', '交易'] },
                { name: '轮播图管理', tab: 'carouselManagement', keywords: ['轮播', '图片', '横幅'] },
                { name: '数据分析', tab: 'dataAnalysis', keywords: ['数据', '统计', '分析', '图表'] },
                { name: '系统设置', tab: 'systemSettings', keywords: ['设置', '配置', '系统'] }
            ];

            const matchedPages = pages.filter(page =>
                page.name.toLowerCase().includes(keyword.toLowerCase()) ||
                page.keywords.some(k => k.includes(keyword))
            );

            matchedPages.forEach(page => {
                results.push({
                    type: 'page',
                    title: page.name,
                    subtitle: '功能页面',
                    action: () => {
                        this.activeTab = page.tab;
                        this.globalSearchVisible = false;
                    }
                });
            });

            this.globalSearchResults = results;
            this.globalSearchVisible = results.length > 0;

            console.log('🔍 搜索结果:', results);
        },

        // 执行搜索结果操作
        executeSearchAction(result) {
            if (result.action) {
                result.action();
            }
        },

        // 清空全局搜索
        clearGlobalSearch() {
            this.globalSearchKeyword = '';
            this.globalSearchResults = [];
            this.globalSearchVisible = false;
        },

        // ==================== 系统设置功能 ====================

        // 打开设置对话框
        openSettingsDialog(type) {
            this.currentSettingsType = type;
            this.settingsDialogVisible = true;
            this.loadSettingsData(type);
        },

        // 加载设置数据
        loadSettingsData(type) {
            switch (type) {
                case 'basic':
                    this.settingsForm = {
                        siteName: '息壤集',
                        siteDescription: '天然美妆购物平台',
                        contactPhone: '************',
                        contactEmail: '<EMAIL>',
                        address: '北京市朝阳区xxx街道xxx号'
                    };
                    break;
                case 'email':
                    this.settingsForm = {
                        smtpHost: '',
                        smtpPort: 587,
                        smtpUser: '',
                        smtpPassword: '',
                        fromEmail: '',
                        fromName: '息壤集'
                    };
                    break;
                case 'payment':
                    this.settingsForm = {
                        alipayEnabled: true,
                        alipayAppId: '',
                        alipayPrivateKey: '',
                        wechatEnabled: true,
                        wechatAppId: '',
                        wechatMchId: '',
                        wechatKey: ''
                    };
                    break;
                case 'sms':
                    this.settingsForm = {
                        smsProvider: 'aliyun',
                        accessKeyId: '',
                        accessKeySecret: '',
                        signName: '息壤集',
                        templateCode: ''
                    };
                    break;
                case 'storage':
                    this.settingsForm = {
                        storageType: 'local',
                        maxFileSize: 10,
                        allowedTypes: 'jpg,jpeg,png,gif,webp',
                        cdnDomain: '',
                        ossEndpoint: '',
                        ossBucket: ''
                    };
                    break;
                case 'security':
                    this.settingsForm = {
                        passwordMinLength: 6,
                        passwordRequireSpecial: false,
                        loginMaxAttempts: 5,
                        sessionTimeout: 30,
                        enableCaptcha: true,
                        apiRateLimit: 100
                    };
                    break;
            }
        },

        // 保存设置
        saveSettings() {
            console.log('保存设置:', this.currentSettingsType, this.settingsForm);

            // 这里应该调用后端API保存设置
            // axios.post('/api/settings/' + this.currentSettingsType, this.settingsForm)

            this.$message.success('设置保存成功');
            this.settingsDialogVisible = false;
        },

        // 获取日志类型样式
        getLogType(action) {
            switch (action) {
                case '新增': return 'success';
                case '编辑': return 'warning';
                case '删除': return 'danger';
                case '登录': return 'info';
                default: return '';
            }
        },

        // 查看全部日志
        viewAllLogs() {
            this.$message.info('跳转到日志管理页面');
            // 这里可以跳转到专门的日志管理页面
        },

        // 获取设置标题
        getSettingsTitle() {
            const titles = {
                'basic': '基本设置',
                'email': '邮件设置',
                'payment': '支付设置',
                'sms': '短信设置',
                'storage': '存储设置',
                'security': '安全设置',
                'backup': '备份设置',
                'performance': '性能优化'
            };
            return titles[this.currentSettingsType] || '系统设置';
        },

        // 系统维护功能
        performSystemMaintenance(action) {
            switch (action) {
                case 'clearCache':
                    this.clearSystemCache();
                    break;
                case 'backup':
                    this.performBackup();
                    break;
                case 'optimize':
                    this.optimizeDatabase();
                    break;
                case 'restart':
                    this.restartSystem();
                    break;
            }
        },

        // 清理系统缓存
        clearSystemCache() {
            this.$confirm('确定要清理系统缓存吗？这可能会暂时影响系统性能。', '清理缓存', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const loading = this.$loading({
                    lock: true,
                    text: '正在清理缓存...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });

                // 模拟清理缓存
                setTimeout(() => {
                    loading.close();
                    this.$message.success('系统缓存清理完成');
                }, 2000);
            });
        },

        // 执行系统备份
        performBackup() {
            this.$confirm('确定要执行系统备份吗？备份过程可能需要几分钟时间。', '系统备份', {
                confirmButtonText: '开始备份',
                cancelButtonText: '取消',
                type: 'info'
            }).then(() => {
                const loading = this.$loading({
                    lock: true,
                    text: '正在备份系统数据...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });

                // 模拟备份过程
                setTimeout(() => {
                    loading.close();
                    this.systemInfo.lastBackup = new Date().toLocaleString();
                    this.$message.success('系统备份完成');
                }, 3000);
            });
        },

        // 优化数据库
        optimizeDatabase() {
            this.$confirm('确定要优化数据库吗？这将清理冗余数据并重建索引。', '数据库优化', {
                confirmButtonText: '开始优化',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const loading = this.$loading({
                    lock: true,
                    text: '正在优化数据库...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });

                // 模拟数据库优化
                setTimeout(() => {
                    loading.close();
                    this.$message.success('数据库优化完成，性能已提升');
                }, 4000);
            });
        },

        // 重启系统
        restartSystem() {
            this.$confirm('确定要重启系统吗？这将断开所有用户连接。', '重启系统', {
                confirmButtonText: '确定重启',
                cancelButtonText: '取消',
                type: 'danger'
            }).then(() => {
                this.$message.warning('系统将在10秒后重启...');
                // 这里应该调用后端重启接口
                // axios.post('/api/system/restart')
            });
        },

        // 更新系统信息
        updateSystemInfo() {
            // 模拟获取实时系统信息
            this.systemInfo.activeUsers = this.userList.length;
            this.systemInfo.todayOrders = Math.floor(Math.random() * 50) + 10;
            this.systemInfo.uptime = Math.floor(Math.random() * 30) + 1 + '天';
            this.systemInfo.memory = Math.floor(Math.random() * 200) + 600 + 'MB';
        },
        // 加载产品列表 - 使用强制刷新接口
        loadProductList() {
            const params = {
                page: this.productPagination.currentPage,
                size: this.productPagination.pageSize
            };

            // 添加搜索条件
            if (this.productSearchForm.name) {
                params.name = this.productSearchForm.name;
            }
            if (this.productSearchForm.category && this.productSearchForm.category !== '') {
                params.category = this.productSearchForm.category;
            }

            // 使用正确的产品分页接口
            axios.get('/products/page', { params })
                .then(response => {
                    console.log('🔄 产品数据响应:', response.data);
                    console.log('📋 产品列表数据:', response.data.data);

                    if (response.data.success) {
                        this.productList = response.data.data || [];
                        this.productPagination.total = response.data.total || 0;

                        // 强制更新Vue组件
                        this.$nextTick(() => {
                            this.$forceUpdate();
                        });
                    } else {
                        this.productList = [];
                        this.productPagination.total = 0;
                        this.$message.error(response.data.message || '获取产品列表失败');
                    }

                    // 调试：检查每个产品的imageUrl
                    if (this.productList && this.productList.length > 0) {
                        console.log('🖼️ 产品图片URL检查:');
                        this.productList.forEach((product, index) => {
                            console.log(`产品${index + 1}:`, {
                                id: product.id,
                                name: product.name,
                                imageUrl: product.imageUrl,
                                hasImageUrl: !!product.imageUrl,
                                imageUrlLength: product.imageUrl ? product.imageUrl.length : 0
                            });
                        });
                    }

                    // 更新产品统计（仅在仪表盘页面存在时）
                    const totalProductsElement = document.getElementById('total-products');
                    if (totalProductsElement) {
                        totalProductsElement.textContent = response.data.total;
                    }
                    // 更新图表数据（仅在数据分析页面存在时）
                    if (this.salesChart) {
                        this.updateSalesChart();
                    }
                })
                .catch(error => {
                    console.error('获取产品列表出错:', error);
                    this.$message.error('获取产品列表失败');
                });
        },

        // 搜索产品
        searchProducts() {
            console.log('🔍 执行产品搜索:', this.productSearchForm);
            this.productPagination.currentPage = 1; // 重置到第一页
            this.loadProductList();
        },

        // 重置搜索条件
        resetProductSearch() {
            this.productSearchForm = {
                name: '',
                category: ''
            };
            this.productPagination.currentPage = 1;
            this.loadProductList();
            this.$message.success('搜索条件已重置');
        },

        // 处理搜索框回车事件
        handleProductSearchEnter() {
            this.searchProducts();
        },

        // 实时搜索产品（输入时触发）
        onProductSearchInput() {
            console.log('🔍 产品搜索输入:', this.productSearchForm.name);
            // 防抖处理，避免频繁请求
            clearTimeout(this.productSearchTimer);
            this.productSearchTimer = setTimeout(() => {
                console.log('⏰ 防抖时间到，执行搜索');
                this.productPagination.currentPage = 1; // 重置到第一页
                this.loadProductList(); // 直接调用加载函数
            }, 500); // 增加防抖时间，避免过于频繁的请求
        },
        // 加载分类列表
        loadCategoryList() {
            console.log('🔄 正在加载分类列表...');

            // 首先尝试使用包含产品数量的轮播图接口
            axios.get('/carousel/categories')
                .then(response => {
                    console.log('📋 分类列表响应:', response.data);

                    if (response.data && response.data.success) {
                        this.categoryList = response.data.data || [];
                        this.categoryPagination.total = response.data.count || this.categoryList.length;
                        console.log('✅ 成功加载分类列表，共', this.categoryList.length, '个分类');

                        // 打印每个分类的产品数量
                        this.categoryList.forEach(category => {
                            console.log(`分类 ${category.name}: ${category.product_count || 0} 个产品`);
                        });
                    } else {
                        console.warn('⚠️ 分类列表响应格式异常:', response.data);
                        this.categoryList = [];
                        this.categoryPagination.total = 0;
                    }
                })
                .catch(error => {
                    console.error('❌ 轮播图分类接口失败，尝试使用分类管理接口:', error);

                    // 如果轮播图接口失败，使用分类管理接口
                    const params = {
                        page: this.categoryPagination.currentPage,
                        size: this.categoryPagination.pageSize
                    };

                    axios.get('/admin/category/page', { params })
                        .then(response => {
                            console.log('📋 分类管理接口响应:', response.data);

                            if (response.data) {
                                this.categoryList = response.data.data || [];
                                this.categoryPagination.total = response.data.total || 0;
                                console.log('✅ 成功加载分类列表，共', this.categoryList.length, '个分类');

                                // 为每个分类添加产品数量（临时解决方案）
                                this.categoryList.forEach(category => {
                                    // 这里可以调用API获取真实的产品数量，暂时使用0
                                    category.product_count = 0;
                                    this.getProductCountForCategory(category);
                                });
                            } else {
                                this.categoryList = [];
                                this.categoryPagination.total = 0;
                                this.$message.error('获取分类列表失败');
                            }
                        })
                        .catch(error2 => {
                            console.error('❌ 分类管理接口也失败:', error2);
                            this.$message.error('获取分类列表失败');
                            this.categoryList = [];
                            this.categoryPagination.total = 0;
                        });
                });
        },

        // 获取分类的产品数量
        getProductCountForCategory(category) {
            if (!category.id) return;

            axios.get(`/carousel/category/${category.id}/products`, { params: { page: 1, size: 1 } })
                .then(response => {
                    if (response.data && response.data.success) {
                        category.product_count = response.data.total || 0;
                        console.log(`分类 ${category.name}: ${category.product_count} 个产品`);
                        // 触发Vue的响应式更新
                        this.$forceUpdate();
                    }
                })
                .catch(error => {
                    console.warn(`获取分类 ${category.name} 的产品数量失败:`, error);
                    category.product_count = 0;
                });
        },
        // 保存用户信息（新增或更新）
        saveUser() {
            this.$refs.formRef.validate(valid => {
                if (valid) {
                    // 根据是否有userId判断是新增还是更新
                    const url = this.formData.userId ? '/user/updateUser' : '/user/addUser';
                    axios.post(url, this.formData)
                        .then(response => {
                            if (response.data.success) {
                                this.$message.success(this.formData.userId ? '更新成功' : '添加成功');
                                this.dialogVisible = false;
                                this.loadUserList(); // 刷新列表
                            } else {
                                this.$message.error(response.data.message || '操作失败');
                            }
                        })
                        .catch(error => {
                            console.error('操作失败:', error);
                            this.$message.error('操作失败');
                        });
                } else {
                    return false;
                }
            });
        },
        // 获取某分类下的产品数量
        getProductCountByCategory(categoryId) {
            // 这里简化处理，实际项目中应通过API获取
            return Math.floor(Math.random() * 10) + 1;
        },
        // 初始化图表
        initCharts() {
            // 检查图表元素是否存在
            const salesChartElement = document.getElementById('salesChart');
            const categoryChartElement = document.getElementById('categoryChart');

            if (!salesChartElement || !categoryChartElement) {
                console.log('图表元素未找到，跳过初始化');
                return;
            }

            // 如果图表已存在，先销毁
            if (this.salesChart) {
                this.salesChart.destroy();
            }
            if (this.categoryChart) {
                this.categoryChart.destroy();
            }

            // 销售趋势图表
            const salesCtx = salesChartElement.getContext('2d');
            this.salesChart = new Chart(salesCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '销售额',
                        data: [12000, 19000, 16000, 21000, 25000, 30000],
                        borderColor: '#D81B60',
                        backgroundColor: 'rgba(216, 27, 96, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '¥' + value;
                                }
                            }
                        }
                    }
                }
            });
            // 产品分类分布图表
            const categoryCtx = categoryChartElement.getContext('2d');
            this.categoryChart = new Chart(categoryCtx, {
                type: 'doughnut',
                data: {
                    labels: ['护肤品', '彩妆', '香水', '美发', '身体护理'],
                    datasets: [{
                        data: [35, 25, 15, 15, 10],
                        backgroundColor: [
                            '#D81B60',
                            '#8E24AA',
                            '#FF9800',
                            '#4CAF50',
                            '#2196F3'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        }
                    },
                    cutout: '65%'
                }
            });
        },
        // 更新销售图表
        updateSalesChart() {
            // 这里简化处理，实际项目中应通过API获取真实数据
            const months = ['1月', '2月', '3月', '4月', '5月', '6月'];
            const salesData = months.map(() => Math.floor(Math.random() * 30000) + 10000);

            if (this.salesChart) {
                this.salesChart.data.datasets[0].data = salesData;
                this.salesChart.update();
            }
            // 更新月度销售额统计（仅在仪表盘页面存在时）
            const monthlySalesElement = document.getElementById('monthly-sales');
            if (monthlySalesElement) {
                monthlySalesElement.textContent = '¥' + salesData[salesData.length - 1].toLocaleString();
            }
        },
        // 用户分页处理方法
        handleUserSizeChange(size) {
            this.userPagination.pageSize = size;
            this.userPagination.currentPage = 1; // 重置到第一页
            this.loadUserList();
        },
        handleUserCurrentChange(page) {
            this.userPagination.currentPage = page;
            this.loadUserList();
        },
        // 产品分页处理方法
        handleProductSizeChange(size) {
            this.productPagination.pageSize = size;
            this.productPagination.currentPage = 1; // 重置到第一页
            this.loadProductList();
        },
        handleProductCurrentChange(page) {
            this.productPagination.currentPage = page;
            this.loadProductList();
        },
        // 分类分页处理方法
        handleCategorySizeChange(size) {
            this.categoryPagination.pageSize = size;
            this.categoryPagination.currentPage = 1; // 重置到第一页
            this.loadCategoryList();
        },
        handleCategoryCurrentChange(page) {
            this.categoryPagination.currentPage = page;
            this.loadCategoryList();
        },
        // 打开新增用户对话框
        openAddUserDialog() {
            // 清空表单数据
            this.newUser = {
                userId: '',
                username: '',
                phone: '',
                password: '',
                registerTime: new Date().getTime() // 设置当前时间戳
            };
            // 显示新增对话框
            this.addUserDialogVisible = true;
        },
        openAddProductDialog() {
            this.addProductDialogVisible = true;
            this.newProduct = {
                name: '',
                description: '',
                price: 0,
                stock: 0,
                imageUrl: '',
                isNew: false,
                categoryIds: []
            };
            // 清空图片数组
            this.newProductImages = [];
        },
        openAddCategoryDialog() {
            this.addCategoryDialogVisible = true;
            this.newCategory = {
                name: ''
            };
        },
        // 新增用户
        addUser() {
            this.$refs.userForm.validate(valid => {
                if (valid) {
                    // 设置当前时间作为注册时间
                    this.newUser.registerTime = new Date().getTime();

                    console.log('请求数据:', this.newUser); // 调试输出
                    axios.post('/user/register', this.newUser)
                        .then(response => {
                            if (response.data.success) {
                                this.$message.success('新增用户成功');
                                this.addUserDialogVisible = false;
                                this.loadUserList();
                                this.resetUserForm();
                            } else {
                                this.$message.error(response.data.message);
                            }
                        })
                        .catch(error => {
                            console.error('新增用户出错:', error);
                            this.$message.error('新增用户失败，请稍后重试');
                        });
                } else {
                    this.$message.warning('请检查表单数据');
                    return false;
                }
            });
        },
        // 编辑用户
        editUser(user) {
            // 显示编辑对话框
            this.dialogVisible = true;
            // 重置密码显示状态
            this.showPassword = false;
            // 将选中用户的信息复制到编辑表单中
            this.editForm = {
                userId: user.userId,
                username: user.username,
                phone: user.phone,
                password: user.password || '', // 如果密码为空，设置为空字符串
                registerTime: user.registerTime
            };
        },
        // 提交表单（添加/编辑）
        // 保存用户信息的方法
        saveUser() {
            // 使用 Axios 发送 POST 请求到后端的更新用户接口
            axios.post('/user/updateUser', this.editForm)
                .then(response => {
                    if (response.data.success) {
                        // 如果更新成功，更新用户列表中的该用户信息
                        const index = this.userList.findIndex(u => u.userId === this.editForm.userId);
                        if (index !== -1) {
                            this.userList.splice(index, 1, this.editForm);
                        }
                        // 关闭编辑对话框
                        this.dialogVisible = false;
                        // 显示成功提示信息
                        this.$message.success('用户信息更新成功');
                    } else {
                        // 如果更新失败，显示错误提示信息
                        this.$message.error('用户信息更新失败');
                    }
                })
                .catch(error => {
                    // 如果请求出错，显示错误提示信息
                    this.$message.error('更新用户信息时出现错误');
                    console.error(error);
                });
        },
        // 删除用户
        deleteUser(userId) {
            // 使用 Axios 发送 DELETE 请求到后端的删除用户接口
            axios.delete(`/user/deleteUser/${userId}`)
                .then(response => {
                    if (response.data.success) {
                        // 如果删除成功，从用户列表中移除该用户
                        this.userList = this.userList.filter(user => user.userId !== userId);
                        // 显示成功提示信息
                        this.$message.success('用户删除成功');
                    } else {
                        // 如果删除失败，显示错误提示信息
                        this.$message.error(response.data.message);
                    }
                })
                .catch(error => {
                    // 如果请求出错，显示错误提示信息
                    this.$message.error('删除用户时出现错误');
                    console.error(error);
                });
        },
        // 新增产品
        addProduct() {
            this.$refs.productForm.validate((valid) => {
                if (valid) {
                    console.log('🔄 正在新增产品:', this.newProduct);
                    console.log('📸 产品图片数量:', this.newProductImages.length);

                    // 第一步：创建产品
                    axios.post('/products/add', this.newProduct)
                        .then(response => {
                            console.log('📋 产品新增响应:', response.data);

                            // 检查新的JSON响应格式
                            if (response.data && response.data.success) {
                                const productId = response.data.productId;
                                console.log('✅ 产品创建成功，ID:', productId);

                                // 第二步：如果有图片，上传图片
                                if (this.newProductImages.length > 0 && productId) {
                                    this.uploadNewProductImages(productId);
                                } else {
                                    // 没有图片，直接完成
                                    this.$message.success(response.data.message || '产品新增成功');
                                    this.addProductDialogVisible = false;
                                    this.loadProductList();
                                    this.resetProductForm();
                                }
                            } else {
                                const errorMsg = response.data && response.data.message ? response.data.message : '产品新增失败';
                                this.$message.error(errorMsg);
                                console.error('❌ 产品新增失败:', response.data);
                            }
                        })
                        .catch(error => {
                            console.error('❌ 新增产品出错:', error);
                            this.$message.error('产品新增失败，请稍后重试');
                        });
                } else {
                    this.$message.warning('请检查表单数据');
                    return false;
                }
            });
        },

        // 上传新增产品的图片
        uploadNewProductImages(productId) {
            console.log('🔄 开始上传产品图片，产品ID:', productId);

            const loading = this.$loading({
                lock: true,
                text: `正在上传 ${this.newProductImages.length} 张图片...`,
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });

            let uploadedCount = 0;
            let failedCount = 0;
            const totalImages = this.newProductImages.length;

            // 逐个上传图片
            const uploadPromises = this.newProductImages.map((imageItem, index) => {
                const formData = new FormData();
                formData.append('file', imageItem.file);
                formData.append('productId', productId);
                formData.append('imageName', imageItem.imageName);
                formData.append('isPrimary', imageItem.isPrimary ? '1' : '0');
                formData.append('sortOrder', index.toString());

                return axios.post('/products/upload-image', formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    }
                }).then(response => {
                    if (response.data.success) {
                        uploadedCount++;
                        console.log(`✅ 图片 ${imageItem.imageName} 上传成功`);
                    } else {
                        failedCount++;
                        console.error(`❌ 图片 ${imageItem.imageName} 上传失败:`, response.data.message);
                    }
                }).catch(error => {
                    failedCount++;
                    console.error(`❌ 图片 ${imageItem.imageName} 上传出错:`, error);
                });
            });

            // 等待所有图片上传完成
            Promise.allSettled(uploadPromises).then(() => {
                loading.close();

                if (uploadedCount > 0) {
                    this.$message.success(`产品创建成功！成功上传 ${uploadedCount} 张图片`);

                    if (failedCount > 0) {
                        this.$message.warning(`有 ${failedCount} 张图片上传失败，您可以稍后在编辑页面重新上传`);
                    }
                } else {
                    this.$message.warning('产品创建成功，但图片上传失败，您可以稍后在编辑页面上传图片');
                }

                // 完成操作
                this.addProductDialogVisible = false;
                this.loadProductList();
                this.resetProductForm();
            });
        },
        // 编辑产品的方法
        editProduct(product) {
            console.log('🔄 正在编辑产品:', product);

            // 显示编辑产品对话框
            this.editProductDialogVisible = true;
            // 将选中产品的信息复制到编辑产品表单中
            this.editProductForm = {
                id: product.id, // 添加 id 字段
                name: product.name,
                description: product.description,
                price: product.price,
                stock: product.stock || 0,
                imageUrl: product.imageUrl,
                isNew: product.isNew,
                categoryIds: [] // 先设为空数组，稍后异步加载
            };

            // 异步加载产品的分类信息
            this.loadProductCategories(product.id);

            // 清空之前的图片数据
            this.editProductImages = [];

            // 加载产品图片
            this.$nextTick(() => {
                this.loadEditProductImages();
            });

            // 确保表单字段重置后仍能显示数据
            if (this.$refs.editProductForm) {
                this.$refs.editProductForm.resetFields();
                this.$nextTick(() => {
                    Object.keys(this.editProductForm).forEach(key => {
                        if (this.$refs.editProductForm.fields[key]) {
                            this.$refs.editProductForm.fields[key].validate();
                        }
                    });
                });
            }
        },

        // 加载产品的分类信息
        loadProductCategories(productId) {
            console.log('🔄 正在加载产品分类信息:', productId);

            axios.get(`/products/${productId}/categories`)
                .then(response => {
                    console.log('📋 产品分类响应:', response.data);

                    if (response.data && response.data.success) {
                        this.editProductForm.categoryIds = response.data.categoryIds || [];
                        console.log('✅ 成功加载产品分类:', this.editProductForm.categoryIds);
                    } else {
                        console.warn('⚠️ 加载产品分类失败:', response.data);
                        this.editProductForm.categoryIds = [];
                    }
                })
                .catch(error => {
                    console.error('❌ 加载产品分类出错:', error);
                    this.editProductForm.categoryIds = [];
                });
        },

        // 保存产品编辑信息（新增或更新）
        saveProduct() {
            this.$refs.editProductForm.validate((valid) => {
                if (valid) {
                    console.log('🔄 正在更新产品:', this.editProductForm);

                    axios.post('/products/updateProduct', this.editProductForm)
                        .then(response => {
                            console.log('📋 产品更新响应:', response.data);

                            // 检查新的JSON响应格式
                            if (response.data && response.data.success) {
                                this.$message.success(response.data.message || '产品更新成功');
                                this.editProductDialogVisible = false;
                                this.loadProductList();
                            } else {
                                const errorMsg = response.data && response.data.message ? response.data.message : '产品更新失败';
                                this.$message.error(errorMsg);
                                console.error('❌ 产品更新失败:', response.data);
                            }
                        })
                        .catch(error => {
                            console.error('❌ 更新产品出错:', error);
                            this.$message.error('产品更新失败，请稍后重试');
                        });
                } else {
                    this.$message.warning('请检查表单数据');
                    return false;
                }
            });
        },
        // 删除产品
        deleteProduct(id) {
            this.$confirm('确定要删除该产品吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.delete(`/products/${id}`)
                    .then(response => {
                        if (response.data === '删除成功') {
                            this.$message.success('产品删除成功');
                            this.loadProductList(); // 刷新产品列表
                        } else {
                            this.$message.error('产品删除失败');
                        }
                    })
                    .catch(error => {
                        console.error('删除产品出错:', error);
                        this.$message.error('产品删除失败，请稍后重试');
                    });
            }).catch(() => {
                // 用户取消操作
            });
        },
        // 新增分类
        addCategory() {
            this.$refs.categoryForm.validate(valid => {
                if (valid) {
                    axios.post('/admin/category/add', this.newCategory)
                        .then(response => {
                            if (response) {
                                this.$message.success('新增分类成功');
                                this.addCategoryDialogVisible = false;
                                this.loadCategoryList();
                                this.resetCategoryForm();
                            } else {
                                this.$message.error('新增分类失败');
                            }
                        })
                        .catch(error => {
                            console.error('新增分类出错:', error);
                            this.$message.error('新增分类失败');
                        });
                } else {
                    return false;
                }
            });
        },
        // 重置分类表单
        resetCategoryForm() {
            this.newCategory = {
                name: ''
            };
            if (this.$refs.categoryForm) {
                this.$refs.categoryForm.resetFields();
            }
        },
        // 编辑分类
        editCategory(category) {
            // 这里可以实现编辑分类的逻辑
            // 显示编辑分类对话框
            this.editCategoryDialogVisible = true;
            // 将选中分类的信息复制到编辑分类表单中
            this.editCategoryForm = { ...category };
            // 确保表单字段重置后仍能显示数据
            if (this.$refs.editCategoryForm) {
                this.$refs.editCategoryForm.resetFields();
                this.$nextTick(() => {
                    Object.keys(this.editCategoryForm).forEach(key => {
                        if (this.$refs.editCategoryForm.fields[key]) {
                            this.$refs.editCategoryForm.fields[key].validate();
                        }
                    });
                });
            }
        },
        // 保存分类编辑信息
        saveCategory() {
            let url = '';
            let method = '';
            if (this.editCategoryDialogVisible) {
                // 编辑分类
                url = '/admin/category/update';
                method = 'put';
            } else {
                // 新增分类
                url = '/admin/category/add';
                method = 'post';
            }

            axios({
                method: method,
                url: url,
                data: this.editCategoryDialogVisible ? this.editCategoryForm : this.newCategory
            })
                .then(response => {
                    if (response.data) {
                        if (this.editCategoryDialogVisible) {
                            this.$message.success('分类信息更新成功');
                        } else {
                            this.$message.success('新增分类成功');
                        }
                        // 关闭对话框
                        this.editCategoryDialogVisible = false;
                        this.addCategoryDialogVisible = false;
                        // 重新加载分类列表
                        this.loadCategoryList();
                    } else {
                        if (this.editCategoryDialogVisible) {
                            this.$message.error('分类信息更新失败');
                        } else {
                            this.$message.error('新增分类失败');
                        }
                    }
                })
                .catch(error => {
                    console.error('保存分类信息出错:', error);
                    if (this.editCategoryDialogVisible) {
                        this.$message.error('分类信息更新失败');
                    } else {
                        this.$message.error('新增分类失败');
                    }
                });
        },
        // 删除分类
        deleteCategory(categoryId) {
            this.$confirm('确定要删除该分类吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                // 确保 categoryId 是有效的 ID
                axios.delete(`/admin/category/delete/${categoryId}`)
                    .then(response => {
                        if (response.data) {
                            this.$message.success('删除分类成功');
                            this.loadCategoryList(); // 重新加载分类列表
                        } else {
                            this.$message.error('删除分类失败');
                        }
                    })
                    .catch(error => {
                        console.error('删除分类出错:', error);
                        this.$message.error('删除分类失败');
                    });
            }).catch(() => {
                // 用户取消操作
            });
        },
        // 处理文件上传成功后的回调
        handleUploadSuccess(response, file, fileList) {
            if (response.success) {
                if (this.addProductDialogVisible) {
                    this.newProduct.imageUrl = response.imageUrl;
                } else if (this.editProductDialogVisible) {
                    this.editProductForm.imageUrl = response.imageUrl;
                }
                this.$message.success('图片上传成功');
            } else {
                this.$message.error(response.message);
            }
        },
        // 处理新增产品图片上传成功
        handleAddProductUploadSuccess(response, file, fileList) {
            if (response.success) {
                this.newProduct.imageUrl = response.imageUrl;
                this.$message.success('图片上传成功');
            } else {
                this.$message.error(response.message || '图片上传失败');
            }
        },
        // 处理文件上传前的钩子，限制上传文件的类型
        beforeUpload(file) {
            const isImage = ['image/jpeg', 'image/png'].includes(file.type);
            const isLt10M = file.size / 1024 / 1024 < 10;

            if (!isImage) {
                this.$message.error('上传图片只能是 JPG 或 PNG 格式!');
                return false;
            }
            if (!isLt10M) {
                this.$message.error('上传图片大小不能超过 10MB!');
                return false;
            }
            return true;
        },
        // 重置用户表单
        resetUserForm() {
            this.newUser = {
                userId: '',
                username: '',
                phone: '',
                password: '',
                registerTime: new Date().getTime()
            };
            this.$refs.userForm.resetFields();
        },
        // 重置产品表单
        resetProductForm() {
            this.newProduct = {
                name: '',
                description: '',
                price: 0,
                stock: 0,
                imageUrl: '',
                isNew: false,
                categoryIds: []
            };
            // 清空图片数组
            this.newProductImages = [];

            if (this.$refs.productForm) {
                this.$refs.productForm.resetFields();
            }
        },
        // 重置分类表单
        resetCategoryForm() {
            this.newCategory = {
                name: ''
            };
            this.$refs.categoryForm.resetFields();
        },
        // 格式化日期
        formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
        },
        // 格式化日期时间（包含时分秒）
        formatDateTime(timestamp) {
            if (!timestamp) return '-';

            // 处理时间戳格式（可能是秒或毫秒）
            let time = timestamp;
            if (typeof timestamp === 'string') {
                time = parseInt(timestamp);
            }

            // 如果时间戳是秒格式（10位），转换为毫秒
            if (time.toString().length === 10) {
                time = time * 1000;
            }

            const date = new Date(time);

            // 检查日期是否有效
            if (isNaN(date.getTime())) {
                console.warn('无效的时间戳:', timestamp);
                return '时间格式错误';
            }

            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
        },
        // 查看用户详情
        viewUserDetail(user) {
            console.log('查看用户详情:', user);

            // 显示加载状态
            this.currentUserDetail = null;
            this.userDetailDialogVisible = true;

            // 调用API获取用户详情
            axios.get(`/api/user-detail/${user.phone}`)
                .then(response => {
                    console.log('用户详情响应:', response.data);
                    if (response.data.success) {
                        this.currentUserDetail = response.data.data;
                    } else {
                        this.$message.error(response.data.message || '获取用户详情失败');
                        this.userDetailDialogVisible = false;
                    }
                })
                .catch(error => {
                    console.error('获取用户详情失败:', error);
                    this.$message.error('获取用户详情失败');
                    this.userDetailDialogVisible = false;
                });
        },
        // 处理头像加载错误
        handleAvatarError(event) {
            // 使用base64编码的默认头像，避免网络依赖
            console.log('头像加载失败，使用默认头像:', event.target.src);
            // 使用现有的默认头像文件
            event.target.src = '/images/default-avatar.png';
        },



        // ==================== 多图片管理相关方法 ====================

        // 新增产品 - 处理图片选择（支持多张图片）
        handleNewProductImageChange(file, fileList) {
            // 处理单个文件
            const processFile = (fileItem) => {
                const isJPGorPNG = fileItem.raw.type === 'image/jpeg' || fileItem.raw.type === 'image/png';
                const isLt10M = fileItem.raw.size / 1024 / 1024 < 10;

                if (!isJPGorPNG) {
                    this.$message.error(`文件 ${fileItem.name} 格式不支持，只能上传 JPG/PNG 格式的图片!`);
                    return false;
                }
                if (!isLt10M) {
                    this.$message.error(`文件 ${fileItem.name} 大小超过限制，单张图片不能超过 10MB!`);
                    return false;
                }

                // 创建预览URL
                const reader = new FileReader();
                reader.onload = (e) => {
                    this.newProductImages.push({
                        file: fileItem.raw,
                        imageUrl: e.target.result,
                        imageName: fileItem.name,
                        isPrimary: this.newProductImages.length === 0 // 第一张设为主图
                    });

                    // 显示成功消息
                    this.$message.success(`图片 ${fileItem.name} 添加成功`);
                };
                reader.readAsDataURL(fileItem.raw);
                return true;
            };

            // 如果是多个文件，逐个处理
            if (fileList && fileList.length > 1) {
                let successCount = 0;
                fileList.forEach(fileItem => {
                    if (processFile(fileItem)) {
                        successCount++;
                    }
                });
                if (successCount > 0) {
                    this.$message.success(`成功添加 ${successCount} 张图片，第一张将作为产品封面`);
                }
            } else {
                // 单个文件处理
                processFile(file);
            }

            return false; // 阻止自动上传
        },

        // 新增产品 - 删除图片
        deleteNewProductImage(index) {
            const deletedImage = this.newProductImages[index];
            this.newProductImages.splice(index, 1);

            this.$message.success(`图片 ${deletedImage.imageName} 已删除`);

            // 如果删除的是第一张且还有其他图片，重新设置第一张为主图
            if (index === 0 && this.newProductImages.length > 0) {
                this.newProductImages[0].isPrimary = true;
                this.$message.info('已将第一张图片设为新的封面图');
            }
        },

        // 编辑产品 - 加载产品图片
        loadEditProductImages() {
            if (!this.editProductForm.id) return;

            axios.get(`/products/${this.editProductForm.id}/images`)
                .then(response => {
                    if (response.data.success) {
                        this.editProductImages = response.data.data || [];
                        this.$message.success('图片列表刷新成功');
                    } else {
                        this.$message.error('获取图片失败: ' + response.data.message);
                    }
                })
                .catch(error => {
                    console.error('加载图片失败:', error);
                    this.$message.error('加载图片失败');
                });
        },

        // 编辑产品 - 删除图片（带确认和动画效果）
        deleteEditProductImage(imageId) {
            // 找到要删除的图片信息
            const imageToDelete = this.editProductImages.find(img => img.id === imageId);
            const imageName = imageToDelete ? imageToDelete.imageName || '图片' : '图片';

            this.$confirm(`确定要删除图片 "${imageName}" 吗？删除后无法恢复。`, '删除确认', {
                confirmButtonText: '确定删除',
                cancelButtonText: '取消',
                type: 'warning',
                center: true
            }).then(() => {
                // 显示删除中的加载状态
                const loading = this.$loading({
                    lock: true,
                    text: '正在删除图片...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });

                axios.delete(`/api/product-images/${imageId}`)
                    .then(response => {
                        loading.close();
                        if (response.data.success) {
                            this.$message({
                                message: `图片 "${imageName}" 删除成功`,
                                type: 'success',
                                duration: 3000
                            });
                            this.loadEditProductImages(); // 重新加载图片列表
                        } else {
                            this.$message.error('删除失败: ' + response.data.message);
                        }
                    })
                    .catch(error => {
                        loading.close();
                        console.error('删除失败:', error);
                        this.$message.error('删除失败，请稍后重试');
                    });
            }).catch(() => {
                this.$message({
                    message: '已取消删除操作',
                    type: 'info',
                    duration: 2000
                });
            });
        },

        // 编辑产品 - 设置主图（带确认和动画效果）
        setEditProductPrimaryImage(imageId) {
            // 找到要设置为主图的图片信息
            const imageToSetPrimary = this.editProductImages.find(img => img.id === imageId);
            const imageName = imageToSetPrimary ? imageToSetPrimary.imageName || '图片' : '图片';

            this.$confirm(`确定要将图片 "${imageName}" 设为产品封面吗？`, '设置封面确认', {
                confirmButtonText: '确定设置',
                cancelButtonText: '取消',
                type: 'info',
                center: true
            }).then(() => {
                // 显示设置中的加载状态
                const loading = this.$loading({
                    lock: true,
                    text: '正在设置封面...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });

                // 获取图片URL
                const imageUrl = imageToSetPrimary ? imageToSetPrimary.imageUrl : null;

                if (!imageUrl) {
                    loading.close();
                    this.$message.error('无法获取图片URL');
                    return;
                }

                console.log('🔄 设置主图:', {
                    productId: this.editProductForm.id,
                    imageUrl: imageUrl,
                    imageName: imageName
                });

                axios.post(`/products/${this.editProductForm.id}/set-primary-image`, {
                    imageUrl: imageUrl
                })
                    .then(response => {
                        loading.close();
                        console.log('📋 设置主图响应:', response.data);

                        if (response.data.success) {
                            this.$message({
                                message: `图片 "${imageName}" 已设为产品封面`,
                                type: 'success',
                                duration: 3000
                            });
                            this.loadEditProductImages(); // 重新加载图片列表
                            this.loadProductList(); // 重新加载产品列表以更新封面显示
                        } else {
                            this.$message.error('设置封面失败: ' + response.data.message);
                        }
                    })
                    .catch(error => {
                        loading.close();
                        console.error('设置封面失败:', error);
                        this.$message.error('设置封面失败，请稍后重试');
                    });
            }).catch(() => {
                this.$message({
                    message: '已取消设置封面操作',
                    type: 'info',
                    duration: 2000
                });
            });
        },

        // 编辑产品 - 多图片上传成功回调
        handleEditProductMultiUploadSuccess(response, file, fileList) {
            if (response.success) {
                this.$message({
                    message: response.message || `图片 "${file.name}" 上传成功`,
                    type: 'success',
                    duration: 3000
                });

                // 显示上传详情
                if (response.uploadedCount) {
                    this.$notify({
                        title: '上传完成',
                        message: `成功上传 ${response.uploadedCount} 张图片`,
                        type: 'success',
                        duration: 4000
                    });
                }

                // 重新加载图片列表
                setTimeout(() => {
                    this.loadEditProductImages();
                }, 500);
            } else {
                this.$message.error(response.message || `图片 "${file.name}" 上传失败`);
            }
        },

        // 编辑产品 - 多图片上传失败回调
        handleEditProductMultiUploadError(err, file, fileList) {
            console.error('上传失败:', err);
            this.$message({
                message: `图片 "${file.name}" 上传失败，请检查网络连接后重试`,
                type: 'error',
                duration: 4000
            });
        },

        // 编辑产品 - 上传前验证
        beforeEditProductMultiUpload(file) {
            const isJPGorPNG = file.type === 'image/jpeg' || file.type === 'image/png';
            const isLt10M = file.size / 1024 / 1024 < 10;

            if (!isJPGorPNG) {
                this.$message({
                    message: `文件 "${file.name}" 格式不支持，只能上传 JPG/PNG 格式的图片!`,
                    type: 'error',
                    duration: 4000
                });
                return false;
            }
            if (!isLt10M) {
                this.$message({
                    message: `文件 "${file.name}" 大小超过限制，单张图片不能超过 10MB!`,
                    type: 'error',
                    duration: 4000
                });
                return false;
            }

            // 显示上传开始提示
            this.$message({
                message: `开始上传图片 "${file.name}"...`,
                type: 'info',
                duration: 2000
            });

            return true;
        },

        // 处理图片加载错误
        handleImageError(event) {
            console.log('图片加载失败:', event.target.src);
            // 使用实际存在的默认图片
            event.target.src = '/images/074dc74f-ab1e-4241-ad36-40a3dae3234a.jpg';
        },

        // 处理图片加载成功
        handleImageLoad(event) {
            console.log('图片加载成功:', event.target.src);
        },

        // ==================== UI 交互方法 ====================

        // 处理用户菜单悬停效果
        handleUserMenuHover(event) {
            if (event && event.target) {
                event.target.classList.add('user-menu-hover');
            }
        },

        // 处理用户菜单离开效果
        handleUserMenuLeave(event) {
            if (event && event.target) {
                event.target.classList.remove('user-menu-hover');
            }
        },

        // ==================== 轮播图管理相关方法 ====================

        // 加载轮播图列表
        loadCarouselList() {
            axios.get('/carousel/images')
                .then(response => {
                    console.log('📋 轮播图列表响应:', response.data);
                    if (response.data.success) {
                        this.carouselList = response.data.data || [];
                        this.$message.success('轮播图列表加载成功');
                    } else {
                        this.$message.error('获取轮播图列表失败: ' + response.data.message);
                    }
                })
                .catch(error => {
                    console.error('获取轮播图列表失败:', error);
                    this.$message.error('获取轮播图列表失败');
                });
        },

        // 新增轮播图
        addCarousel() {
            if (!this.newCarousel.title) {
                this.$message.error('请输入轮播图标题');
                return;
            }
            if (!this.newCarousel.imageUrl) {
                this.$message.error('请上传轮播图片');
                return;
            }

            const carouselData = {
                name: '轮播图',
                title: this.newCarousel.title,
                description: this.newCarousel.description,
                imageUrl: this.newCarousel.imageUrl,
                categoryId: this.newCarousel.categoryId,
                sortOrder: this.newCarousel.sortOrder || 0
            };

            axios.post('/carousel/add', carouselData)
                .then(response => {
                    console.log('📋 新增轮播图响应:', response.data);
                    if (response.data.success) {
                        this.$message.success('轮播图添加成功');
                        this.addCarouselDialogVisible = false;
                        this.resetNewCarouselForm();
                        this.loadCarouselList();
                    } else {
                        this.$message.error('添加轮播图失败: ' + response.data.message);
                    }
                })
                .catch(error => {
                    console.error('添加轮播图失败:', error);
                    this.$message.error('添加轮播图失败');
                });
        },

        // 编辑轮播图
        editCarousel(carousel) {
            this.editCarouselForm = {
                id: carousel.id,
                title: carousel.title,
                description: carousel.description,
                imageUrl: carousel.image_url || carousel.imageUrl,
                categoryId: carousel.category_id || carousel.categoryId,
                sortOrder: carousel.sort_order || carousel.sortOrder || 0
            };
            this.editCarouselDialogVisible = true;
        },

        // 保存轮播图修改
        saveCarousel() {
            if (!this.editCarouselForm.title) {
                this.$message.error('请输入轮播图标题');
                return;
            }
            if (!this.editCarouselForm.imageUrl) {
                this.$message.error('请上传轮播图片');
                return;
            }

            const carouselData = {
                id: this.editCarouselForm.id,
                name: '轮播图',
                title: this.editCarouselForm.title,
                description: this.editCarouselForm.description,
                imageUrl: this.editCarouselForm.imageUrl,
                categoryId: this.editCarouselForm.categoryId,
                sortOrder: this.editCarouselForm.sortOrder || 0
            };

            axios.put('/carousel/update', carouselData)
                .then(response => {
                    console.log('📋 更新轮播图响应:', response.data);
                    if (response.data.success) {
                        this.$message.success('轮播图更新成功');
                        this.editCarouselDialogVisible = false;
                        this.loadCarouselList();
                    } else {
                        this.$message.error('更新轮播图失败: ' + response.data.message);
                    }
                })
                .catch(error => {
                    console.error('更新轮播图失败:', error);
                    this.$message.error('更新轮播图失败');
                });
        },

        // 删除轮播图
        deleteCarousel(carouselId) {
            this.$confirm('确定要删除这张轮播图吗？删除后无法恢复。', '删除确认', {
                confirmButtonText: '确定删除',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.delete(`/carousel/delete/${carouselId}`)
                    .then(response => {
                        console.log('📋 删除轮播图响应:', response.data);
                        if (response.data.success) {
                            this.$message.success('轮播图删除成功');
                            this.loadCarouselList();
                        } else {
                            this.$message.error('删除轮播图失败: ' + response.data.message);
                        }
                    })
                    .catch(error => {
                        console.error('删除轮播图失败:', error);
                        this.$message.error('删除轮播图失败');
                    });
            }).catch(() => {
                this.$message.info('已取消删除');
            });
        },

        // 切换轮播图状态
        toggleCarouselStatus(carousel) {
            const newStatus = !carousel.is_active;
            const statusText = newStatus ? '启用' : '禁用';

            this.$confirm(`确定要${statusText}这张轮播图吗？`, '状态切换确认', {
                confirmButtonText: `确定${statusText}`,
                cancelButtonText: '取消',
                type: 'info'
            }).then(() => {
                axios.put(`/carousel/toggle-status/${carousel.id}`)
                    .then(response => {
                        console.log('📋 切换轮播图状态响应:', response.data);
                        if (response.data.success) {
                            this.$message.success(`轮播图${statusText}成功`);
                            this.loadCarouselList();
                        } else {
                            this.$message.error(`${statusText}轮播图失败: ` + response.data.message);
                        }
                    })
                    .catch(error => {
                        console.error(`${statusText}轮播图失败:`, error);
                        this.$message.error(`${statusText}轮播图失败`);
                    });
            }).catch(() => {
                this.$message.info('已取消操作');
            });
        },

        // 重置新增轮播图表单
        resetNewCarouselForm() {
            this.newCarousel = {
                title: '',
                description: '',
                imageUrl: '',
                categoryId: null,
                sortOrder: 0
            };
        },

        // 处理轮播图上传成功
        handleCarouselUploadSuccess(response, file, fileList) {
            if (response.success) {
                this.newCarousel.imageUrl = response.imageUrl;
                this.$message.success('图片上传成功');
            } else {
                this.$message.error(response.message || '图片上传失败');
            }
        },

        // 处理编辑轮播图上传成功
        handleEditCarouselUploadSuccess(response, file, fileList) {
            if (response.success) {
                this.editCarouselForm.imageUrl = response.imageUrl;
                this.$message.success('图片上传成功');
            } else {
                this.$message.error(response.message || '图片上传失败');
            }
        },

        // 轮播图上传前验证
        beforeCarouselUpload(file) {
            const isJPGorPNG = file.type === 'image/jpeg' || file.type === 'image/png';
            const isLt2M = file.size / 1024 / 1024 < 2;

            if (!isJPGorPNG) {
                this.$message.error('上传图片只能是 JPG/PNG 格式!');
            }
            if (!isLt2M) {
                this.$message.error('上传图片大小不能超过 2MB!');
            }
            return isJPGorPNG && isLt2M;
        },

        // ==================== 订单管理相关方法 ====================

        // 加载订单列表
        loadOrders() {
            this.orderLoading = true;
            const params = {
                page: this.orderCurrentPage,
                size: this.orderPageSize,
                status: this.orderStatusFilter || undefined
            };

            axios.get('/api/admin/orders', { params })
                .then(response => {
                    if (response.data.success) {
                        this.orderList = response.data.data;
                        this.orderTotal = response.data.total;
                    } else {
                        this.$message.error('获取订单列表失败');
                    }
                })
                .catch(error => {
                    console.error('获取订单列表失败:', error);
                    this.$message.error('网络错误，请重试');
                })
                .finally(() => {
                    this.orderLoading = false;
                });
        },

        // 加载订单统计
        loadOrderStats() {
            axios.get('/api/admin/orders/stats')
                .then(response => {
                    if (response.data.success) {
                        this.orderStats = response.data.data;
                    }
                })
                .catch(error => {
                    console.error('获取订单统计失败:', error);
                });
        },

        // 订单分页处理
        handleOrderSizeChange(size) {
            this.orderPageSize = size;
            this.orderCurrentPage = 1;
            this.loadOrders();
        },

        handleOrderCurrentChange(page) {
            this.orderCurrentPage = page;
            this.loadOrders();
        },

        // 查看订单详情
        viewOrderDetail(order) {
            this.currentOrderDetail = order;
            this.orderDetailDialogVisible = true;
        },

        // 发货
        shipOrder(order) {
            this.$confirm('确定要将此订单标记为已发货吗？', '确认发货', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.post(`/api/admin/orders/${order.orderNo}/ship`)
                    .then(response => {
                        if (response.data.success) {
                            this.$message.success('发货成功');
                            this.loadOrders();
                            this.loadOrderStats();
                        } else {
                            this.$message.error(response.data.message || '发货失败');
                        }
                    })
                    .catch(error => {
                        console.error('发货失败:', error);
                        this.$message.error('网络错误，请重试');
                    });
            });
        },

        // 管理员取消订单
        cancelOrderAdmin(order) {
            this.$confirm('确定要取消此订单吗？', '确认取消', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.post(`/api/admin/orders/${order.orderNo}/cancel`)
                    .then(response => {
                        if (response.data.success) {
                            this.$message.success('订单已取消');
                            this.loadOrders();
                            this.loadOrderStats();
                        } else {
                            this.$message.error(response.data.message || '取消失败');
                        }
                    })
                    .catch(error => {
                        console.error('取消订单失败:', error);
                        this.$message.error('网络错误，请重试');
                    });
            });
        },

        // 获取订单状态文本
        getOrderStatusText(status) {
            const statusMap = {
                1: '待付款',
                2: '待发货',
                3: '待收货',
                4: '已完成',
                5: '已取消'
            };
            return statusMap[status] || '未知状态';
        },

        // 获取订单状态类型（用于标签颜色）
        getOrderStatusType(status) {
            const typeMap = {
                1: 'warning',
                2: 'primary',
                3: 'info',
                4: 'success',
                5: 'danger'
            };
            return typeMap[status] || 'info';
        },

        // 格式化时间
        formatTime(timestamp) {
            if (!timestamp) return '';
            return new Date(timestamp).toLocaleString('zh-CN');
        },

        // ==================== 数据分析相关方法 ====================

        // 加载数据分析数据
        loadAnalysisData() {
            axios.get('/api/admin/analysis/data')
                .then(response => {
                    if (response.data.success) {
                        this.analysisData = response.data.data;
                        // 更新图表数据
                        this.updateChartsWithRealData();
                    }
                })
                .catch(error => {
                    console.error('获取分析数据失败:', error);
                    // 使用模拟数据
                    this.analysisData = {
                        todaySales: '12,345',
                        monthlySales: '456,789',
                        yearlySales: '2,345,678',
                        todayOrders: 23,
                        monthlyOrders: 567,
                        newUsers: 12,
                        activeUsers: 234
                    };
                });
        },

        // 使用真实数据更新图表
        updateChartsWithRealData() {
            if (this.salesChart && this.analysisData.salesTrend) {
                this.salesChart.data.labels = this.analysisData.salesTrend.labels;
                this.salesChart.data.datasets[0].data = this.analysisData.salesTrend.values;
                this.salesChart.update();
            }

            if (this.categoryChart && this.analysisData.categoryDistribution) {
                this.categoryChart.data.datasets[0].data = this.analysisData.categoryDistribution.values;
                this.categoryChart.data.labels = this.analysisData.categoryDistribution.labels;
                this.categoryChart.update();
            }
        },

        // 更新销售图表
        updateSalesChart() {
            axios.get('/api/admin/analysis/sales-trend?months=6')
                .then(response => {
                    if (response.data.success && this.salesChart) {
                        const data = response.data.data;
                        this.salesChart.data.labels = data.labels;
                        this.salesChart.data.datasets[0].data = data.values;
                        this.salesChart.update();
                    }
                })
                .catch(error => {
                    console.error('更新销售图表失败:', error);
                });
        },

        // ==================== 产品管理增强功能 ====================

        // 搜索产品
        searchProducts() {
            // 搜索功能通过computed属性filteredProductList自动实现
            console.log('搜索关键词:', this.productSearchKeyword);
        },

        // 按分类筛选产品
        filterProductsByCategory() {
            console.log('筛选分类:', this.productCategoryFilter);
        },

        // 按库存状态筛选产品
        filterProductsByStock() {
            console.log('筛选库存状态:', this.productStockFilter);
        },

        // 重置产品筛选条件
        resetProductFilters() {
            this.productSearchKeyword = '';
            this.productCategoryFilter = '';
            this.productStockFilter = '';
            this.$message.success('筛选条件已重置');
        },

        // 处理产品选择变化
        handleProductSelectionChange(selection) {
            this.selectedProducts = selection;
        },

        // 批量删除产品
        batchDeleteProducts() {
            if (this.selectedProducts.length === 0) {
                this.$message.warning('请先选择要删除的产品');
                return;
            }

            this.$confirm(`确定要删除选中的 ${this.selectedProducts.length} 个产品吗？`, '批量删除确认', {
                confirmButtonText: '确定删除',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const productIds = this.selectedProducts.map(product => product.id);

                axios.post('/products/batch-delete', { productIds })
                    .then(response => {
                        if (response.data.success) {
                            this.$message.success(`成功删除 ${this.selectedProducts.length} 个产品`);
                            this.selectedProducts = [];
                            this.loadProductList();
                        } else {
                            this.$message.error(response.data.message || '批量删除失败');
                        }
                    })
                    .catch(error => {
                        console.error('批量删除失败:', error);
                        this.$message.error('网络错误，请重试');
                    });
            });
        },

        // 导出产品数据
        exportProducts() {
            this.$message.info('导出功能开发中...');
            // TODO: 实现产品数据导出功能
        },

        // 查看产品详情
        viewProductDetail(product) {
            this.currentProductDetail = { ...product };
            this.productDetailDialogVisible = true;

            // 加载产品的详细信息（包括图片、分类等）
            this.loadProductDetailInfo(product.id);
        },

        // 加载产品详细信息
        loadProductDetailInfo(productId) {
            axios.get(`/products/detail/${productId}`)
                .then(response => {
                    if (response.data.success) {
                        this.currentProductDetail = { ...this.currentProductDetail, ...response.data.data };
                    }
                })
                .catch(error => {
                    console.error('加载产品详情失败:', error);
                });
        },

        // 切换产品新品状态
        toggleProductNewStatus(product) {
            const newStatus = product.isNew;
            axios.post(`/products/${product.id}/toggle-new`, { isNew: newStatus })
                .then(response => {
                    if (response.data.success) {
                        this.$message.success(`产品${newStatus ? '已设为' : '已取消'}新品`);
                        this.loadProductList();
                    } else {
                        // 如果失败，恢复原状态
                        product.isNew = !newStatus;
                        this.$message.error(response.data.message || '操作失败');
                    }
                })
                .catch(error => {
                    // 如果失败，恢复原状态
                    product.isNew = !newStatus;
                    console.error('切换新品状态失败:', error);
                    this.$message.error('网络错误，请重试');
                });
        },

        // 处理产品操作（更多菜单）
        handleProductAction(command) {
            const { action, row } = command;

            switch (action) {
                case 'toggleStatus':
                    this.toggleProductStatus(row);
                    break;
                case 'copy':
                    this.copyProduct(row);
                    break;
                case 'delete':
                    this.deleteProduct(row.id);
                    break;
            }
        },

        // 切换产品上下架状态
        toggleProductStatus(product) {
            const newStatus = product.status === 1 ? 0 : 1;
            const statusText = newStatus === 1 ? '上架' : '下架';

            this.$confirm(`确定要${statusText}此产品吗？`, '状态切换确认', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.post(`/products/${product.id}/toggle-status`, { status: newStatus })
                    .then(response => {
                        if (response.data.success) {
                            this.$message.success(`产品已${statusText}`);
                            this.loadProductList();
                        } else {
                            this.$message.error(response.data.message || '操作失败');
                        }
                    })
                    .catch(error => {
                        console.error('切换产品状态失败:', error);
                        this.$message.error('网络错误，请重试');
                    });
            });
        },

        // 复制产品
        copyProduct(product) {
            this.$confirm('确定要复制此产品吗？', '复制产品确认', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'info'
            }).then(() => {
                const copyData = {
                    ...product,
                    name: product.name + ' (副本)',
                    id: undefined // 移除ID，让后端生成新ID
                };

                axios.post('/products/copy', copyData)
                    .then(response => {
                        if (response.data.success) {
                            this.$message.success('产品复制成功');
                            this.loadProductList();
                        } else {
                            this.$message.error(response.data.message || '复制失败');
                        }
                    })
                    .catch(error => {
                        console.error('复制产品失败:', error);
                        this.$message.error('网络错误，请重试');
                    });
            });
        },

        // ==================== 产品图片管理功能 ====================

        // 预览产品图片
        previewProductImages(product) {
            this.currentProductForImages = product;
            this.productImageDialogVisible = true;
            this.loadProductImages(product.id);
        },

        // 管理产品图片
        manageProductImages(product) {
            this.currentProductForImages = product;
            this.productImageDialogVisible = true;
            this.loadProductImages(product.id);
        },

        // 加载产品图片列表
        loadProductImages(productId) {
            axios.get(`/api/product-images/product/${productId}`)
                .then(response => {
                    if (response.data.success) {
                        this.productImages = response.data.data;
                    } else {
                        this.productImages = [];
                        console.error('加载产品图片失败:', response.data.message);
                    }
                })
                .catch(error => {
                    this.productImages = [];
                    console.error('加载产品图片失败:', error);
                });
        },

        // 图片上传前的验证
        beforeImageUpload(file) {
            const isImage = file.type.startsWith('image/');
            const isLt2M = file.size / 1024 / 1024 < 2;

            if (!isImage) {
                this.$message.error('只能上传图片文件!');
                return false;
            }
            if (!isLt2M) {
                this.$message.error('图片大小不能超过 2MB!');
                return false;
            }
            return true;
        },

        // 图片上传成功回调
        handleImageUploadSuccess(response, file) {
            if (response.success) {
                this.$message.success('图片上传成功');
                this.refreshProductImages();
            } else {
                this.$message.error(response.message || '图片上传失败');
            }
        },

        // 设置主图
        setPrimaryImage(image) {
            axios.post(`/api/product-images/${image.id}/set-primary`)
                .then(response => {
                    if (response.data.success) {
                        this.$message.success('主图设置成功');
                        this.refreshProductImages();
                        this.loadProductList(); // 刷新产品列表以更新主图显示
                    } else {
                        this.$message.error(response.data.message || '设置主图失败');
                    }
                })
                .catch(error => {
                    console.error('设置主图失败:', error);
                    this.$message.error('网络错误，请重试');
                });
        },

        // 删除产品图片
        deleteProductImage(image) {
            this.$confirm('确定要删除这张图片吗？', '删除图片确认', {
                confirmButtonText: '确定删除',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.delete(`/api/product-images/${image.id}`)
                    .then(response => {
                        if (response.data.success) {
                            this.$message.success('图片删除成功');
                            this.refreshProductImages();
                            this.loadProductList(); // 刷新产品列表
                        } else {
                            this.$message.error(response.data.message || '删除失败');
                        }
                    })
                    .catch(error => {
                        console.error('删除图片失败:', error);
                        this.$message.error('网络错误，请重试');
                    });
            });
        },

        // 刷新产品图片列表
        refreshProductImages() {
            if (this.currentProductForImages) {
                this.loadProductImages(this.currentProductForImages.id);
            }
        },

        // 预览图片
        previewImage(imageUrl) {
            console.log('🖼️ 预览图片:', imageUrl);
            this.previewImageUrl = this.getImageUrl(imageUrl);
            this.imagePreviewDialogVisible = true;
        },

        // 切换产品详情主图
        changeMainImage(imageUrl) {
            if (this.currentProductDetail) {
                this.currentProductDetail.primary_image_url = imageUrl;
            }
        },

        // 格式化文件大小
        formatFileSize(bytes) {
            if (!bytes) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },

        // 格式化日期时间
        formatDateTime(timestamp) {
            if (!timestamp) return '-';

            // 处理时间戳格式（可能是秒或毫秒）
            let time = timestamp;
            if (typeof timestamp === 'string') {
                time = parseInt(timestamp);
            }

            // 如果时间戳是秒格式（10位），转换为毫秒
            if (time.toString().length === 10) {
                time = time * 1000;
            }

            const date = new Date(time);

            // 检查日期是否有效
            if (isNaN(date.getTime())) {
                console.warn('无效的时间戳:', timestamp);
                return '时间格式错误';
            }

            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
        },

        // 格式化日期
        formatDate(date) {
            if (!date) return '未知';
            return date.toLocaleString('zh-CN');
        },

        // 获取随机颜色（用于分类标签）
        getRandomColor() {
            const colors = ['#ff9999', '#99ccff', '#99ff99', '#ffcc99', '#cc99ff', '#ffff99'];
            return colors[Math.floor(Math.random() * colors.length)];
        },

        // 获取正确的图片URL
        getImageUrl(imageUrl) {
            if (!imageUrl) {
                return '/images/default-product.svg';
            }

            // 如果已经是完整的URL，直接返回
            if (imageUrl.startsWith('http') || imageUrl.startsWith('/images/')) {
                return imageUrl;
            }

            // 否则添加/images/前缀
            return `/images/${imageUrl}`;
        },

        // 处理图片加载错误
        handleImageError(event) {
            console.log('图片加载失败，使用默认图片:', event.target.src);
            event.target.src = '/images/default-product.svg';
        },

        // ==================== 收藏管理相关方法 ====================

        // 加载收藏列表
        loadFavorites() {
            this.favoriteLoading = true;
            const params = {
                page: this.favoritePagination.currentPage,
                size: this.favoritePagination.pageSize
            };

            // 添加搜索条件
            if (this.favoriteSearchForm.keyword) {
                params.keyword = this.favoriteSearchForm.keyword;
            }
            if (this.favoriteSearchForm.category) {
                params.category = this.favoriteSearchForm.category;
            }
            if (this.favoriteSearchForm.dateRange && this.favoriteSearchForm.dateRange.length === 2) {
                params.startDate = this.formatDate(this.favoriteSearchForm.dateRange[0]);
                params.endDate = this.formatDate(this.favoriteSearchForm.dateRange[1]);
            }

            axios.get('/api/favorites/admin/all', { params })
                .then(response => {
                    if (response.data.success) {
                        this.favoriteList = response.data.data || [];
                        this.favoritePagination.total = response.data.total || 0;
                    } else {
                        this.$message.error('加载收藏列表失败');
                    }
                })
                .catch(error => {
                    console.error('加载收藏列表失败:', error);
                    this.$message.error('网络错误，请重试');
                })
                .finally(() => {
                    this.favoriteLoading = false;
                });
        },

        // 加载收藏统计
        loadFavoriteStats() {
            axios.get('/api/favorites/admin/stats')
                .then(response => {
                    if (response.data.success) {
                        this.favoriteStats = response.data.data || {};
                    }
                })
                .catch(error => {
                    console.error('加载收藏统计失败:', error);
                });
        },

        // 收藏搜索输入防抖
        onFavoriteSearchInput() {
            if (this.favoriteSearchTimer) {
                clearTimeout(this.favoriteSearchTimer);
            }
            this.favoriteSearchTimer = setTimeout(() => {
                this.searchFavorites();
            }, 500);
        },

        // 搜索收藏
        searchFavorites() {
            this.favoritePagination.currentPage = 1;
            this.loadFavorites();
        },

        // 重置收藏搜索
        resetFavoriteSearch() {
            this.favoriteSearchForm = {
                keyword: '',
                category: '',
                dateRange: null
            };
            this.searchFavorites();
        },

        // 收藏分页大小改变
        handleFavoriteSizeChange(size) {
            this.favoritePagination.pageSize = size;
            this.favoritePagination.currentPage = 1;
            this.loadFavorites();
        },

        // 收藏当前页改变
        handleFavoriteCurrentChange(page) {
            this.favoritePagination.currentPage = page;
            this.loadFavorites();
        },

        // 查看收藏详情
        viewFavoriteDetail(favorite) {
            this.$message.info('收藏详情功能开发中...');
        },

        // 删除收藏
        deleteFavorite(favoriteId) {
            this.$confirm('确定要删除这条收藏记录吗？', '删除确认', {
                confirmButtonText: '确定删除',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.delete(`/api/favorites/admin/${favoriteId}`)
                    .then(response => {
                        if (response.data.success) {
                            this.$message.success('删除成功');
                            this.loadFavorites();
                            this.loadFavoriteStats();
                        } else {
                            this.$message.error(response.data.message || '删除失败');
                        }
                    })
                    .catch(error => {
                        console.error('删除收藏失败:', error);
                        this.$message.error('网络错误，请重试');
                    });
            });
        },

        // 导出收藏数据
        exportFavorites() {
            this.$message.info('导出功能开发中...');
        },

        // 查看热门商品
        viewPopularProducts() {
            this.$message.info('热门商品分析功能开发中...');
        },

        // ==================== 评论管理相关方法 ====================

        // 加载评论列表
        loadComments() {
            this.commentLoading = true;
            const params = {
                page: this.commentPagination.currentPage,
                size: this.commentPagination.pageSize
            };

            // 添加搜索条件
            if (this.commentSearchForm.keyword) {
                params.keyword = this.commentSearchForm.keyword;
            }
            if (this.commentSearchForm.status !== '') {
                params.status = this.commentSearchForm.status;
            }
            if (this.commentSearchForm.rating) {
                params.rating = this.commentSearchForm.rating;
            }
            if (this.commentSearchForm.dateRange && this.commentSearchForm.dateRange.length === 2) {
                params.startDate = this.formatDate(this.commentSearchForm.dateRange[0]);
                params.endDate = this.formatDate(this.commentSearchForm.dateRange[1]);
            }

            axios.get('/api/comments/admin/all', { params })
                .then(response => {
                    if (response.data.success) {
                        this.commentList = response.data.data || [];
                        this.commentPagination.total = response.data.total || 0;
                    } else {
                        this.$message.error('加载评论列表失败');
                    }
                })
                .catch(error => {
                    console.error('加载评论列表失败:', error);
                    this.$message.error('网络错误，请重试');
                })
                .finally(() => {
                    this.commentLoading = false;
                });
        },

        // 加载评论统计
        loadCommentStats() {
            axios.get('/api/comments/admin/stats')
                .then(response => {
                    if (response.data.success) {
                        this.commentStats = response.data.data || {};
                    }
                })
                .catch(error => {
                    console.error('加载评论统计失败:', error);
                });
        },

        // 评论搜索输入防抖
        onCommentSearchInput() {
            if (this.commentSearchTimer) {
                clearTimeout(this.commentSearchTimer);
            }
            this.commentSearchTimer = setTimeout(() => {
                this.searchComments();
            }, 500);
        },

        // 搜索评论
        searchComments() {
            this.commentPagination.currentPage = 1;
            this.loadComments();
        },

        // 重置评论搜索
        resetCommentSearch() {
            this.commentSearchForm = {
                keyword: '',
                status: '',
                rating: '',
                dateRange: null
            };
            this.searchComments();
        },

        // 评论分页大小改变
        handleCommentSizeChange(size) {
            this.commentPagination.pageSize = size;
            this.commentPagination.currentPage = 1;
            this.loadComments();
        },

        // 评论当前页改变
        handleCommentCurrentChange(page) {
            this.commentPagination.currentPage = page;
            this.loadComments();
        },

        // 评论选择改变
        handleCommentSelectionChange(selection) {
            this.selectedComments = selection;
        },

        // 通过评论
        approveComment(commentId) {
            this.updateCommentStatus(commentId, 1, '通过');
        },

        // 拒绝评论
        rejectComment(commentId) {
            this.updateCommentStatus(commentId, 2, '拒绝');
        },

        // 更新评论状态
        updateCommentStatus(commentId, status, action) {
            axios.post('/api/comments/admin/status', {
                id: commentId,
                status: status
            })
            .then(response => {
                if (response.data.success) {
                    this.$message.success(`${action}成功`);
                    this.loadComments();
                    this.loadCommentStats();
                } else {
                    this.$message.error(response.data.message || `${action}失败`);
                }
            })
            .catch(error => {
                console.error(`${action}评论失败:`, error);
                this.$message.error('网络错误，请重试');
            });
        },

        // 批量通过评论
        batchApproveComments() {
            if (this.selectedComments.length === 0) {
                this.$message.warning('请选择要通过的评论');
                return;
            }

            this.$confirm(`确定要通过选中的 ${this.selectedComments.length} 条评论吗？`, '批量通过确认', {
                confirmButtonText: '确定通过',
                cancelButtonText: '取消',
                type: 'info'
            }).then(() => {
                const promises = this.selectedComments.map(comment =>
                    axios.post('/api/comments/admin/status', {
                        id: comment.id,
                        status: 1
                    })
                );

                Promise.all(promises)
                    .then(() => {
                        this.$message.success('批量通过成功');
                        this.loadComments();
                        this.loadCommentStats();
                    })
                    .catch(error => {
                        console.error('批量通过失败:', error);
                        this.$message.error('批量通过失败');
                    });
            });
        },

        // 批量拒绝评论
        batchRejectComments() {
            if (this.selectedComments.length === 0) {
                this.$message.warning('请选择要拒绝的评论');
                return;
            }

            this.$confirm(`确定要拒绝选中的 ${this.selectedComments.length} 条评论吗？`, '批量拒绝确认', {
                confirmButtonText: '确定拒绝',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const promises = this.selectedComments.map(comment =>
                    axios.post('/api/comments/admin/status', {
                        id: comment.id,
                        status: 2
                    })
                );

                Promise.all(promises)
                    .then(() => {
                        this.$message.success('批量拒绝成功');
                        this.loadComments();
                        this.loadCommentStats();
                    })
                    .catch(error => {
                        console.error('批量拒绝失败:', error);
                        this.$message.error('批量拒绝失败');
                    });
            });
        },

        // 查看评论详情
        viewCommentDetail(comment) {
            console.log('查看评论详情:', comment);
            this.currentCommentDetail = comment;

            // 处理评论图片
            if (comment.imageUrls && typeof comment.imageUrls === 'string') {
                try {
                    this.currentCommentDetail.imageUrls = JSON.parse(comment.imageUrls);
                } catch (e) {
                    this.currentCommentDetail.imageUrls = [];
                }
            } else if (!comment.imageUrls) {
                this.currentCommentDetail.imageUrls = [];
            }

            this.commentDetailDialogVisible = true;
        },

        // 从详情页通过评论
        approveCommentFromDetail() {
            if (this.currentCommentDetail) {
                this.approveComment(this.currentCommentDetail.id);
                this.commentDetailDialogVisible = false;
            }
        },

        // 从详情页拒绝评论
        rejectCommentFromDetail() {
            if (this.currentCommentDetail) {
                this.rejectComment(this.currentCommentDetail.id);
                this.commentDetailDialogVisible = false;
            }
        },

        // 从详情页删除评论
        deleteCommentFromDetail() {
            if (this.currentCommentDetail) {
                this.deleteComment(this.currentCommentDetail.id);
                this.commentDetailDialogVisible = false;
            }
        },

        // 预览图片
        previewImage(imageUrl) {
            // 创建图片预览模态框
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
                cursor: pointer;
            `;

            const img = document.createElement('img');
            img.src = imageUrl;
            img.style.cssText = `
                max-width: 90%;
                max-height: 90%;
                object-fit: contain;
            `;

            modal.appendChild(img);
            document.body.appendChild(modal);

            modal.onclick = () => {
                document.body.removeChild(modal);
            };
        },

        // 获取评论状态类型
        getCommentStatusType(status) {
            const typeMap = {
                0: 'warning',
                1: 'success',
                2: 'danger'
            };
            return typeMap[status] || 'info';
        },

        // 删除评论
        deleteComment(commentId) {
            this.$confirm('确定要删除这条评论吗？', '删除确认', {
                confirmButtonText: '确定删除',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.delete(`/api/comments/admin/${commentId}`)
                    .then(response => {
                        if (response.data.success) {
                            this.$message.success('删除成功');
                            this.loadComments();
                            this.loadCommentStats();
                        } else {
                            this.$message.error(response.data.message || '删除失败');
                        }
                    })
                    .catch(error => {
                        console.error('删除评论失败:', error);
                        this.$message.error('网络错误，请重试');
                    });
            });
        },

        // 导出评论数据
        exportComments() {
            this.$message.info('导出功能开发中...');
        },

        // 获取评论状态类型
        getCommentStatusType(status) {
            const typeMap = {
                0: 'warning',  // 待审核
                1: 'success',  // 已通过
                2: 'danger'    // 已拒绝
            };
            return typeMap[status] || 'info';
        },

        // 获取评论状态文本
        getCommentStatusText(status) {
            const textMap = {
                0: '待审核',
                1: '已通过',
                2: '已拒绝'
            };
            return textMap[status] || '未知';
        },

        // ==================== 分享管理相关方法 ====================

        // 加载分享列表
        loadShares() {
            this.shareLoading = true;
            const params = {
                page: this.sharePagination.currentPage,
                size: this.sharePagination.pageSize
            };

            // 添加搜索条件
            if (this.shareSearchForm.keyword) {
                params.keyword = this.shareSearchForm.keyword;
            }
            if (this.shareSearchForm.shareType) {
                params.shareType = this.shareSearchForm.shareType;
            }
            if (this.shareSearchForm.dateRange && this.shareSearchForm.dateRange.length === 2) {
                params.startDate = this.formatDate(this.shareSearchForm.dateRange[0]);
                params.endDate = this.formatDate(this.shareSearchForm.dateRange[1]);
            }

            axios.get('/api/shares/admin/all', { params })
                .then(response => {
                    if (response.data.success) {
                        this.shareList = response.data.data || [];
                        this.sharePagination.total = response.data.total || 0;
                    } else {
                        this.$message.error('加载分享列表失败');
                    }
                })
                .catch(error => {
                    console.error('加载分享列表失败:', error);
                    this.$message.error('网络错误，请重试');
                })
                .finally(() => {
                    this.shareLoading = false;
                });
        },

        // 加载分享统计
        loadShareStats() {
            axios.get('/api/shares/stats')
                .then(response => {
                    if (response.data.success) {
                        const stats = response.data.data || [];
                        this.shareStats = {
                            total: stats.reduce((sum, item) => sum + item.count, 0),
                            wechat: stats.find(item => item.shareType === 'wechat')?.count || 0,
                            weibo: stats.find(item => item.shareType === 'weibo')?.count || 0,
                            qq: stats.find(item => item.shareType === 'qq')?.count || 0
                        };
                    }
                })
                .catch(error => {
                    console.error('加载分享统计失败:', error);
                });
        },

        // 分享搜索输入防抖
        onShareSearchInput() {
            if (this.shareSearchTimer) {
                clearTimeout(this.shareSearchTimer);
            }
            this.shareSearchTimer = setTimeout(() => {
                this.searchShares();
            }, 500);
        },

        // 搜索分享
        searchShares() {
            this.sharePagination.currentPage = 1;
            this.loadShares();
        },

        // 重置分享搜索
        resetShareSearch() {
            this.shareSearchForm = {
                keyword: '',
                shareType: '',
                dateRange: null
            };
            this.searchShares();
        },

        // 分享分页大小改变
        handleShareSizeChange(size) {
            this.sharePagination.pageSize = size;
            this.sharePagination.currentPage = 1;
            this.loadShares();
        },

        // 分享当前页改变
        handleShareCurrentChange(page) {
            this.sharePagination.currentPage = page;
            this.loadShares();
        },

        // 查看分享详情
        viewShareDetail(share) {
            this.$message.info('分享详情功能开发中...');
        },

        // 删除分享
        deleteShare(shareId) {
            this.$confirm('确定要删除这条分享记录吗？', '删除确认', {
                confirmButtonText: '确定删除',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.delete(`/api/shares/admin/${shareId}`)
                    .then(response => {
                        if (response.data.success) {
                            this.$message.success('删除成功');
                            this.loadShares();
                            this.loadShareStats();
                        } else {
                            this.$message.error(response.data.message || '删除失败');
                        }
                    })
                    .catch(error => {
                        console.error('删除分享失败:', error);
                        this.$message.error('网络错误，请重试');
                    });
            });
        },

        // 导出分享数据
        exportShares() {
            this.$message.info('导出功能开发中...');
        },

        // 查看分享统计
        viewShareStats() {
            this.$message.info('分享统计分析功能开发中...');
        },

        // 获取分享类型颜色
        getShareTypeColor(shareType) {
            const colorMap = {
                'wechat': 'success',
                'weibo': 'danger',
                'qq': 'primary',
                'link': 'info'
            };
            return colorMap[shareType] || 'info';
        },

        // 获取分享类型文本
        getShareTypeText(shareType) {
            const textMap = {
                'wechat': '微信',
                'weibo': '微博',
                'qq': 'QQ',
                'link': '复制链接'
            };
            return textMap[shareType] || shareType;
        },

        // ==================== 系统设置相关方法 ====================

        // 加载系统设置
        loadSystemSettings() {
            axios.get('/api/admin/settings')
                .then(response => {
                    if (response.data.success) {
                        this.systemSettings = response.data.data;
                    } else {
                        this.$message.error('加载系统设置失败');
                    }
                })
                .catch(error => {
                    console.error('加载系统设置失败:', error);
                    this.$message.error('网络错误，请重试');
                });
        },

        // 保存系统设置
        saveSystemSettings() {
            axios.post('/api/admin/settings', this.systemSettings)
                .then(response => {
                    if (response.data.success) {
                        this.$message.success('系统设置保存成功');
                    } else {
                        this.$message.error(response.data.message || '保存失败');
                    }
                })
                .catch(error => {
                    console.error('保存系统设置失败:', error);
                    this.$message.error('网络错误，请重试');
                });
        },

        // 重置系统设置
        resetSystemSettings() {
            this.$confirm('确定要重置系统设置吗？', '重置确认', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.post('/api/admin/settings/reset')
                    .then(response => {
                        if (response.data.success) {
                            this.$message.success('系统设置已重置');
                            this.loadSystemSettings(); // 重新加载设置
                        } else {
                            this.$message.error(response.data.message || '重置失败');
                        }
                    })
                    .catch(error => {
                        console.error('重置系统设置失败:', error);
                        this.$message.error('网络错误，请重试');
                    });
            });
        },

        // 获取头像URL
        getAvatarUrl(avatarUrl) {
            if (!avatarUrl) {
                return '/images/default-avatar.png';
            }

            // 如果已经是完整URL，直接返回
            if (avatarUrl.startsWith('http') || avatarUrl.startsWith('/')) {
                return avatarUrl;
            }

            // 如果是用户上传的头像文件名
            if (avatarUrl.startsWith('avatar_')) {
                return `/images/avatars/${avatarUrl}`;
            }

            // 如果是预设头像文件名
            return `/images/avatar/${avatarUrl}`;
        },

        // ==================== 日志管理相关方法 ====================

        // 加载日志统计
        loadLogStats() {
            axios.get('/api/admin/logs/stats')
                .then(response => {
                    if (response.data.success) {
                        this.logStats = response.data.data;
                    }
                })
                .catch(error => {
                    console.error('加载日志统计失败:', error);
                });
        },

        // 加载日志列表
        loadLogs() {
            this.logLoading = true;
            const params = {
                page: this.logPagination.currentPage,
                size: this.logPagination.pageSize,
                ...this.logSearchForm
            };

            axios.get('/api/admin/logs', { params })
                .then(response => {
                    if (response.data.success) {
                        this.logList = response.data.data.list || [];
                        this.logPagination.total = response.data.data.total || 0;
                    } else {
                        this.$message.error('加载日志失败');
                    }
                })
                .catch(error => {
                    console.error('加载日志失败:', error);
                    this.$message.error('网络错误，请重试');
                })
                .finally(() => {
                    this.logLoading = false;
                });
        },

        // 日志搜索输入防抖
        onLogSearchInput() {
            if (this.logSearchTimer) {
                clearTimeout(this.logSearchTimer);
            }
            this.logSearchTimer = setTimeout(() => {
                this.searchLogs();
            }, 500);
        },

        // 搜索日志
        searchLogs() {
            this.logPagination.currentPage = 1;
            this.loadLogs();
        },

        // 重置日志搜索
        resetLogSearch() {
            this.logSearchForm = {
                level: '',
                action: '',
                keyword: '',
                dateRange: null
            };
            this.searchLogs();
        },

        // 日志分页大小改变
        handleLogSizeChange(size) {
            this.logPagination.pageSize = size;
            this.logPagination.currentPage = 1;
            this.loadLogs();
        },

        // 日志当前页改变
        handleLogCurrentChange(page) {
            this.logPagination.currentPage = page;
            this.loadLogs();
        },

        // 查看日志详情
        viewLogDetail(log) {
            this.$alert(JSON.stringify(log, null, 2), '日志详情', {
                confirmButtonText: '确定',
                customClass: 'log-detail-dialog'
            });
        },

        // 删除日志
        deleteLog(logId) {
            this.$confirm('确定要删除这条日志吗？', '删除确认', {
                confirmButtonText: '确定删除',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.delete(`/api/admin/logs/${logId}`)
                    .then(response => {
                        if (response.data.success) {
                            this.$message.success('删除成功');
                            this.loadLogs();
                            this.loadLogStats();
                        } else {
                            this.$message.error(response.data.message || '删除失败');
                        }
                    })
                    .catch(error => {
                        console.error('删除日志失败:', error);
                        this.$message.error('网络错误，请重试');
                    });
            });
        },

        // 导出日志
        exportLogs() {
            const params = new URLSearchParams(this.logSearchForm);
            window.open(`/api/admin/logs/export?${params.toString()}`, '_blank');
        },

        // 清理旧日志
        clearOldLogs() {
            this.$confirm('确定要清理30天前的旧日志吗？', '清理确认', {
                confirmButtonText: '确定清理',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.post('/api/admin/logs/clear')
                    .then(response => {
                        if (response.data.success) {
                            this.$message.success('清理完成');
                            this.loadLogs();
                            this.loadLogStats();
                        } else {
                            this.$message.error(response.data.message || '清理失败');
                        }
                    })
                    .catch(error => {
                        console.error('清理日志失败:', error);
                        this.$message.error('网络错误，请重试');
                    });
            });
        },

        // 刷新日志
        refreshLogs() {
            this.loadLogs();
            this.loadLogStats();
        },

        // 获取日志级别类型
        getLogLevelType(level) {
            const typeMap = {
                'INFO': 'success',
                'WARN': 'warning',
                'ERROR': 'danger',
                'DEBUG': 'info'
            };
            return typeMap[level] || 'info';
        },

        // 获取操作类型
        getActionType(action) {
            const typeMap = {
                'LOGIN': 'primary',
                'CREATE': 'success',
                'UPDATE': 'warning',
                'DELETE': 'danger',
                'SELECT': 'info'
            };
            return typeMap[action] || 'info';
        },

        // 获取日志类型
        getLogType(action) {
            const typeMap = {
                '新增': 'success',
                '编辑': 'warning',
                '删除': 'danger',
                '查看': 'info',
                '登录': 'primary'
            };
            return typeMap[action] || 'info';
        },

        // ==================== 备份管理相关方法 ====================

        // 加载备份统计
        loadBackupStats() {
            axios.get('/api/admin/backup/stats')
                .then(response => {
                    if (response.data.success) {
                        this.backupStats = response.data.data;
                    }
                })
                .catch(error => {
                    console.error('加载备份统计失败:', error);
                });
        },

        // 加载备份列表
        loadBackupList() {
            this.backupListLoading = true;
            const params = {
                page: this.backupPagination.currentPage,
                size: this.backupPagination.pageSize
            };

            axios.get('/api/admin/backup/list', { params })
                .then(response => {
                    if (response.data.success) {
                        this.backupList = response.data.data.list || [];
                        this.backupPagination.total = response.data.data.total || 0;
                    } else {
                        this.$message.error('加载备份列表失败');
                    }
                })
                .catch(error => {
                    console.error('加载备份列表失败:', error);
                    this.$message.error('网络错误，请重试');
                })
                .finally(() => {
                    this.backupListLoading = false;
                });
        },

        // 创建手动备份
        createManualBackup() {
            if (!this.manualBackupForm.name.trim()) {
                this.manualBackupForm.name = `手动备份_${new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')}`;
            }

            this.backupLoading = true;
            axios.post('/api/admin/backup/create', this.manualBackupForm)
                .then(response => {
                    if (response.data.success) {
                        this.$message.success('备份创建成功');
                        this.manualBackupForm.name = '';
                        this.loadBackupList();
                        this.loadBackupStats();
                    } else {
                        this.$message.error(response.data.message || '备份创建失败');
                    }
                })
                .catch(error => {
                    console.error('创建备份失败:', error);
                    this.$message.error('网络错误，请重试');
                })
                .finally(() => {
                    this.backupLoading = false;
                });
        },

        // 更新自动备份设置
        updateAutoBackupSettings() {
            axios.post('/api/admin/backup/auto-settings', this.autoBackupForm)
                .then(response => {
                    if (response.data.success) {
                        this.$message.success('自动备份设置已更新');
                        this.loadBackupStats();
                    } else {
                        this.$message.error(response.data.message || '设置更新失败');
                    }
                })
                .catch(error => {
                    console.error('更新自动备份设置失败:', error);
                    this.$message.error('网络错误，请重试');
                });
        },

        // 下载备份
        downloadBackup(backup) {
            window.open(`/api/admin/backup/download/${backup.id}`, '_blank');
        },

        // 恢复备份
        restoreBackup(backup) {
            this.$confirm(`确定要恢复备份"${backup.name}"吗？这将覆盖当前数据！`, '恢复确认', {
                confirmButtonText: '确定恢复',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.post(`/api/admin/backup/restore/${backup.id}`)
                    .then(response => {
                        if (response.data.success) {
                            this.$message.success('备份恢复成功');
                        } else {
                            this.$message.error(response.data.message || '备份恢复失败');
                        }
                    })
                    .catch(error => {
                        console.error('恢复备份失败:', error);
                        this.$message.error('网络错误，请重试');
                    });
            });
        },

        // 删除备份
        deleteBackup(backupId) {
            this.$confirm('确定要删除这个备份文件吗？', '删除确认', {
                confirmButtonText: '确定删除',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.delete(`/api/admin/backup/${backupId}`)
                    .then(response => {
                        if (response.data.success) {
                            this.$message.success('删除成功');
                            this.loadBackupList();
                            this.loadBackupStats();
                        } else {
                            this.$message.error(response.data.message || '删除失败');
                        }
                    })
                    .catch(error => {
                        console.error('删除备份失败:', error);
                        this.$message.error('网络错误，请重试');
                    });
            });
        },

        // 清理旧备份
        cleanOldBackups() {
            this.$confirm('确定要清理超过保留期限的旧备份吗？', '清理确认', {
                confirmButtonText: '确定清理',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.post('/api/admin/backup/clean')
                    .then(response => {
                        if (response.data.success) {
                            this.$message.success('清理完成');
                            this.loadBackupList();
                            this.loadBackupStats();
                        } else {
                            this.$message.error(response.data.message || '清理失败');
                        }
                    })
                    .catch(error => {
                        console.error('清理备份失败:', error);
                        this.$message.error('网络错误，请重试');
                    });
            });
        },

        // 刷新备份列表
        refreshBackupList() {
            this.loadBackupList();
            this.loadBackupStats();
        },

        // 备份分页大小改变
        handleBackupSizeChange(size) {
            this.backupPagination.pageSize = size;
            this.backupPagination.currentPage = 1;
            this.loadBackupList();
        },

        // 备份当前页改变
        handleBackupCurrentChange(page) {
            this.backupPagination.currentPage = page;
            this.loadBackupList();
        },

        // 获取备份类型颜色
        getBackupTypeColor(type) {
            const colorMap = {
                'full': 'primary',
                'data': 'success',
                'structure': 'warning'
            };
            return colorMap[type] || 'info';
        },

        // 获取备份类型名称
        getBackupTypeName(type) {
            const nameMap = {
                'full': '完整备份',
                'data': '数据备份',
                'structure': '结构备份'
            };
            return nameMap[type] || type;
        },

        // 格式化文件大小
        formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },

        // ==================== 安全中心相关方法 ====================

        // 加载安全统计
        loadSecurityStats() {
            axios.get('/api/admin/security/stats')
                .then(response => {
                    if (response.data.success) {
                        this.securityStats = response.data.data;
                    }
                })
                .catch(error => {
                    console.error('加载安全统计失败:', error);
                });
        },

        // 更新安全设置
        updateSecuritySettings() {
            const settings = {
                ...this.loginSecurityForm,
                ...this.apiSecurityForm
            };

            axios.post('/api/admin/security/settings', settings)
                .then(response => {
                    if (response.data.success) {
                        this.$message.success('安全设置已更新');
                    } else {
                        this.$message.error(response.data.message || '设置更新失败');
                    }
                })
                .catch(error => {
                    console.error('更新安全设置失败:', error);
                    this.$message.error('网络错误，请重试');
                });
        },

        // 处理IP标签页点击
        handleIPTabClick(tab) {
            if (tab.name === 'blacklist') {
                this.loadBlacklistIPs();
            } else if (tab.name === 'whitelist') {
                this.loadWhitelistIPs();
            }
        },

        // 加载黑名单IP
        loadBlacklistIPs() {
            axios.get('/api/admin/security/blacklist')
                .then(response => {
                    if (response.data.success) {
                        this.blacklistIPs = response.data.data || [];
                    }
                })
                .catch(error => {
                    console.error('加载黑名单IP失败:', error);
                });
        },

        // 加载白名单IP
        loadWhitelistIPs() {
            axios.get('/api/admin/security/whitelist')
                .then(response => {
                    if (response.data.success) {
                        this.whitelistIPs = response.data.data || [];
                    }
                })
                .catch(error => {
                    console.error('加载白名单IP失败:', error);
                });
        },

        // 刷新IP列表
        refreshIPList() {
            this.loadBlacklistIPs();
            this.loadWhitelistIPs();
            this.loadSecurityStats();
        },

        // 编辑IP
        editIP(ip) {
            this.$message.info('IP编辑功能开发中...');
        },

        // 从黑名单移除IP
        removeFromBlacklist(ipId) {
            this.$confirm('确定要从黑名单中移除这个IP吗？', '移除确认', {
                confirmButtonText: '确定移除',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.delete(`/api/admin/security/blacklist/${ipId}`)
                    .then(response => {
                        if (response.data.success) {
                            this.$message.success('移除成功');
                            this.loadBlacklistIPs();
                            this.loadSecurityStats();
                        } else {
                            this.$message.error(response.data.message || '移除失败');
                        }
                    })
                    .catch(error => {
                        console.error('移除黑名单IP失败:', error);
                        this.$message.error('网络错误，请重试');
                    });
            });
        },

        // 从白名单移除IP
        removeFromWhitelist(ipId) {
            this.$confirm('确定要从白名单中移除这个IP吗？', '移除确认', {
                confirmButtonText: '确定移除',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.delete(`/api/admin/security/whitelist/${ipId}`)
                    .then(response => {
                        if (response.data.success) {
                            this.$message.success('移除成功');
                            this.loadWhitelistIPs();
                        } else {
                            this.$message.error(response.data.message || '移除失败');
                        }
                    })
                    .catch(error => {
                        console.error('移除白名单IP失败:', error);
                        this.$message.error('网络错误，请重试');
                    });
            });
        },

        // 加载安全日志
        loadSecurityLogs() {
            this.securityLogLoading = true;
            const params = {
                page: this.securityLogPagination.currentPage,
                size: this.securityLogPagination.pageSize
            };

            axios.get('/api/admin/security/logs', { params })
                .then(response => {
                    if (response.data.success) {
                        this.securityLogs = response.data.data.list || [];
                        this.securityLogPagination.total = response.data.data.total || 0;
                    } else {
                        this.$message.error('加载安全日志失败');
                    }
                })
                .catch(error => {
                    console.error('加载安全日志失败:', error);
                    this.$message.error('网络错误，请重试');
                })
                .finally(() => {
                    this.securityLogLoading = false;
                });
        },

        // 处理安全事件
        handleSecurityEvent(event) {
            this.$confirm(`确定要处理这个安全事件吗？`, '处理确认', {
                confirmButtonText: '确定处理',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.post(`/api/admin/security/events/${event.id}/handle`)
                    .then(response => {
                        if (response.data.success) {
                            this.$message.success('事件处理成功');
                            this.loadSecurityLogs();
                        } else {
                            this.$message.error(response.data.message || '处理失败');
                        }
                    })
                    .catch(error => {
                        console.error('处理安全事件失败:', error);
                        this.$message.error('网络错误，请重试');
                    });
            });
        },

        // 查看安全事件详情
        viewSecurityEventDetail(event) {
            this.$alert(JSON.stringify(event, null, 2), '安全事件详情', {
                confirmButtonText: '确定',
                customClass: 'security-event-detail-dialog'
            });
        },

        // 清理安全日志
        clearSecurityLogs() {
            this.$confirm('确定要清理30天前的安全日志吗？', '清理确认', {
                confirmButtonText: '确定清理',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.post('/api/admin/security/logs/clear')
                    .then(response => {
                        if (response.data.success) {
                            this.$message.success('清理完成');
                            this.loadSecurityLogs();
                        } else {
                            this.$message.error(response.data.message || '清理失败');
                        }
                    })
                    .catch(error => {
                        console.error('清理安全日志失败:', error);
                        this.$message.error('网络错误，请重试');
                    });
            });
        },

        // 刷新安全日志
        refreshSecurityLogs() {
            this.loadSecurityLogs();
            this.loadSecurityStats();
        },

        // 安全日志分页大小改变
        handleSecurityLogSizeChange(size) {
            this.securityLogPagination.pageSize = size;
            this.securityLogPagination.currentPage = 1;
            this.loadSecurityLogs();
        },

        // 安全日志当前页改变
        handleSecurityLogCurrentChange(page) {
            this.securityLogPagination.currentPage = page;
            this.loadSecurityLogs();
        },

        // 获取安全事件类型
        getSecurityEventType(type) {
            const typeMap = {
                'LOGIN_FAIL': 'danger',
                'SQL_INJECTION': 'danger',
                'XSS_ATTACK': 'warning',
                'BRUTE_FORCE': 'danger',
                'SUSPICIOUS_ACCESS': 'warning'
            };
            return typeMap[type] || 'info';
        },

        // 获取严重程度类型
        getSeverityType(severity) {
            const typeMap = {
                '高': 'danger',
                '中': 'warning',
                '低': 'info'
            };
            return typeMap[severity] || 'info';
        },

        // 保存IP地址
        saveIP() {
            this.$refs.addIPForm.validate((valid) => {
                if (valid) {
                    const url = this.addIPForm.type === 'blacklist'
                        ? '/api/admin/security/blacklist'
                        : '/api/admin/security/whitelist';

                    axios.post(url, this.addIPForm)
                        .then(response => {
                            if (response.data.success) {
                                this.$message.success('IP地址添加成功');
                                this.addIPDialogVisible = false;
                                this.addIPForm = {
                                    ip: '',
                                    type: 'blacklist',
                                    description: '',
                                    expireTime: null
                                };
                                this.refreshIPList();
                            } else {
                                this.$message.error(response.data.message || '添加失败');
                            }
                        })
                        .catch(error => {
                            console.error('添加IP失败:', error);
                            this.$message.error('网络错误，请重试');
                        });
                } else {
                    this.$message.error('请检查输入信息');
                }
            });
        },

        // 获取设置标题
        getSettingsTitle() {
            const titleMap = {
                'basic': '基本设置',
                'email': '邮件设置',
                'security': '安全设置'
            };
            return titleMap[this.currentSettingsType] || '系统设置';
        },

        // 保存设置
        saveSettings() {
            axios.post('/api/admin/settings', {
                type: this.currentSettingsType,
                data: this.settingsForm
            })
            .then(response => {
                if (response.data.success) {
                    this.$message.success('设置保存成功');
                    this.settingsDialogVisible = false;
                } else {
                    this.$message.error(response.data.message || '保存失败');
                }
            })
            .catch(error => {
                console.error('保存设置失败:', error);
                this.$message.error('网络错误，请重试');
            });
        }
    }
    });
}, 1000); // 等待1秒确保所有依赖加载完成