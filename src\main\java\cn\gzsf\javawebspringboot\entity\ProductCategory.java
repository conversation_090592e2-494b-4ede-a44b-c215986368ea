package cn.gzsf.javawebspringboot.entity;

import javax.persistence.*;

/**
 * 产品分类关联实体类
 */
@Entity
@Table(name = "product_category")
public class ProductCategory {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "product_id", nullable = false)
    private Long productId;
    
    @Column(name = "category_id", nullable = false)
    private Long categoryId;
    
    @Column(name = "created_time")
    private Long createdTime;
    
    // 构造函数
    public ProductCategory() {}
    
    public ProductCategory(Long productId, Long categoryId) {
        this.productId = productId;
        this.categoryId = categoryId;
        this.createdTime = System.currentTimeMillis();
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getProductId() {
        return productId;
    }
    
    public void setProductId(Long productId) {
        this.productId = productId;
    }
    
    public Long getCategoryId() {
        return categoryId;
    }
    
    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }
    
    public Long getCreatedTime() {
        return createdTime;
    }
    
    public void setCreatedTime(Long createdTime) {
        this.createdTime = createdTime;
    }
    
    @Override
    public String toString() {
        return "ProductCategory{" +
                "id=" + id +
                ", productId=" + productId +
                ", categoryId=" + categoryId +
                ", createdTime=" + createdTime +
                '}';
    }
}
