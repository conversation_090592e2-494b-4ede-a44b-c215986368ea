<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gzsf.javawebspringboot.mapper.ProductCommentMapper">

    <!-- 添加评论 -->
    <insert id="addComment" parameterType="cn.gzsf.javawebspringboot.entity.ProductComment">
        INSERT INTO product_comment (product_id, user_phone, user_name, user_avatar, content, rating, images, created_time, updated_time, status)
        VALUES (#{productId}, #{userPhone}, #{userName}, #{userAvatar}, #{content}, #{rating}, #{images}, #{createdTime}, #{updatedTime}, #{status})
    </insert>

    <!-- 更新评论状态 -->
    <update id="updateCommentStatus">
        UPDATE product_comment 
        SET status = #{status}, updated_time = UNIX_TIMESTAMP() * 1000
        WHERE id = #{id}
    </update>

    <!-- 删除评论 -->
    <delete id="deleteComment">
        DELETE FROM product_comment WHERE id = #{id}
    </delete>

    <!-- 获取产品评论列表 -->
    <select id="getProductComments" resultType="cn.gzsf.javawebspringboot.entity.ProductComment">
        SELECT
            pc.*,
            p.name as productName,
            COALESCE(pi.image_url, p.image_url, '/images/default-product.svg') as productImageUrl,
            u.username as userName,
            ud.avatar_url as userAvatar,
            u.phone as userAccount,
            u.phone as userPhone
        FROM product_comment pc
        LEFT JOIN products p ON pc.product_id = p.id
        LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
        LEFT JOIN user u ON pc.user_phone = u.phone COLLATE utf8mb4_unicode_ci
        LEFT JOIN user_detail ud ON u.phone = ud.phone COLLATE utf8mb4_unicode_ci
        WHERE pc.product_id = #{productId}
        <if test="status != null">
            AND pc.status = #{status}
        </if>
        ORDER BY pc.created_time DESC
        LIMIT #{offset}, #{size}
    </select>

    <!-- 获取产品评论总数 -->
    <select id="getProductCommentCount" resultType="int">
        SELECT COUNT(*) FROM product_comment 
        WHERE product_id = #{productId}
        <if test="status != null">
            AND status = #{status}
        </if>
    </select>

    <!-- 获取用户评论列表 -->
    <select id="getUserComments" resultType="cn.gzsf.javawebspringboot.entity.ProductComment">
        SELECT
            pc.*,
            p.name as productName,
            COALESCE(pi.image_url, p.image_url, '/images/default-product.svg') as productImageUrl,
            u.username as userName,
            ud.avatar_url as userAvatar,
            u.user_id as userAccount,
            u.phone as userPhone
        FROM product_comment pc
        LEFT JOIN products p ON pc.product_id = p.id
        LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
        LEFT JOIN user u ON pc.user_phone = u.phone COLLATE utf8mb4_unicode_ci
        LEFT JOIN user_detail ud ON u.phone = ud.phone COLLATE utf8mb4_unicode_ci
        WHERE pc.user_phone = #{userPhone}
        ORDER BY pc.created_time DESC
        LIMIT #{offset}, #{size}
    </select>

    <!-- 获取用户评论总数 -->
    <select id="getUserCommentCount" resultType="int">
        SELECT COUNT(*) FROM product_comment 
        WHERE user_phone = #{userPhone}
    </select>

    <!-- 获取所有评论（管理员用） -->
    <select id="getAllComments" resultType="cn.gzsf.javawebspringboot.entity.ProductComment">
        SELECT
            pc.*,
            p.name as productName,
            COALESCE(pi.image_url, p.image_url, '/images/default-product.svg') as productImageUrl,
            u.username as userName,
            ud.avatar_url as userAvatar,
            u.user_id as userAccount,
            u.phone as userPhone
        FROM product_comment pc
        LEFT JOIN products p ON pc.product_id = p.id
        LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
        LEFT JOIN user u ON pc.user_phone = u.phone COLLATE utf8mb4_unicode_ci
        LEFT JOIN user_detail ud ON u.phone = ud.phone COLLATE utf8mb4_unicode_ci
        <if test="status != null">
            WHERE pc.status = #{status}
        </if>
        ORDER BY pc.created_time DESC
        LIMIT #{offset}, #{size}
    </select>

    <!-- 获取评论总数（管理员用） -->
    <select id="getTotalCommentCount" resultType="int">
        SELECT COUNT(*) FROM product_comment
        <if test="status != null">
            WHERE status = #{status}
        </if>
    </select>

    <!-- 获取产品平均评分 -->
    <select id="getProductAverageRating" resultType="double">
        SELECT AVG(rating) FROM product_comment 
        WHERE product_id = #{productId} AND status = 1
    </select>

    <!-- 获取评论详情 -->
    <select id="getCommentById" resultType="cn.gzsf.javawebspringboot.entity.ProductComment">
        SELECT
            pc.*,
            p.name as productName,
            COALESCE(pi.image_url, p.image_url, '/images/default-product.svg') as productImageUrl
        FROM product_comment pc
        LEFT JOIN products p ON pc.product_id = p.id
        LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
        WHERE pc.id = #{id}
    </select>

</mapper>
