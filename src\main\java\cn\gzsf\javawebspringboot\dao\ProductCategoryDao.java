package cn.gzsf.javawebspringboot.dao;

import cn.gzsf.javawebspringboot.entity.ProductCategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品分类关联DAO接口
 */
@Mapper
public interface ProductCategoryDao {
    
    /**
     * 根据产品ID获取分类ID列表
     */
    List<Long> getCategoryIdsByProductId(@Param("productId") Long productId);
    
    /**
     * 根据分类ID获取产品ID列表
     */
    List<Long> getProductIdsByCategoryId(@Param("categoryId") Long categoryId);
    
    /**
     * 添加产品分类关联
     */
    int insert(ProductCategory productCategory);
    
    /**
     * 批量添加产品分类关联
     */
    int insertBatch(@Param("list") List<ProductCategory> productCategories);
    
    /**
     * 删除产品的所有分类关联
     */
    int deleteByProductId(@Param("productId") Long productId);
    
    /**
     * 删除分类的所有产品关联
     */
    int deleteByCategoryId(@Param("categoryId") Long categoryId);
    
    /**
     * 删除特定的产品分类关联
     */
    int deleteByProductIdAndCategoryId(@Param("productId") Long productId, @Param("categoryId") Long categoryId);
    
    /**
     * 检查产品分类关联是否存在
     */
    int existsByProductIdAndCategoryId(@Param("productId") Long productId, @Param("categoryId") Long categoryId);
    
    /**
     * 获取产品的分类信息（包含分类名称）
     */
    List<String> getCategoryNamesByProductId(@Param("productId") Long productId);
    
    /**
     * 获取分类下的产品数量
     */
    int getProductCountByCategoryId(@Param("categoryId") Long categoryId);
}
