package cn.gzsf.javawebspringboot.dao;

import cn.gzsf.javawebspringboot.entity.Product;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

// 表示这是一个 MyBatis Mapper 接口
@Mapper
public interface ProductDao {
    // 查询所有商品
    List<Product> findAllProducts();
    // 根据关键词模糊搜索商品
    List<Product> searchProducts(@Param("keyword") String keyword);

    // 更新产品信息
    int updateProduct(Product product);

    // 添加产品
    int addProduct(Product product);

    // 删除 product_category 表中与产品关联的记录
    int deleteProductCategoriesByProductId(@Param("product_id") Long productId);

    // 删除产品
    int deleteProduct(Long id);

    // 根据 ID 查询产品
    Product findProductById(Long id);

    // 分页查询产品
    List<Product> findProductsPage(@Param("offset") int offset, @Param("size") int size);

    // 获取产品总数
    int getTotalProductCount();
}