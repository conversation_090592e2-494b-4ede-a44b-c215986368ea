package cn.gzsf.javawebspringboot.service;

import java.util.List;

/**
 * 产品分类关联服务接口
 */
public interface ProductCategoryService {
    
    /**
     * 保存产品的分类关联
     * @param productId 产品ID
     * @param categoryIds 分类ID列表
     * @return 是否成功
     */
    boolean saveProductCategories(Long productId, List<Long> categoryIds);
    
    /**
     * 获取产品的分类ID列表
     * @param productId 产品ID
     * @return 分类ID列表
     */
    List<Long> getProductCategoryIds(Long productId);
    
    /**
     * 获取产品的分类名称列表
     * @param productId 产品ID
     * @return 分类名称列表
     */
    List<String> getProductCategoryNames(Long productId);
    
    /**
     * 获取分类下的产品ID列表
     * @param categoryId 分类ID
     * @return 产品ID列表
     */
    List<Long> getCategoryProductIds(Long categoryId);
    
    /**
     * 删除产品的所有分类关联
     * @param productId 产品ID
     * @return 是否成功
     */
    boolean deleteProductCategories(Long productId);
    
    /**
     * 删除分类的所有产品关联
     * @param categoryId 分类ID
     * @return 是否成功
     */
    boolean deleteCategoryProducts(Long categoryId);
    
    /**
     * 获取分类下的产品数量
     * @param categoryId 分类ID
     * @return 产品数量
     */
    int getCategoryProductCount(Long categoryId);
}
