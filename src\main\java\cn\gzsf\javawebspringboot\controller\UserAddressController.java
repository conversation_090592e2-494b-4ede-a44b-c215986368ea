package cn.gzsf.javawebspringboot.controller;

import cn.gzsf.javawebspringboot.entity.UserAddress;
import cn.gzsf.javawebspringboot.service.UserAddressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户地址控制器
 */
@RestController
@RequestMapping("/api/address")
@CrossOrigin(origins = "*")
public class UserAddressController {

    @Autowired
    private UserAddressService userAddressService;

    /**
     * 获取用户地址列表
     */
    @GetMapping("/list/{userPhone}")
    public Map<String, Object> getAddressList(@PathVariable String userPhone) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<UserAddress> addresses = userAddressService.getAddressByUserPhone(userPhone);
            result.put("success", true);
            result.put("data", addresses);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取地址列表失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 添加地址
     */
    @PostMapping("/add")
    public Map<String, Object> addAddress(@RequestBody UserAddress address) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = userAddressService.addAddress(address);
            if (success) {
                result.put("success", true);
                result.put("message", "地址添加成功");
            } else {
                result.put("success", false);
                result.put("message", "地址添加失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "系统错误：" + e.getMessage());
        }
        return result;
    }

    /**
     * 更新地址
     */
    @PostMapping("/update")
    public Map<String, Object> updateAddress(@RequestBody UserAddress address) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = userAddressService.updateAddress(address);
            if (success) {
                result.put("success", true);
                result.put("message", "地址更新成功");
            } else {
                result.put("success", false);
                result.put("message", "地址更新失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "系统错误：" + e.getMessage());
        }
        return result;
    }

    /**
     * 删除地址
     */
    @DeleteMapping("/delete/{id}")
    public Map<String, Object> deleteAddress(@PathVariable Integer id) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = userAddressService.deleteAddress(id);
            if (success) {
                result.put("success", true);
                result.put("message", "地址删除成功");
            } else {
                result.put("success", false);
                result.put("message", "地址删除失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "系统错误：" + e.getMessage());
        }
        return result;
    }

    /**
     * 设为默认地址
     */
    @PostMapping("/setDefault/{id}")
    public Map<String, Object> setDefaultAddress(@PathVariable Integer id, @RequestParam String userPhone) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = userAddressService.setDefaultAddress(id, userPhone);
            if (success) {
                result.put("success", true);
                result.put("message", "设置默认地址成功");
            } else {
                result.put("success", false);
                result.put("message", "设置默认地址失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "系统错误：" + e.getMessage());
        }
        return result;
    }
}
