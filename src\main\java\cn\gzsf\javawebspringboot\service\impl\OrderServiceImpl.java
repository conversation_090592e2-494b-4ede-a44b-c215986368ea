package cn.gzsf.javawebspringboot.service.impl;

import cn.gzsf.javawebspringboot.dto.OrderCreateDTO;
import cn.gzsf.javawebspringboot.entity.UserOrder;
import cn.gzsf.javawebspringboot.mapper.OrderMapper;
import cn.gzsf.javawebspringboot.service.CartService;
import cn.gzsf.javawebspringboot.service.OrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 订单服务实现类
 */
@Service
@Transactional
public class OrderServiceImpl implements OrderService {

    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private CartService cartService;

    @Override
    public String createOrder(OrderCreateDTO orderCreateDTO) {
        try {
            // 生成订单号
            String orderNo = generateOrderNo();
            
            // 创建订单对象
            UserOrder order = new UserOrder();
            order.setOrderNo(orderNo);
            order.setUserPhone(orderCreateDTO.getUserPhone());
            order.setTotalAmount(BigDecimal.valueOf(orderCreateDTO.getTotalAmount()));
            order.setStatus(1); // 1-待付款
            
            // 设置收货信息（这里使用默认值，实际应该从用户地址或前端传入）
            order.setReceiverName(orderCreateDTO.getReceiverName() != null ? 
                orderCreateDTO.getReceiverName() : "默认收货人");
            order.setReceiverPhone(orderCreateDTO.getReceiverPhone() != null ? 
                orderCreateDTO.getReceiverPhone() : orderCreateDTO.getUserPhone());
            order.setReceiverAddress(orderCreateDTO.getReceiverAddress() != null ? 
                orderCreateDTO.getReceiverAddress() : "默认收货地址");
            order.setRemark(orderCreateDTO.getRemark());
            
            long currentTime = System.currentTimeMillis();
            order.setCreatedTime(currentTime);
            order.setUpdatedTime(currentTime);
            
            // 保存订单
            int result = orderMapper.createOrder(order);
            
            if (result > 0) {
                // 订单创建成功后，清空购物车
                cartService.clearCart(orderCreateDTO.getUserPhone());
                return orderNo;
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    
    @Override
    public UserOrder getOrderByOrderNo(String orderNo) {
        try {
            return orderMapper.getOrderByOrderNo(orderNo);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public boolean payOrder(String orderNo) {
        try {
            UserOrder order = orderMapper.getOrderByOrderNo(orderNo);
            if (order != null && order.getStatus() == 1) { // 只有待付款状态才能支付
                order.setStatus(2); // 2-待发货
                order.setUpdatedTime(System.currentTimeMillis());
                return orderMapper.updateOrderStatus(order) > 0;
            }
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean cancelOrder(String orderNo) {
        try {
            UserOrder order = orderMapper.getOrderByOrderNo(orderNo);
            if (order != null && (order.getStatus() == 1 || order.getStatus() == 2)) { // 待付款或待发货状态才能取消
                order.setStatus(5); // 5-已取消
                order.setUpdatedTime(System.currentTimeMillis());
                return orderMapper.updateOrderStatus(order) > 0;
            }
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean confirmReceived(String orderNo) {
        try {
            UserOrder order = orderMapper.getOrderByOrderNo(orderNo);
            if (order != null && order.getStatus() == 3) { // 只有待收货状态才能确认收货
                order.setStatus(4); // 4-已完成
                order.setUpdatedTime(System.currentTimeMillis());
                return orderMapper.updateOrderStatus(order) > 0;
            }
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean deleteOrder(String orderNo) {
        try {
            UserOrder order = orderMapper.getOrderByOrderNo(orderNo);
            if (order != null && (order.getStatus() == 4 || order.getStatus() == 5)) { // 只有已完成或已取消的订单才能删除
                return orderMapper.deleteOrder(orderNo) > 0;
            }
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = sdf.format(new Date());
        // 添加3位随机数
        int random = (int) (Math.random() * 1000);
        return "ORD" + timestamp + String.format("%03d", random);
    }
}
