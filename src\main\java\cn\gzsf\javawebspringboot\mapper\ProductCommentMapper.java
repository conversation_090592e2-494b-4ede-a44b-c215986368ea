package cn.gzsf.javawebspringboot.mapper;

import cn.gzsf.javawebspringboot.entity.ProductComment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品评论Mapper接口
 */
@Mapper
public interface ProductCommentMapper {
    
    /**
     * 添加评论
     */
    int addComment(ProductComment comment);
    
    /**
     * 更新评论状态
     */
    int updateCommentStatus(@Param("id") Long id, @Param("status") Integer status);
    
    /**
     * 删除评论
     */
    int deleteComment(@Param("id") Long id);
    
    /**
     * 获取产品评论列表
     */
    List<ProductComment> getProductComments(@Param("productId") Long productId, 
                                          @Param("status") Integer status,
                                          @Param("offset") int offset, 
                                          @Param("size") int size);
    
    /**
     * 获取产品评论总数
     */
    int getProductCommentCount(@Param("productId") Long productId, @Param("status") Integer status);
    
    /**
     * 获取用户评论列表
     */
    List<ProductComment> getUserComments(@Param("userPhone") String userPhone, 
                                       @Param("offset") int offset, 
                                       @Param("size") int size);
    
    /**
     * 获取用户评论总数
     */
    int getUserCommentCount(@Param("userPhone") String userPhone);
    
    /**
     * 获取所有评论（管理员用）
     */
    List<ProductComment> getAllComments(@Param("status") Integer status,
                                      @Param("offset") int offset, 
                                      @Param("size") int size);
    
    /**
     * 获取评论总数（管理员用）
     */
    int getTotalCommentCount(@Param("status") Integer status);
    
    /**
     * 获取产品平均评分
     */
    Double getProductAverageRating(@Param("productId") Long productId);
    
    /**
     * 获取评论详情
     */
    ProductComment getCommentById(@Param("id") Long id);
}
