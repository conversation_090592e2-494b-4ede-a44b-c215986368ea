package cn.gzsf.javawebspringboot.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 头像上传控制器
 */
@RestController
@RequestMapping("/api/avatar")
public class AvatarController {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 头像上传接口
     */
    @PostMapping("/upload")
    public Map<String, Object> uploadAvatar(
            @RequestParam("file") MultipartFile file,
            @RequestParam("phone") String phone) {
        
        Map<String, Object> result = new HashMap<>();
        
        if (file.isEmpty()) {
            result.put("success", false);
            result.put("message", "上传的文件为空");
            return result;
        }

        try {
            // 验证文件类型
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || !isImageFile(originalFilename)) {
                result.put("success", false);
                result.put("message", "只支持图片文件格式 (jpg, jpeg, png, gif)");
                return result;
            }

            // 验证文件大小 (2MB)
            if (file.getSize() > 2 * 1024 * 1024) {
                result.put("success", false);
                result.put("message", "文件大小不能超过2MB");
                return result;
            }

            // 生成唯一文件名，使用UUID确保唯一性
            String fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String uniqueFileName = generateUniqueFileName(phone, fileExtension);

            // 获取头像保存目录
            String currentDir = System.getProperty("user.dir");
            String avatarDir = currentDir + "/src/main/resources/static/images/avatars";

            // 确保目录存在
            File targetDir = new File(avatarDir);
            if (!targetDir.exists()) {
                boolean created = targetDir.mkdirs();
                System.out.println("📁 创建头像目录: " + (created ? "成功" : "失败") + " - " + avatarDir);
            }

            // 确保文件名唯一，避免冲突
            File targetFile = new File(targetDir, uniqueFileName);
            int counter = 1;
            while (targetFile.exists()) {
                String nameWithoutExt = uniqueFileName.substring(0, uniqueFileName.lastIndexOf("."));
                uniqueFileName = nameWithoutExt + "_" + counter + fileExtension;
                targetFile = new File(targetDir, uniqueFileName);
                counter++;
                if (counter > 100) { // 防止无限循环
                    throw new RuntimeException("无法生成唯一文件名");
                }
            }

            // 保存文件，添加重试机制
            boolean uploadSuccess = false;
            int retryCount = 0;
            Exception lastException = null;

            while (!uploadSuccess && retryCount < 3) {
                try {
                    file.transferTo(targetFile);
                    uploadSuccess = true;
                    System.out.println("👤 头像上传成功: " + targetFile.getAbsolutePath());
                } catch (Exception e) {
                    lastException = e;
                    retryCount++;
                    System.err.println("⚠️ 头像上传重试 " + retryCount + "/3: " + e.getMessage());

                    if (retryCount < 3) {
                        // 等待一小段时间后重试
                        try {
                            Thread.sleep(100);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                        }

                        // 生成新的文件名重试
                        uniqueFileName = generateUniqueFileName(phone, fileExtension);
                        targetFile = new File(targetDir, uniqueFileName);
                    }
                }
            }

            if (!uploadSuccess) {
                throw new RuntimeException("文件上传失败，已重试3次: " + lastException.getMessage(), lastException);
            }

            // 延迟清理用户的旧头像文件（在数据库更新成功后）

            // 更新用户头像信息到数据库 - 只存储文件名，不存储完整路径
            String updateSql = "UPDATE user_detail SET avatar_url = ? WHERE phone = ?";
            int updatedRows = jdbcTemplate.update(updateSql, uniqueFileName, phone);

            if (updatedRows > 0) {
                result.put("success", true);
                result.put("avatar", uniqueFileName);
                result.put("avatarUrl", "images/avatars/" + uniqueFileName);
                result.put("message", "头像上传成功");
                System.out.println("✅ 头像数据库更新成功: " + phone + " -> " + uniqueFileName);

                // 数据库更新成功后，异步清理旧头像文件
                cleanupOldAvatarsAsync(phone, targetDir, uniqueFileName);
            } else {
                // 如果用户详情记录不存在，创建一个
                String insertSql = "INSERT INTO user_detail (phone, avatar_url, created_time, updated_time) VALUES (?, ?, ?, ?)";
                long currentTime = System.currentTimeMillis();
                jdbcTemplate.update(insertSql, phone, uniqueFileName, currentTime, currentTime);

                result.put("success", true);
                result.put("avatar", uniqueFileName);
                result.put("avatarUrl", "images/avatars/" + uniqueFileName);
                result.put("message", "头像上传成功");
                System.out.println("✅ 头像记录创建成功: " + phone + " -> " + uniqueFileName);

                // 数据库更新成功后，异步清理旧头像文件
                cleanupOldAvatarsAsync(phone, targetDir, uniqueFileName);
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "头像上传失败: " + e.getMessage());
            System.err.println("❌ 头像上传失败: " + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 检查头像文件是否存在
     */
    @GetMapping("/check/{phone}")
    public Map<String, Object> checkAvatarExists(@PathVariable String phone) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 从数据库获取用户头像文件名
            String avatarFileName = getCurrentAvatarFromDB(phone);

            if (avatarFileName != null && avatarFileName.startsWith("avatar_")) {
                // 检查文件是否存在
                String currentDir = System.getProperty("user.dir");
                String avatarDir = currentDir + "/src/main/resources/static/images/avatars";
                File avatarFile = new File(avatarDir, avatarFileName);

                if (avatarFile.exists()) {
                    result.put("success", true);
                    result.put("exists", true);
                    result.put("avatarUrl", "images/avatars/" + avatarFileName);
                } else {
                    // 文件不存在，重置为默认头像
                    String updateSql = "UPDATE user_detail SET avatar_url = ? WHERE phone = ?";
                    jdbcTemplate.update(updateSql, "default-avatar.jpg", phone);

                    result.put("success", true);
                    result.put("exists", false);
                    result.put("avatarUrl", "images/avatar/default-avatar.jpg");
                    result.put("message", "头像文件不存在，已重置为默认头像");

                    System.out.println("🔄 用户 " + phone + " 头像文件丢失，已重置为默认头像");
                }
            } else {
                // 使用预设头像或默认头像
                result.put("success", true);
                result.put("exists", true);
                result.put("avatarUrl", avatarFileName != null ? "images/avatar/" + avatarFileName : "images/avatar/default-avatar.jpg");
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "检查头像失败: " + e.getMessage());
            System.err.println("❌ 检查头像存在性失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 生成唯一文件名
     */
    private String generateUniqueFileName(String phone, String fileExtension) {
        // 使用时间戳 + 随机数确保唯一性
        long timestamp = System.currentTimeMillis();
        int random = (int) (Math.random() * 10000);
        return "avatar_" + phone + "_" + timestamp + "_" + random + fileExtension;
    }

    /**
     * 异步清理用户的旧头像文件
     */
    private void cleanupOldAvatarsAsync(String phone, File avatarDir, String currentFileName) {
        // 使用新线程异步执行清理，避免阻塞主线程
        new Thread(() -> {
            try {
                // 等待一段时间，确保浏览器已经加载了新头像
                Thread.sleep(3000);

                // 从数据库获取当前用户的头像文件名，确保不删除正在使用的头像
                String currentAvatarInDB = getCurrentAvatarFromDB(phone);

                File[] files = avatarDir.listFiles();
                if (files != null) {
                    String phonePrefix = "avatar_" + phone + "_";
                    int deletedCount = 0;

                    for (File file : files) {
                        if (file.isFile() && file.getName().startsWith(phonePrefix)) {
                            String fileName = file.getName();

                            // 确保不删除当前正在使用的头像文件
                            if (!fileName.equals(currentFileName) &&
                                !fileName.equals(currentAvatarInDB)) {

                                // 尝试删除旧头像文件，多次重试
                                boolean deleted = deleteFileWithRetry(file, 3);
                                if (deleted) {
                                    deletedCount++;
                                    System.out.println("🗑️ 清理旧头像: " + fileName);
                                } else {
                                    System.err.println("⚠️ 无法删除旧头像: " + fileName + " (文件可能正在使用中)");
                                }
                            } else {
                                System.out.println("🔒 保留当前头像: " + fileName);
                            }
                        }
                    }

                    if (deletedCount > 0) {
                        System.out.println("✅ 用户 " + phone + " 清理完成，删除了 " + deletedCount + " 个旧头像文件");
                    }
                }
            } catch (Exception e) {
                System.err.println("⚠️ 清理旧头像时出错: " + e.getMessage());
            }
        }).start();
    }

    /**
     * 从数据库获取用户当前的头像文件名
     */
    private String getCurrentAvatarFromDB(String phone) {
        try {
            String sql = "SELECT avatar_url FROM user_detail WHERE phone = ?";
            return jdbcTemplate.queryForObject(sql, String.class, phone);
        } catch (Exception e) {
            System.err.println("⚠️ 获取用户当前头像失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 带重试的文件删除
     */
    private boolean deleteFileWithRetry(File file, int maxRetries) {
        for (int i = 0; i < maxRetries; i++) {
            try {
                if (file.delete()) {
                    return true;
                }
                // 如果删除失败，等待一段时间后重试
                Thread.sleep(500);
            } catch (Exception e) {
                System.err.println("删除文件重试 " + (i + 1) + "/" + maxRetries + ": " + e.getMessage());
                try {
                    Thread.sleep(500);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        return false;
    }

    /**
     * 检查是否为图片文件
     */
    private boolean isImageFile(String filename) {
        String lowerCaseFilename = filename.toLowerCase();
        return lowerCaseFilename.endsWith(".jpg") ||
               lowerCaseFilename.endsWith(".jpeg") ||
               lowerCaseFilename.endsWith(".png") ||
               lowerCaseFilename.endsWith(".gif");
    }
}
