package cn.gzsf.javawebspringboot.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Configuration;

/**
 * MyBatis配置类
 * 统一管理Mapper接口扫描，避免重复配置
 */
@Configuration
@MapperScan(basePackages = {
    "cn.gzsf.javawebspringboot.dao",
    "cn.gzsf.javawebspringboot.mapper"
})
public class MyBatisConfig {
    // 这里统一配置所有Mapper接口的扫描路径
    // 避免在多个地方重复配置@MapperScan导致警告
}