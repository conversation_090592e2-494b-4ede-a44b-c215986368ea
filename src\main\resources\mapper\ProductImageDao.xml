<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gzsf.javawebspringboot.dao.ProductImageDao">

    <resultMap id="BaseResultMap" type="cn.gzsf.javawebspringboot.entity.ProductImage">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="product_id" property="productId" jdbcType="BIGINT"/>
        <result column="image_url" property="imageUrl" jdbcType="VARCHAR"/>
        <result column="image_name" property="imageName" jdbcType="VARCHAR"/>
        <result column="is_primary" property="isPrimary" jdbcType="BOOLEAN"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
        <result column="created_time" property="createdTime" jdbcType="BIGINT"/>
        <result column="updated_time" property="updatedTime" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 根据产品ID查询所有图片 -->
    <select id="findByProductId" resultMap="BaseResultMap">
        SELECT * FROM product_images 
        WHERE product_id = #{productId}
        ORDER BY is_primary DESC, sort_order ASC, created_time ASC
    </select>

    <!-- 根据ID查询图片 -->
    <select id="findById" resultMap="BaseResultMap">
        SELECT * FROM product_images WHERE id = #{id}
    </select>

    <!-- 插入新图片 -->
    <insert id="insert" parameterType="cn.gzsf.javawebspringboot.entity.ProductImage" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO product_images (product_id, image_url, image_name, is_primary, sort_order, created_time, updated_time)
        VALUES (#{productId}, #{imageUrl}, #{imageName}, #{isPrimary}, #{sortOrder}, #{createdTime}, #{updatedTime})
    </insert>

    <!-- 批量插入图片 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO product_images (product_id, image_url, image_name, is_primary, sort_order, created_time, updated_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.productId}, #{item.imageUrl}, #{item.imageName}, #{item.isPrimary}, #{item.sortOrder}, #{item.createdTime}, #{item.updatedTime})
        </foreach>
    </insert>

    <!-- 更新图片信息 -->
    <update id="update" parameterType="cn.gzsf.javawebspringboot.entity.ProductImage">
        UPDATE product_images
        <set>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="imageName != null">image_name = #{imageName},</if>
            <if test="isPrimary != null">is_primary = #{isPrimary},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            updated_time = #{updatedTime}
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除图片 -->
    <delete id="deleteById">
        DELETE FROM product_images WHERE id = #{id}
    </delete>

    <!-- 根据产品ID删除所有图片 -->
    <delete id="deleteByProductId">
        DELETE FROM product_images WHERE product_id = #{productId}
    </delete>

    <!-- 设置主图 -->
    <update id="setPrimary">
        UPDATE product_images
        SET is_primary = CASE
            WHEN id = #{id} THEN 1
            ELSE 0
        END,
        updated_time = UNIX_TIMESTAMP() * 1000
        WHERE product_id = #{productId}
    </update>

    <!-- 取消产品的所有主图设置 -->
    <update id="clearPrimary">
        UPDATE product_images
        SET is_primary = 0, updated_time = UNIX_TIMESTAMP() * 1000
        WHERE product_id = #{productId}
    </update>

    <!-- 获取产品的主图 -->
    <select id="findPrimaryByProductId" resultMap="BaseResultMap">
        SELECT * FROM product_images 
        WHERE product_id = #{productId} AND is_primary = 1
        LIMIT 1
    </select>

    <!-- 获取产品图片数量 -->
    <select id="countByProductId" resultType="int">
        SELECT COUNT(*) FROM product_images WHERE product_id = #{productId}
    </select>

</mapper>
