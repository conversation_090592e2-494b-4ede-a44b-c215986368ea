package cn.gzsf.javawebspringboot.entity;

import java.time.LocalDateTime;

/**
 * 安全事件实体类
 */
public class SecurityEvent {
    private Long id;
    private String type;            // 事件类型：LOGIN_FAIL, SQL_INJECTION, XSS_ATTACK, BRUTE_FORCE, SUSPICIOUS_ACCESS
    private String ip;              // 来源IP
    private String description;     // 事件描述
    private String severity;        // 严重程度：高, 中, 低
    private String status;          // 处理状态：pending, handled
    private LocalDateTime time;     // 发生时间
    private String userAgent;       // 用户代理
    private String details;         // 详细信息（JSON格式）
    private String handler;         // 处理人
    private LocalDateTime handleTime; // 处理时间

    public SecurityEvent() {}

    public SecurityEvent(String type, String ip, String description, String severity) {
        this.type = type;
        this.ip = ip;
        this.description = description;
        this.severity = severity;
        this.status = "pending";
        this.time = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSeverity() {
        return severity;
    }

    public void setSeverity(String severity) {
        this.severity = severity;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getTime() {
        return time;
    }

    public void setTime(LocalDateTime time) {
        this.time = time;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public String getHandler() {
        return handler;
    }

    public void setHandler(String handler) {
        this.handler = handler;
    }

    public LocalDateTime getHandleTime() {
        return handleTime;
    }

    public void setHandleTime(LocalDateTime handleTime) {
        this.handleTime = handleTime;
    }

    @Override
    public String toString() {
        return "SecurityEvent{" +
                "id=" + id +
                ", type='" + type + '\'' +
                ", ip='" + ip + '\'' +
                ", description='" + description + '\'' +
                ", severity='" + severity + '\'' +
                ", status='" + status + '\'' +
                ", time=" + time +
                '}';
    }
}
