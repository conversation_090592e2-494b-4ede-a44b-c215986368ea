package cn.gzsf.javawebspringboot.mapper;

import cn.gzsf.javawebspringboot.entity.UserOrder;
import org.apache.ibatis.annotations.*;

/**
 * 订单Mapper接口
 */
@Mapper
public interface OrderMapper {

    /**
     * 创建订单
     */
    @Insert("INSERT INTO user_order (order_no, user_phone, total_amount, status, " +
            "receiver_name, receiver_phone, receiver_address, remark, created_time, updated_time) " +
            "VALUES (#{orderNo}, #{userPhone}, #{totalAmount}, #{status}, " +
            "#{receiverName}, #{receiverPhone}, #{receiverAddress}, #{remark}, #{createdTime}, #{updatedTime})")
    int createOrder(UserOrder order);

    /**
     * 根据订单号查询订单
     */
    @Select("SELECT * FROM user_order WHERE order_no = #{orderNo}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "orderNo", column = "order_no"),
        @Result(property = "userPhone", column = "user_phone"),
        @Result(property = "totalAmount", column = "total_amount"),
        @Result(property = "status", column = "status"),
        @Result(property = "receiverName", column = "receiver_name"),
        @Result(property = "receiverPhone", column = "receiver_phone"),
        @Result(property = "receiverAddress", column = "receiver_address"),
        @Result(property = "remark", column = "remark"),
        @Result(property = "createdTime", column = "created_time"),
        @Result(property = "updatedTime", column = "updated_time")
    })
    UserOrder getOrderByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 更新订单状态
     */
    @Update("UPDATE user_order SET status = #{status}, updated_time = #{updatedTime} WHERE order_no = #{orderNo}")
    int updateOrderStatus(UserOrder order);

    /**
     * 删除订单
     */
    @Delete("DELETE FROM user_order WHERE order_no = #{orderNo}")
    int deleteOrder(@Param("orderNo") String orderNo);
}
