package cn.gzsf.javawebspringboot.service.impl;

import cn.gzsf.javawebspringboot.entity.SystemBackup;
import cn.gzsf.javawebspringboot.service.AdminBackupService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 管理员备份服务实现类
 * 注意：这是一个简化的内存实现，生产环境应该使用数据库和实际的备份逻辑
 */
@Service
public class AdminBackupServiceImpl implements AdminBackupService {
    
    // 使用内存存储，生产环境应该使用数据库
    private final Map<Long, SystemBackup> backupStorage = new ConcurrentHashMap<>();
    private final AtomicLong idGenerator = new AtomicLong(1);
    private boolean autoBackupEnabled = false;
    
    public AdminBackupServiceImpl() {
        // 初始化一些示例数据
        initSampleData();
    }
    
    private void initSampleData() {
        SystemBackup backup1 = new SystemBackup("自动备份_20241217", "full", "/backups/auto_20241217.sql", "system");
        backup1.setId(idGenerator.getAndIncrement());
        backup1.setFileSize(1024L * 1024 * 50); // 50MB
        backup1.setStatus("success");
        backup1.setCreateTime(LocalDateTime.now().minusDays(1));
        backupStorage.put(backup1.getId(), backup1);
        
        SystemBackup backup2 = new SystemBackup("手动备份_测试", "data", "/backups/manual_test.sql", "admin");
        backup2.setId(idGenerator.getAndIncrement());
        backup2.setFileSize(1024L * 1024 * 30); // 30MB
        backup2.setStatus("success");
        backup2.setCreateTime(LocalDateTime.now().minusHours(6));
        backupStorage.put(backup2.getId(), backup2);
    }

    @Override
    public int getTotalBackupCount() {
        return backupStorage.size();
    }

    @Override
    public String getLatestBackupTime() {
        return backupStorage.values().stream()
                .map(SystemBackup::getCreateTime)
                .max(LocalDateTime::compareTo)
                .map(time -> time.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .orElse("无");
    }

    @Override
    public String getTotalBackupSize() {
        long totalBytes = backupStorage.values().stream()
                .mapToLong(backup -> backup.getFileSize() != null ? backup.getFileSize() : 0)
                .sum();
        
        if (totalBytes < 1024) {
            return totalBytes + " B";
        } else if (totalBytes < 1024 * 1024) {
            return String.format("%.1f KB", totalBytes / 1024.0);
        } else if (totalBytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", totalBytes / (1024.0 * 1024));
        } else {
            return String.format("%.1f GB", totalBytes / (1024.0 * 1024 * 1024));
        }
    }

    @Override
    public boolean isAutoBackupEnabled() {
        return autoBackupEnabled;
    }

    @Override
    public List<SystemBackup> getBackupsByPage(Map<String, Object> params) {
        int page = (Integer) params.get("page");
        int size = (Integer) params.get("size");
        
        List<SystemBackup> sortedBackups = backupStorage.values().stream()
                .sorted((a, b) -> b.getCreateTime().compareTo(a.getCreateTime())) // 按时间倒序
                .collect(Collectors.toList());
        
        int start = (page - 1) * size;
        int end = Math.min(start + size, sortedBackups.size());
        
        if (start >= sortedBackups.size()) {
            return new ArrayList<>();
        }
        
        return sortedBackups.subList(start, end);
    }

    @Override
    public SystemBackup createBackup(String name, String type, String creator) {
        SystemBackup backup = new SystemBackup(name, type, "/backups/" + name + ".sql", creator);
        backup.setId(idGenerator.getAndIncrement());
        
        // 模拟备份过程
        try {
            Thread.sleep(1000); // 模拟备份耗时
            backup.setStatus("success");
            backup.setFileSize(1024L * 1024 * (20 + (long)(Math.random() * 80))); // 随机大小20-100MB
        } catch (InterruptedException e) {
            backup.setStatus("failed");
        }
        
        backupStorage.put(backup.getId(), backup);
        return backup;
    }

    @Override
    public void updateAutoBackupSettings(Map<String, Object> settings) {
        this.autoBackupEnabled = (Boolean) settings.getOrDefault("enabled", false);
        // 这里可以保存其他自动备份设置，如频率、时间等
    }

    @Override
    public void restoreBackup(Long id) {
        SystemBackup backup = backupStorage.get(id);
        if (backup == null) {
            throw new RuntimeException("备份文件不存在");
        }
        
        if (!"success".equals(backup.getStatus())) {
            throw new RuntimeException("备份文件状态异常，无法恢复");
        }
        
        // 这里应该实现实际的数据库恢复逻辑
        // 暂时只是模拟
        try {
            Thread.sleep(2000); // 模拟恢复耗时
        } catch (InterruptedException e) {
            throw new RuntimeException("恢复过程被中断");
        }
    }

    @Override
    public void deleteBackup(Long id) {
        SystemBackup backup = backupStorage.remove(id);
        if (backup == null) {
            throw new RuntimeException("备份文件不存在");
        }
        
        // 这里应该删除实际的备份文件
        // 暂时只是从内存中移除
    }

    @Override
    public int cleanOldBackups() {
        // 清理30天前的备份
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(30);
        
        List<Long> toDelete = backupStorage.values().stream()
                .filter(backup -> backup.getCreateTime().isBefore(cutoffTime))
                .map(SystemBackup::getId)
                .collect(Collectors.toList());
        
        toDelete.forEach(backupStorage::remove);
        return toDelete.size();
    }
}
