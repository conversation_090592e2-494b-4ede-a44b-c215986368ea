package cn.gzsf.javawebspringboot.service;

import cn.gzsf.javawebspringboot.entity.SystemLog;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 管理员日志服务接口
 */
public interface AdminLogService {
    
    /**
     * 根据时间范围统计日志数量
     */
    int countLogsByTimeRange(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据日志级别统计数量
     */
    int countLogsByLevel(String level);
    
    /**
     * 分页获取日志列表
     */
    List<SystemLog> getLogsByPage(Map<String, Object> params);
    
    /**
     * 统计日志总数
     */
    int countLogs(Map<String, Object> params);
    
    /**
     * 删除日志
     */
    void deleteLog(Long id);
    
    /**
     * 清理指定时间之前的日志
     */
    int clearLogsBefore(LocalDateTime cutoffTime);
    
    /**
     * 添加日志记录
     */
    void addLog(SystemLog log);
    
    /**
     * 记录操作日志
     */
    void logOperation(String level, String action, String user, String description, String ip);
}
