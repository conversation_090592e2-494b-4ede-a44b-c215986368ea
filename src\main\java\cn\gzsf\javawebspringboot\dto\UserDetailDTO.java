package cn.gzsf.javawebspringboot.dto;

import cn.gzsf.javawebspringboot.entity.*;
import java.util.List;

/**
 * 用户详情DTO，包含用户的所有相关信息
 */
public class UserDetailDTO {
    
    // 基本用户信息
    private String userId;
    private String username;
    private String phone;
    private Long registerTime;
    
    // 用户详情信息
    private String avatarUrl;
    private String signature;
    
    // 收货地址列表
    private List<UserAddress> addresses;
    
    // 购物车商品数量
    private Integer cartItemCount;
    
    // 订单统计
    private Integer totalOrderCount;
    private Integer pendingOrderCount;
    
    // 好友数量
    private Integer friendCount;
    
    // 构造函数
    public UserDetailDTO() {}
    
    // Getter和Setter方法
    public String getUserId() {
        return userId;
    }
    
    public void setUserId(String userId) {
        this.userId = userId;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public Long getRegisterTime() {
        return registerTime;
    }
    
    public void setRegisterTime(Long registerTime) {
        this.registerTime = registerTime;
    }
    
    public String getAvatarUrl() {
        return avatarUrl;
    }
    
    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }
    
    public String getSignature() {
        return signature;
    }
    
    public void setSignature(String signature) {
        this.signature = signature;
    }
    
    public List<UserAddress> getAddresses() {
        return addresses;
    }
    
    public void setAddresses(List<UserAddress> addresses) {
        this.addresses = addresses;
    }
    
    public Integer getCartItemCount() {
        return cartItemCount;
    }
    
    public void setCartItemCount(Integer cartItemCount) {
        this.cartItemCount = cartItemCount;
    }
    
    public Integer getTotalOrderCount() {
        return totalOrderCount;
    }
    
    public void setTotalOrderCount(Integer totalOrderCount) {
        this.totalOrderCount = totalOrderCount;
    }
    
    public Integer getPendingOrderCount() {
        return pendingOrderCount;
    }
    
    public void setPendingOrderCount(Integer pendingOrderCount) {
        this.pendingOrderCount = pendingOrderCount;
    }
    
    public Integer getFriendCount() {
        return friendCount;
    }
    
    public void setFriendCount(Integer friendCount) {
        this.friendCount = friendCount;
    }
    
    // 获取默认地址
    public UserAddress getDefaultAddress() {
        if (addresses != null) {
            return addresses.stream()
                    .filter(UserAddress::getIsDefault)
                    .findFirst()
                    .orElse(null);
        }
        return null;
    }
    
    // 获取格式化的注册时间
    public String getFormattedRegisterTime() {
        if (registerTime != null) {
            return new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                    .format(new java.util.Date(registerTime));
        }
        return "";
    }
}
