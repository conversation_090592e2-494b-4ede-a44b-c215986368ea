<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gzsf.javawebspringboot.dao.CategoryDao">

    <!-- 查询所有分类 -->
    <select id="findAllCategories" resultType="cn.gzsf.javawebspringboot.entity.Category">
        SELECT id, name, description, sort_order, is_active, created_time, updated_time
        FROM categories
        WHERE is_active = 1
        ORDER BY sort_order ASC, id ASC
    </select>

    <!-- 根据 ID 查询分类 -->
    <select id="findCategoryById" resultType="cn.gzsf.javawebspringboot.entity.Category">
        SELECT id, name, description, sort_order, is_active, created_time, updated_time
        FROM categories
        WHERE id = #{id}
    </select>

    <!-- 插入新分类 -->
    <insert id="insertCategory" parameterType="cn.gzsf.javawebspringboot.entity.Category" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO categories (name, description, sort_order, is_active, created_time, updated_time)
        VALUES (#{name}, #{description}, #{sortOrder}, #{isActive}, #{createdTime}, #{updatedTime})
    </insert>

    <!-- 更新分类信息 -->
    <update id="updateCategory" parameterType="cn.gzsf.javawebspringboot.entity.Category">
        UPDATE categories
        SET name = #{name},
            description = #{description},
            sort_order = #{sortOrder},
            is_active = #{isActive},
            updated_time = #{updatedTime}
        WHERE id = #{id}
    </update>

    <!-- 删除分类 -->
    <delete id="deleteCategory" parameterType="java.lang.Long">
        DELETE FROM categories WHERE id = #{id}
    </delete>

    <!-- 分页查询分类 -->
    <select id="findCategoriesPage" resultType="cn.gzsf.javawebspringboot.entity.Category">
        SELECT id, name, description, sort_order, is_active, created_time, updated_time
        FROM categories
        ORDER BY sort_order ASC, id DESC
        LIMIT #{offset}, #{size}
    </select>

    <!-- 获取分类总数 -->
    <select id="getTotalCategoryCount" resultType="int">
        SELECT COUNT(*) FROM categories
    </select>
</mapper>