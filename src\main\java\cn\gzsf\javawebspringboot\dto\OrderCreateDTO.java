package cn.gzsf.javawebspringboot.dto;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单创建DTO
 */
public class OrderCreateDTO {
    
    private String userPhone;
    private Double totalAmount;
    private String receiverName;
    private String receiverPhone;
    private String receiverAddress;
    private String remark;
    private List<OrderItemDTO> items;
    
    // 构造函数
    public OrderCreateDTO() {}
    
    // Getter和Setter方法
    public String getUserPhone() {
        return userPhone;
    }
    
    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }
    
    public Double getTotalAmount() {
        return totalAmount;
    }
    
    public void setTotalAmount(Double totalAmount) {
        this.totalAmount = totalAmount;
    }
    
    public String getReceiverName() {
        return receiverName;
    }
    
    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }
    
    public String getReceiverPhone() {
        return receiverPhone;
    }
    
    public void setReceiverPhone(String receiverPhone) {
        this.receiverPhone = receiverPhone;
    }
    
    public String getReceiverAddress() {
        return receiverAddress;
    }
    
    public void setReceiverAddress(String receiverAddress) {
        this.receiverAddress = receiverAddress;
    }
    
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
    
    public List<OrderItemDTO> getItems() {
        return items;
    }
    
    public void setItems(List<OrderItemDTO> items) {
        this.items = items;
    }
    
    /**
     * 订单商品DTO
     */
    public static class OrderItemDTO {
        private Integer productId;
        private String productName;
        private Double productPrice;
        private Integer quantity;
        
        // 构造函数
        public OrderItemDTO() {}
        
        public OrderItemDTO(Integer productId, String productName, Double productPrice, Integer quantity) {
            this.productId = productId;
            this.productName = productName;
            this.productPrice = productPrice;
            this.quantity = quantity;
        }
        
        // Getter和Setter方法
        public Integer getProductId() {
            return productId;
        }
        
        public void setProductId(Integer productId) {
            this.productId = productId;
        }
        
        public String getProductName() {
            return productName;
        }
        
        public void setProductName(String productName) {
            this.productName = productName;
        }
        
        public Double getProductPrice() {
            return productPrice;
        }
        
        public void setProductPrice(Double productPrice) {
            this.productPrice = productPrice;
        }
        
        public Integer getQuantity() {
            return quantity;
        }
        
        public void setQuantity(Integer quantity) {
            this.quantity = quantity;
        }
    }
}
