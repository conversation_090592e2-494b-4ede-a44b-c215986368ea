package cn.gzsf.javawebspringboot.mapper;

import cn.gzsf.javawebspringboot.dto.CartItemDTO;
import cn.gzsf.javawebspringboot.entity.ShoppingCart;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 购物车Mapper接口
 */
@Mapper
public interface CartMapper {

    /**
     * 添加商品到购物车
     */
    @Insert("INSERT INTO shopping_cart (user_phone, product_id, quantity, created_time, updated_time) " +
            "VALUES (#{userPhone}, #{productId}, #{quantity}, #{createdTime}, #{updatedTime})")
    int addToCart(ShoppingCart cartItem);

    /**
     * 获取购物车中的特定商品
     */
    @Select("SELECT * FROM shopping_cart WHERE user_phone = #{userPhone} AND product_id = #{productId}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "userPhone", column = "user_phone"),
        @Result(property = "productId", column = "product_id"),
        @Result(property = "quantity", column = "quantity"),
        @Result(property = "createdTime", column = "created_time"),
        @Result(property = "updatedTime", column = "updated_time")
    })
    ShoppingCart getCartItem(@Param("userPhone") String userPhone, @Param("productId") Integer productId);

    /**
     * 获取用户购物车（包含商品信息）
     */
    @Select("SELECT sc.id, sc.user_phone, sc.product_id, p.name as product_name, " +
            "p.description as product_description, p.price as product_price, " +
            "COALESCE(pi.image_url, p.image_url, 'images/default-product.svg') as product_image_url, " +
            "sc.quantity, sc.created_time, sc.updated_time " +
            "FROM shopping_cart sc " +
            "LEFT JOIN products p ON sc.product_id = p.id " +
            "LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1 " +
            "WHERE sc.user_phone = #{userPhone} " +
            "ORDER BY sc.created_time DESC")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "userPhone", column = "user_phone"),
        @Result(property = "productId", column = "product_id"),
        @Result(property = "productName", column = "product_name"),
        @Result(property = "productDescription", column = "product_description"),
        @Result(property = "productPrice", column = "product_price"),
        @Result(property = "productImageUrl", column = "product_image_url"),
        @Result(property = "quantity", column = "quantity"),
        @Result(property = "createdTime", column = "created_time"),
        @Result(property = "updatedTime", column = "updated_time")
    })
    List<CartItemDTO> getCartByUserPhone(@Param("userPhone") String userPhone);

    /**
     * 更新购物车商品数量
     */
    @Update("UPDATE shopping_cart SET quantity = #{quantity}, updated_time = #{updatedTime} " +
            "WHERE user_phone = #{userPhone} AND product_id = #{productId}")
    int updateCartItem(ShoppingCart cartItem);

    /**
     * 从购物车删除商品
     */
    @Delete("DELETE FROM shopping_cart WHERE user_phone = #{userPhone} AND product_id = #{productId}")
    int removeFromCart(@Param("userPhone") String userPhone, @Param("productId") Integer productId);

    /**
     * 清空购物车
     */
    @Delete("DELETE FROM shopping_cart WHERE user_phone = #{userPhone}")
    int clearCart(@Param("userPhone") String userPhone);

    /**
     * 获取购物车商品总数量
     */
    @Select("SELECT COALESCE(SUM(quantity), 0) FROM shopping_cart WHERE user_phone = #{userPhone}")
    int getCartItemCount(@Param("userPhone") String userPhone);
}
