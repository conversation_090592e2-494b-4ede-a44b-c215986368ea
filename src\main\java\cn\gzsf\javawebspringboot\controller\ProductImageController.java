package cn.gzsf.javawebspringboot.controller;

import cn.gzsf.javawebspringboot.entity.ProductImage;
import cn.gzsf.javawebspringboot.service.ProductImageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 产品图片控制器
 */
@RestController
@RequestMapping("/api/product-images")
public class ProductImageController {
    
    @Autowired
    private ProductImageService productImageService;
    
    /**
     * 根据产品ID获取所有图片
     */
    @GetMapping("/product/{productId}")
    public Map<String, Object> getImagesByProductId(@PathVariable Long productId) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<ProductImage> images = productImageService.getImagesByProductId(productId);
            result.put("success", true);
            result.put("data", images);
            result.put("message", "获取图片列表成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取图片列表失败: " + e.getMessage());
        }
        return result;
    }
    
    /**
     * 根据ID获取图片
     */
    @GetMapping("/{id}")
    public Map<String, Object> getImageById(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            ProductImage image = productImageService.getImageById(id);
            if (image != null) {
                result.put("success", true);
                result.put("data", image);
                result.put("message", "获取图片成功");
            } else {
                result.put("success", false);
                result.put("message", "图片不存在");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取图片失败: " + e.getMessage());
        }
        return result;
    }
    
    /**
     * 上传单张图片
     */
    @PostMapping("/upload")
    public Map<String, Object> uploadImage(
            @RequestParam("productId") Long productId,
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "isPrimary", defaultValue = "false") Boolean isPrimary) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            ProductImage uploadedImage = productImageService.uploadImage(productId, file, isPrimary);
            if (uploadedImage != null) {
                result.put("success", true);
                result.put("data", uploadedImage);
                result.put("message", "图片上传成功");
            } else {
                result.put("success", false);
                result.put("message", "图片上传失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "图片上传失败: " + e.getMessage());
        }
        return result;
    }
    
    /**
     * 上传多张图片
     */
    @PostMapping("/upload-multiple")
    public Map<String, Object> uploadMultipleImages(
            @RequestParam("productId") Long productId,
            @RequestParam("files") MultipartFile[] files) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            List<ProductImage> uploadedImages = productImageService.uploadImages(productId, files);
            result.put("success", true);
            result.put("data", uploadedImages);
            result.put("uploadedCount", uploadedImages.size());
            result.put("message", "成功上传 " + uploadedImages.size() + " 张图片");
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "图片上传失败: " + e.getMessage());
        }
        return result;
    }
    
    /**
     * 删除图片
     */
    @DeleteMapping("/{id}")
    public Map<String, Object> deleteImage(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = productImageService.deleteImage(id);
            if (success) {
                result.put("success", true);
                result.put("message", "图片删除成功");
            } else {
                result.put("success", false);
                result.put("message", "图片删除失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "图片删除失败: " + e.getMessage());
        }
        return result;
    }
    
    /**
     * 设置主图
     */
    @PutMapping("/{id}/primary")
    public Map<String, Object> setPrimaryImage(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 先获取图片信息
            ProductImage image = productImageService.getImageById(id);
            if (image == null) {
                result.put("success", false);
                result.put("message", "图片不存在");
                return result;
            }

            // 将该产品的所有图片设为非主图
            productImageService.clearPrimaryImages(image.getProductId());

            // 设置当前图片为主图
            boolean success = productImageService.setPrimaryImage(id);

            if (success) {
                result.put("success", true);
                result.put("message", "主图设置成功");
            } else {
                result.put("success", false);
                result.put("message", "主图设置失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "设置主图失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 设置主图（POST方式，用于前端调用）
     */
    @PostMapping("/{id}/set-primary")
    public Map<String, Object> setPrimaryImagePost(@PathVariable Long id) {
        return setPrimaryImage(id);
    }
    
    /**
     * 获取产品的主图
     */
    @GetMapping("/product/{productId}/primary")
    public Map<String, Object> getPrimaryImage(@PathVariable Long productId) {
        Map<String, Object> result = new HashMap<>();
        try {
            ProductImage primaryImage = productImageService.getPrimaryImage(productId);
            result.put("success", true);
            result.put("data", primaryImage);
            result.put("message", "获取主图成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取主图失败: " + e.getMessage());
        }
        return result;
    }
    
    /**
     * 删除产品的所有图片
     */
    @DeleteMapping("/product/{productId}")
    public Map<String, Object> deleteImagesByProductId(@PathVariable Long productId) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = productImageService.deleteImagesByProductId(productId);
            if (success) {
                result.put("success", true);
                result.put("message", "删除产品图片成功");
            } else {
                result.put("success", false);
                result.put("message", "删除产品图片失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "删除产品图片失败: " + e.getMessage());
        }
        return result;
    }



    /**
     * 添加产品图片
     */
    @PostMapping("/add")
    public Map<String, Object> addProductImage(@RequestBody ProductImage productImage) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = productImageService.addImage(productImage);

            if (success) {
                result.put("success", true);
                result.put("message", "图片添加成功");
            } else {
                result.put("success", false);
                result.put("message", "图片添加失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "添加图片失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 更新图片信息
     */
    @PutMapping("/{imageId}")
    public Map<String, Object> updateProductImage(@PathVariable Long imageId, @RequestBody ProductImage productImage) {
        Map<String, Object> result = new HashMap<>();
        try {
            productImage.setId(imageId);
            boolean success = productImageService.updateImage(productImage);

            if (success) {
                result.put("success", true);
                result.put("message", "图片更新成功");
            } else {
                result.put("success", false);
                result.put("message", "图片更新失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "更新图片失败: " + e.getMessage());
        }
        return result;
    }
}
