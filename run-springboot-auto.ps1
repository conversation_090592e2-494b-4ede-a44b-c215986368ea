# 自动运行SpringBoot项目（类似IDEA方式）
Write-Host "🚀 自动启动SpringBoot项目..." -ForegroundColor Green

# 查找Java安装路径
Write-Host "🔍 查找Java安装路径..." -ForegroundColor Yellow
$javaPath = (Get-Command java).Source
$javaHome = Split-Path (Split-Path $javaPath -Parent) -Parent
Write-Host "📍 Java路径: $javaHome" -ForegroundColor Cyan

# 设置环境变量
$env:JAVA_HOME = $javaHome
$env:PATH = "$javaHome\bin;$env:PATH"

Write-Host "✅ Java环境已设置" -ForegroundColor Green
Write-Host "   JAVA_HOME: $env:JAVA_HOME" -ForegroundColor White

# 检查Java版本
Write-Host "📋 Java版本信息:" -ForegroundColor Yellow
java -version

# 设置Maven路径
$mavenPath = ".\apache-maven-3.9.4\bin\mvn.cmd"
Write-Host "📦 使用本地Maven: $mavenPath" -ForegroundColor Cyan

# 清理并编译项目
Write-Host "🧹 清理项目..." -ForegroundColor Yellow
& $mavenPath clean

Write-Host "🔨 编译项目..." -ForegroundColor Yellow
& $mavenPath compile

# 运行SpringBoot应用
Write-Host "🎯 启动SpringBoot应用..." -ForegroundColor Green
Write-Host "📍 应用将在 http://localhost:8082 启动" -ForegroundColor Cyan
Write-Host "🎉 功能已实现:" -ForegroundColor Magenta
Write-Host "   ✅ 息壤臻选区域只显示新品（横向滚动）" -ForegroundColor White
Write-Host "   ✅ 分类导航下方显示分类产品（网格布局）" -ForegroundColor White
Write-Host "   ✅ 清理了所有404错误" -ForegroundColor White

# 使用spring-boot:run启动应用
& $mavenPath spring-boot:run
