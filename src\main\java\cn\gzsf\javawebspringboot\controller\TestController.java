package cn.gzsf.javawebspringboot.controller;

import cn.gzsf.javawebspringboot.dao.UserDao;
import cn.gzsf.javawebspringboot.dao.ProductDao;
import cn.gzsf.javawebspringboot.dao.CategoryDao;
import cn.gzsf.javawebspringboot.mapper.UserDetailMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
public class TestController {

    @Autowired(required = false)
    private UserDao userDao;

    @Autowired(required = false)
    private ProductDao productDao;

    @Autowired(required = false)
    private CategoryDao categoryDao;

    @Autowired(required = false)
    private UserDetailMapper userDetailMapper;

    @GetMapping("/test/status")
    public Map<String, Object> getStatus() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "running");
        result.put("message", "应用程序正在运行");
        result.put("timestamp", System.currentTimeMillis());
        result.put("port", 8082);
        
        System.out.println("✅ 状态检查请求 - 应用程序正在运行");
        
        return result;
    }

    @GetMapping("/test/hello")
    public String hello() {
        System.out.println("✅ Hello请求");
        return "Hello! 应用程序正在运行在端口8082";
    }

    /**
     * 测试MyBatis Mapper是否正常注入
     */
    @GetMapping("/test/mappers")
    public Map<String, Object> testMappers() {
        Map<String, Object> result = new HashMap<>();

        try {
            result.put("success", true);
            result.put("userDao", userDao != null ? "✅ 注入成功" : "❌ 注入失败");
            result.put("productDao", productDao != null ? "✅ 注入成功" : "❌ 注入失败");
            result.put("categoryDao", categoryDao != null ? "✅ 注入成功" : "❌ 注入失败");
            result.put("userDetailMapper", userDetailMapper != null ? "✅ 注入成功" : "❌ 注入失败");

            // 测试数据库连接
            if (userDao != null) {
                int userCount = userDao.getTotalUserCount();
                result.put("userCount", userCount);
                result.put("databaseConnection", "✅ 数据库连接正常");
            }

            result.put("message", "MyBatis配置测试完成");

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("message", "MyBatis配置测试失败");
        }

        return result;
    }
}
