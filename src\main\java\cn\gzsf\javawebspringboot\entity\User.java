package cn.gzsf.javawebspringboot.entity;

import javax.persistence.*;
import java.io.Serializable;

// 用户实体类，实现Serializable接口以便在网络传输或持久化存储时使用
@Entity
@Table(name = "user")
public class User implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id; // 主键ID

    @Column(name = "user_id", unique = true) // 显式映射数据库字段
    private String userId; // 用户唯一 ID

    private String username; // 用户名

    private String phone; // 用户手机号

    private String password; // 用户密码

    @Transient
    private String avatar; // 用户头像（不存储在user表中）

    @Transient
    private String signature; // 用户签名（不存储在user表中）

    @Column(name = "register_time")
    private Long registerTime; // 用户注册时间

    @Transient
    private Long createdTime; // 创建时间（不存储在user表中）

    // 无参构造函数
    public User() {
    }

    // 有参构造函数
    public User(String userId, String username, String phone, String password, Long registerTime) {
        this.userId = userId;
        this.username = username;
        this.phone = phone;
        this.password = password;
        this.registerTime = registerTime;
        this.createdTime = System.currentTimeMillis();
    }

    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", userId='" + userId + '\'' +
                ", username='" + username + '\'' +
                ", phone='" + phone + '\'' +
                ", password='" + password + '\'' +
                ", avatar='" + avatar + '\'' +
                ", signature='" + signature + '\'' +
                ", registerTime=" + registerTime +
                ", createdTime=" + createdTime +
                '}';
    }

    // Getter 和 Setter 方法

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public Long getRegisterTime() {
        return registerTime;
    }

    public void setRegisterTime(Long registerTime) {
        this.registerTime = registerTime;
    }

    public Long getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Long createdTime) {
        this.createdTime = createdTime;
    }
}