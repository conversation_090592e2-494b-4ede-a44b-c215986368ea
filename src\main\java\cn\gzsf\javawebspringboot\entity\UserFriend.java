package cn.gzsf.javawebspringboot.entity;

import javax.persistence.*;

/**
 * 用户好友实体类
 */
@Entity
@Table(name = "user_friends")
public class UserFriend {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    
    @Column(name = "user_phone", nullable = false, length = 20)
    private String userPhone;
    
    @Column(name = "friend_phone", nullable = false, length = 20)
    private String friendPhone;
    
    @Column(name = "friend_name", length = 100)
    private String friendName;
    
    @Column(name = "status")
    private Integer status = 1; // 1-正常，0-已删除
    
    @Column(name = "created_time", nullable = false)
    private Long createdTime;
    
    @Column(name = "updated_time")
    private Long updatedTime;
    
    // 扩展字段（不存储在数据库中）
    @Transient
    private String friendUsername;
    
    @Transient
    private String friendAvatar;
    
    // 构造函数
    public UserFriend() {}
    
    public UserFriend(String userPhone, String friendPhone, String friendName) {
        this.userPhone = userPhone;
        this.friendPhone = friendPhone;
        this.friendName = friendName;
        this.status = 1;
        this.createdTime = System.currentTimeMillis();
        this.updatedTime = System.currentTimeMillis();
    }
    
    // Getter和Setter方法
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getUserPhone() {
        return userPhone;
    }
    
    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }
    
    public String getFriendPhone() {
        return friendPhone;
    }
    
    public void setFriendPhone(String friendPhone) {
        this.friendPhone = friendPhone;
    }
    
    public String getFriendName() {
        return friendName;
    }
    
    public void setFriendName(String friendName) {
        this.friendName = friendName;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public Long getCreatedTime() {
        return createdTime;
    }
    
    public void setCreatedTime(Long createdTime) {
        this.createdTime = createdTime;
    }
    
    public Long getUpdatedTime() {
        return updatedTime;
    }
    
    public void setUpdatedTime(Long updatedTime) {
        this.updatedTime = updatedTime;
    }
    
    public String getFriendUsername() {
        return friendUsername;
    }
    
    public void setFriendUsername(String friendUsername) {
        this.friendUsername = friendUsername;
    }
    
    public String getFriendAvatar() {
        return friendAvatar;
    }
    
    public void setFriendAvatar(String friendAvatar) {
        this.friendAvatar = friendAvatar;
    }
    
    @Override
    public String toString() {
        return "UserFriend{" +
                "id=" + id +
                ", userPhone='" + userPhone + '\'' +
                ", friendPhone='" + friendPhone + '\'' +
                ", friendName='" + friendName + '\'' +
                ", status=" + status +
                ", createdTime=" + createdTime +
                ", updatedTime=" + updatedTime +
                ", friendUsername='" + friendUsername + '\'' +
                ", friendAvatar='" + friendAvatar + '\'' +
                '}';
    }
}
