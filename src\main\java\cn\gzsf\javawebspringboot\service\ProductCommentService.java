package cn.gzsf.javawebspringboot.service;

import cn.gzsf.javawebspringboot.entity.ProductComment;

import java.util.List;
import java.util.Map;

/**
 * 产品评论服务接口
 */
public interface ProductCommentService {
    
    /**
     * 添加评论
     */
    boolean addComment(ProductComment comment);
    
    /**
     * 更新评论状态
     */
    boolean updateCommentStatus(Long id, Integer status);
    
    /**
     * 删除评论
     */
    boolean deleteComment(Long id);
    
    /**
     * 获取产品评论列表
     */
    Map<String, Object> getProductComments(Long productId, Integer status, int page, int size);
    
    /**
     * 获取用户评论列表
     */
    Map<String, Object> getUserComments(String userPhone, int page, int size);
    
    /**
     * 获取所有评论（管理员用）
     */
    Map<String, Object> getAllComments(Integer status, int page, int size);
    
    /**
     * 获取产品平均评分
     */
    Double getProductAverageRating(Long productId);
    
    /**
     * 获取评论详情
     */
    ProductComment getCommentById(Long id);

    /**
     * 获取评论统计（管理员用）
     */
    Map<String, Object> getCommentStats();
}
