package cn.gzsf.javawebspringboot.service;

import cn.gzsf.javawebspringboot.entity.SystemBackup;
import java.util.List;
import java.util.Map;

/**
 * 管理员备份服务接口
 */
public interface AdminBackupService {
    
    /**
     * 获取备份总数
     */
    int getTotalBackupCount();
    
    /**
     * 获取最新备份时间
     */
    String getLatestBackupTime();
    
    /**
     * 获取备份总大小
     */
    String getTotalBackupSize();
    
    /**
     * 检查自动备份是否启用
     */
    boolean isAutoBackupEnabled();
    
    /**
     * 分页获取备份列表
     */
    List<SystemBackup> getBackupsByPage(Map<String, Object> params);
    
    /**
     * 创建备份
     */
    SystemBackup createBackup(String name, String type, String creator);
    
    /**
     * 更新自动备份设置
     */
    void updateAutoBackupSettings(Map<String, Object> settings);
    
    /**
     * 恢复备份
     */
    void restoreBackup(Long id);
    
    /**
     * 删除备份
     */
    void deleteBackup(Long id);
    
    /**
     * 清理旧备份
     */
    int cleanOldBackups();
}
