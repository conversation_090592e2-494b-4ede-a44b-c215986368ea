package cn.gzsf.javawebspringboot.dto;

import java.math.BigDecimal;

/**
 * 购物车商品DTO
 */
public class CartItemDTO {
    
    private Integer id;
    private String userPhone;
    private Integer productId;
    private String productName;
    private String productDescription;
    private Double productPrice;
    private String productImageUrl;
    private Integer quantity;
    private Long createdTime;
    private Long updatedTime;
    
    // 构造函数
    public CartItemDTO() {}
    
    public CartItemDTO(Integer id, String userPhone, Integer productId, String productName, 
                      String productDescription, Double productPrice, String productImageUrl, 
                      Integer quantity, Long createdTime, Long updatedTime) {
        this.id = id;
        this.userPhone = userPhone;
        this.productId = productId;
        this.productName = productName;
        this.productDescription = productDescription;
        this.productPrice = productPrice;
        this.productImageUrl = productImageUrl;
        this.quantity = quantity;
        this.createdTime = createdTime;
        this.updatedTime = updatedTime;
    }
    
    // Getter和Setter方法
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getUserPhone() {
        return userPhone;
    }
    
    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }
    
    public Integer getProductId() {
        return productId;
    }
    
    public void setProductId(Integer productId) {
        this.productId = productId;
    }
    
    public String getProductName() {
        return productName;
    }
    
    public void setProductName(String productName) {
        this.productName = productName;
    }
    
    public String getProductDescription() {
        return productDescription;
    }
    
    public void setProductDescription(String productDescription) {
        this.productDescription = productDescription;
    }
    
    public Double getProductPrice() {
        return productPrice;
    }
    
    public void setProductPrice(Double productPrice) {
        this.productPrice = productPrice;
    }
    
    public String getProductImageUrl() {
        return productImageUrl;
    }
    
    public void setProductImageUrl(String productImageUrl) {
        this.productImageUrl = productImageUrl;
    }
    
    public Integer getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }
    
    public Long getCreatedTime() {
        return createdTime;
    }
    
    public void setCreatedTime(Long createdTime) {
        this.createdTime = createdTime;
    }
    
    public Long getUpdatedTime() {
        return updatedTime;
    }
    
    public void setUpdatedTime(Long updatedTime) {
        this.updatedTime = updatedTime;
    }
}
