package cn.gzsf.javawebspringboot.service;

import cn.gzsf.javawebspringboot.entity.ProductShare;

import java.util.List;
import java.util.Map;

/**
 * 产品分享服务接口
 */
public interface ProductShareService {
    
    /**
     * 添加分享记录
     */
    boolean addShare(String userPhone, Long productId, String shareType);
    
    /**
     * 获取产品分享总数
     */
    int getProductShareCount(Long productId);
    
    /**
     * 获取用户分享列表
     */
    Map<String, Object> getUserShares(String userPhone, int page, int size);
    
    /**
     * 获取热门分享产品
     */
    List<ProductShare> getPopularShares(int limit);
    
    /**
     * 获取分享统计
     */
    List<Map<String, Object>> getShareStatsByType();
    
    /**
     * 获取所有分享记录（管理员用）
     */
    Map<String, Object> getAllShares(int page, int size);

    /**
     * 根据ID删除分享记录（管理员用）
     */
    boolean deleteShareById(Long id);
}
