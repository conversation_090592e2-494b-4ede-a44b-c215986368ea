# IDEA风格的SpringBoot启动脚本
Write-Host "🚀 IDEA风格启动SpringBoot应用..." -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Cyan

# 模拟IDEA的启动信息
Write-Host "📋 运行配置: SpringBoot Application" -ForegroundColor Yellow
Write-Host "📁 工作目录: $(Get-Location)" -ForegroundColor White
Write-Host "☕ 主类: cn.gzsf.javawebspringboot.JavaWebSpringBootApplication" -ForegroundColor White
Write-Host "🔧 VM选项: -Dserver.port=8082" -ForegroundColor White
Write-Host "🌍 环境变量: SPRING_PROFILES_ACTIVE=dev" -ForegroundColor White

Write-Host "================================================" -ForegroundColor Cyan

# 设置环境变量（模拟IDEA的环境设置）
$env:SPRING_PROFILES_ACTIVE = "dev"

# 确保资源文件是最新的（模拟IDEA的自动编译）
Write-Host "🔄 更新资源文件..." -ForegroundColor Yellow
if (Test-Path "target/classes") {
    Copy-Item -Recurse -Force "src/main/resources/*" "target/classes/" -ErrorAction SilentlyContinue
    Write-Host "✅ 资源文件已更新" -ForegroundColor Green
}

# 显示启动信息（模拟IDEA的控制台输出）
Write-Host ""
Write-Host "🎯 正在启动应用..." -ForegroundColor Green
Write-Host "📍 服务器端口: 8082" -ForegroundColor Cyan
Write-Host "🌐 访问地址: http://localhost:8082" -ForegroundColor Cyan
Write-Host ""
Write-Host "✨ 项目功能已实现:" -ForegroundColor Magenta
Write-Host "   ✅ 息壤臻选区域只显示新品（横向滚动）" -ForegroundColor White
Write-Host "   ✅ 分类导航下方显示分类产品（网格布局）" -ForegroundColor White
Write-Host "   ✅ 清理了所有404错误" -ForegroundColor White
Write-Host ""
Write-Host "================================================" -ForegroundColor Cyan
Write-Host "🚀 启动SpringBoot应用..." -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Cyan

# 启动应用（模拟IDEA的运行方式）
java -cp "target/classes" -Dserver.port=8082 -Dspring.profiles.active=dev cn.gzsf.javawebspringboot.JavaWebSpringBootApplication
