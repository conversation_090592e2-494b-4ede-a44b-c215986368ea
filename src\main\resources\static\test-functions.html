<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-item {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>🧪 功能测试页面</h1>
    <p>这个页面用于测试各个功能是否正常工作</p>

    <!-- 1. 评论图片上传测试 -->
    <div class="test-section">
        <h2>📸 评论图片上传测试</h2>
        <div class="test-item">
            <h3>测试步骤：</h3>
            <ol>
                <li>点击下面的按钮打开index.html</li>
                <li>选择任意产品，点击"发表评论"</li>
                <li>尝试上传图片</li>
                <li>检查图片是否能正常预览和上传</li>
            </ol>
            <button class="test-button" onclick="window.open('index.html', '_blank')">打开首页测试</button>
            <div id="comment-upload-status" class="status info">
                <strong>检查要点：</strong>
                <ul>
                    <li>图片选择后是否显示预览</li>
                    <li>上传过程中是否有进度提示</li>
                    <li>上传成功后是否显示成功消息</li>
                    <li>提交评论时图片是否包含在内</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 2. 收藏管理用户信息测试 -->
    <div class="test-section">
        <h2>❤️ 收藏管理用户信息测试</h2>
        <div class="test-item">
            <h3>测试步骤：</h3>
            <ol>
                <li>点击下面的按钮打开管理页面</li>
                <li>进入"收藏管理"页面</li>
                <li>检查收藏列表中的用户信息是否正确显示</li>
            </ol>
            <button class="test-button" onclick="window.open('admin.html', '_blank')">打开管理页面</button>
            <div id="favorite-status" class="status info">
                <strong>检查要点：</strong>
                <ul>
                    <li>用户头像是否正确显示</li>
                    <li>用户名是否正确显示</li>
                    <li>用户电话和账户信息是否显示</li>
                    <li>如果没有数据，检查数据库中是否有收藏记录</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 3. 产品分类全部测试 -->
    <div class="test-section">
        <h2>🏷️ 产品分类"全部"测试</h2>
        <div class="test-item">
            <h3>测试步骤：</h3>
            <ol>
                <li>打开首页</li>
                <li>点击产品分类中的"全部"</li>
                <li>检查是否显示所有产品</li>
            </ol>
            <button class="test-button" onclick="testCategoryAll()">测试分类功能</button>
            <div id="category-status" class="status info">
                <strong>检查要点：</strong>
                <ul>
                    <li>点击"全部"后是否显示产品</li>
                    <li>产品数量是否正确</li>
                    <li>如果没有产品，检查数据库中是否有产品数据</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 4. 头像实时更新测试 -->
    <div class="test-section">
        <h2>👤 头像实时更新测试</h2>
        <div class="test-item">
            <h3>测试步骤：</h3>
            <ol>
                <li>登录用户账号</li>
                <li>进入个人资料页面</li>
                <li>更换头像</li>
                <li>检查页面其他位置的头像是否同步更新</li>
            </ol>
            <button class="test-button" onclick="window.open('index.html', '_blank')">打开首页测试</button>
            <div id="avatar-status" class="status info">
                <strong>检查要点：</strong>
                <ul>
                    <li>更换头像后，导航栏头像是否更新</li>
                    <li>个人中心头像是否更新</li>
                    <li>其他页面的头像是否同步</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 5. 订单详情商品信息测试 -->
    <div class="test-section">
        <h2>📦 订单详情商品信息测试</h2>
        <div class="test-item">
            <h3>测试步骤：</h3>
            <ol>
                <li>登录用户账号</li>
                <li>进入个人中心的"我的订单"</li>
                <li>点击任意订单的"查看详情"</li>
                <li>检查商品信息是否正确显示</li>
            </ol>
            <button class="test-button" onclick="testOrderDetails()">测试订单详情</button>
            <div id="order-status" class="status info">
                <strong>检查要点：</strong>
                <ul>
                    <li>订单详情中是否显示商品信息</li>
                    <li>商品图片、名称、价格是否正确</li>
                    <li>如果没有商品信息，检查order_detail表</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 数据库检查工具 -->
    <div class="test-section">
        <h2>🗄️ 数据库检查工具</h2>
        <div class="test-item">
            <h3>快速数据检查：</h3>
            <button class="test-button" onclick="checkDatabase()">检查数据库状态</button>
            <button class="test-button" onclick="insertTestData()">插入测试数据</button>
            <div id="db-status" class="status info">
                点击上面的按钮检查数据库状态或插入测试数据
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script>
        // 测试分类功能
        function testCategoryAll() {
            const status = document.getElementById('category-status');
            status.className = 'status info';
            status.innerHTML = '正在测试分类功能...';
            
            axios.get('/products/category/0')
                .then(response => {
                    if (response.data.success) {
                        const products = response.data.data;
                        status.className = 'status success';
                        status.innerHTML = `✅ 分类接口正常！返回了 ${products.length} 个产品`;
                    } else {
                        status.className = 'status error';
                        status.innerHTML = `❌ 分类接口返回错误：${response.data.message}`;
                    }
                })
                .catch(error => {
                    status.className = 'status error';
                    status.innerHTML = `❌ 分类接口请求失败：${error.message}`;
                });
        }

        // 测试订单详情
        function testOrderDetails() {
            const status = document.getElementById('order-status');
            status.className = 'status info';
            status.innerHTML = '正在检查订单接口...';
            
            // 这里可以添加订单详情的测试逻辑
            axios.get('/api/order/list/13220248009')
                .then(response => {
                    if (response.data.success) {
                        const orders = response.data.data;
                        status.className = 'status success';
                        status.innerHTML = `✅ 订单接口正常！找到 ${orders.length} 个订单`;
                    } else {
                        status.className = 'status error';
                        status.innerHTML = `❌ 订单接口返回错误：${response.data.message}`;
                    }
                })
                .catch(error => {
                    status.className = 'status error';
                    status.innerHTML = `❌ 订单接口请求失败：${error.message}`;
                });
        }

        // 检查数据库状态
        function checkDatabase() {
            const status = document.getElementById('db-status');
            status.className = 'status info';
            status.innerHTML = '正在检查数据库状态...';
            
            Promise.all([
                axios.get('/products/category/0'),
                axios.get('/api/favorites/admin/all?page=1&size=5'),
                axios.get('/api/order/list/13220248009')
            ]).then(responses => {
                const [products, favorites, orders] = responses;
                let html = '<strong>数据库检查结果：</strong><ul>';
                
                if (products.data.success) {
                    html += `<li>✅ 产品数据：${products.data.data.length} 个产品</li>`;
                } else {
                    html += `<li>❌ 产品数据：${products.data.message}</li>`;
                }
                
                if (favorites.data.success) {
                    html += `<li>✅ 收藏数据：${favorites.data.total} 条收藏</li>`;
                } else {
                    html += `<li>❌ 收藏数据：${favorites.data.message}</li>`;
                }
                
                if (orders.data.success) {
                    html += `<li>✅ 订单数据：${orders.data.data.length} 个订单</li>`;
                } else {
                    html += `<li>❌ 订单数据：${orders.data.message}</li>`;
                }
                
                html += '</ul>';
                status.className = 'status success';
                status.innerHTML = html;
            }).catch(error => {
                status.className = 'status error';
                status.innerHTML = `❌ 检查失败：${error.message}`;
            });
        }

        // 插入测试数据
        function insertTestData() {
            const status = document.getElementById('db-status');
            status.className = 'status info';
            status.innerHTML = '正在插入测试数据...';
            
            // 这里可以调用后端接口插入测试数据
            status.className = 'status info';
            status.innerHTML = '请手动执行 src/main/resources/sql/test_data.sql 文件来插入测试数据';
        }
    </script>
</body>
</html>
