package cn.gzsf.javawebspringboot.service;

import cn.gzsf.javawebspringboot.entity.SecurityEvent;
import cn.gzsf.javawebspringboot.entity.IpManagement;
import java.util.List;
import java.util.Map;

/**
 * 管理员安全服务接口
 */
public interface AdminSecurityService {
    
    /**
     * 获取今日攻击次数
     */
    int getTodayAttackCount();
    
    /**
     * 获取黑名单IP数量
     */
    int getBlacklistIPCount();
    
    /**
     * 获取在线用户数量
     */
    int getOnlineUserCount();
    
    /**
     * 分页获取安全事件列表
     */
    List<SecurityEvent> getSecurityEventsByPage(Map<String, Object> params);
    
    /**
     * 获取安全事件总数
     */
    int getSecurityEventsCount();
    
    /**
     * 获取黑名单IP列表
     */
    List<IpManagement> getBlacklistIPs();
    
    /**
     * 获取白名单IP列表
     */
    List<IpManagement> getWhitelistIPs();
    
    /**
     * 添加IP到列表
     */
    void addIPToList(IpManagement ipManagement);
    
    /**
     * 从列表中移除IP
     */
    void removeIPFromList(Long id);
    
    /**
     * 处理安全事件
     */
    void handleSecurityEvent(Long id, String handler);
    
    /**
     * 清理旧的安全事件
     */
    int clearOldSecurityEvents();
    
    /**
     * 更新安全设置
     */
    void updateSecuritySettings(Map<String, Object> settings);
    
    /**
     * 记录安全事件
     */
    void recordSecurityEvent(String type, String ip, String description, String severity);
}
