package cn.gzsf.javawebspringboot.entity;

import java.time.LocalDateTime;

/**
 * 系统日志实体类
 */
public class SystemLog {
    private Long id;
    private String level;           // 日志级别：INFO, WARN, ERROR, DEBUG
    private String action;          // 操作类型：LOGIN, CREATE, UPDATE, DELETE, SELECT
    private String user;            // 操作用户
    private String description;     // 操作描述
    private String ip;              // 操作IP地址
    private String userAgent;       // 用户代理
    private LocalDateTime time;     // 操作时间
    private String details;         // 详细信息（JSON格式）

    public SystemLog() {}

    public SystemLog(String level, String action, String user, String description, String ip) {
        this.level = level;
        this.action = action;
        this.user = user;
        this.description = description;
        this.ip = ip;
        this.time = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public LocalDateTime getTime() {
        return time;
    }

    public void setTime(LocalDateTime time) {
        this.time = time;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    @Override
    public String toString() {
        return "SystemLog{" +
                "id=" + id +
                ", level='" + level + '\'' +
                ", action='" + action + '\'' +
                ", user='" + user + '\'' +
                ", description='" + description + '\'' +
                ", ip='" + ip + '\'' +
                ", time=" + time +
                '}';
    }
}
