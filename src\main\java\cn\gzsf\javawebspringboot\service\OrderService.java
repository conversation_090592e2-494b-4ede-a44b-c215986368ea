package cn.gzsf.javawebspringboot.service;

import cn.gzsf.javawebspringboot.dto.OrderCreateDTO;
import cn.gzsf.javawebspringboot.entity.UserOrder;

/**
 * 订单服务接口
 */
public interface OrderService {

    /**
     * 创建订单
     * @param orderCreateDTO 订单创建数据
     * @return 订单号，创建失败返回null
     */
    String createOrder(OrderCreateDTO orderCreateDTO);

    /**
     * 根据订单号获取订单详情
     * @param orderNo 订单号
     * @return 订单详情
     */
    UserOrder getOrderByOrderNo(String orderNo);

    /**
     * 支付订单
     * @param orderNo 订单号
     * @return 支付是否成功
     */
    boolean payOrder(String orderNo);

    /**
     * 取消订单
     * @param orderNo 订单号
     * @return 取消是否成功
     */
    boolean cancelOrder(String orderNo);

    /**
     * 确认收货
     * @param orderNo 订单号
     * @return 确认是否成功
     */
    boolean confirmReceived(String orderNo);

    /**
     * 删除订单
     * @param orderNo 订单号
     * @return 删除是否成功
     */
    boolean deleteOrder(String orderNo);
}
