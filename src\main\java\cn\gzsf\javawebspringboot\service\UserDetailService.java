package cn.gzsf.javawebspringboot.service;

import cn.gzsf.javawebspringboot.dto.UserDetailDTO;
import cn.gzsf.javawebspringboot.entity.*;
import cn.gzsf.javawebspringboot.mapper.UserDetailMapper;
import cn.gzsf.javawebspringboot.dao.UserDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 用户详情服务类
 */
@Service
@Transactional
public class UserDetailService {
    
    @Autowired
    private UserDetailMapper userDetailMapper;

    @Autowired
    private UserDao userDao;
    
    /**
     * 根据手机号获取用户完整详情信息
     */
    public UserDetailDTO getUserDetailByPhone(String phone) {
        // 获取基本用户信息
        User user = userDao.findUserByPhone(phone);
        if (user == null) {
            return null;
        }
        
        // 创建DTO对象
        UserDetailDTO dto = new UserDetailDTO();
        dto.setUserId(user.getUserId());
        dto.setUsername(user.getUsername());
        dto.setPhone(user.getPhone());
        dto.setRegisterTime(user.getRegisterTime());
        
        // 获取用户详情信息
        UserDetail userDetail = userDetailMapper.getUserDetailByPhone(phone);
        if (userDetail != null) {
            // 处理头像URL，确保不为空
            String avatarUrl = userDetail.getAvatarUrl();
            if (avatarUrl == null || avatarUrl.trim().isEmpty() || "null".equals(avatarUrl)) {
                avatarUrl = "default-avatar.jpg";
            }
            dto.setAvatarUrl(avatarUrl);
            dto.setSignature(userDetail.getSignature());

            System.out.println("📋 获取用户详情 - 手机号: " + phone + ", 头像: " + avatarUrl + ", 签名: " + userDetail.getSignature());
        } else {
            // 如果用户详情不存在，设置默认值
            dto.setAvatarUrl("default-avatar.jpg");
            dto.setSignature("这个人很懒，什么都没留下~");

            System.out.println("⚠️ 用户详情不存在，使用默认值 - 手机号: " + phone);
        }
        
        // 获取地址信息
        List<UserAddress> addresses = userDetailMapper.getUserAddressesByPhone(phone);
        dto.setAddresses(addresses);
        
        // 获取购物车统计
        Integer cartItemCount = userDetailMapper.getCartItemCountByPhone(phone);
        dto.setCartItemCount(cartItemCount != null ? cartItemCount : 0);
        
        // 获取订单统计
        Integer totalOrderCount = userDetailMapper.getTotalOrderCountByPhone(phone);
        Integer pendingOrderCount = userDetailMapper.getPendingOrderCountByPhone(phone);
        dto.setTotalOrderCount(totalOrderCount != null ? totalOrderCount : 0);
        dto.setPendingOrderCount(pendingOrderCount != null ? pendingOrderCount : 0);
        
        // 获取好友统计
        Integer friendCount = userDetailMapper.getFriendCountByPhone(phone);
        dto.setFriendCount(friendCount != null ? friendCount : 0);
        
        return dto;
    }
    
    /**
     * 创建或更新用户详情
     */
    public boolean saveUserDetail(UserDetail userDetail) {
        UserDetail existing = userDetailMapper.getUserDetailByPhone(userDetail.getPhone());

        if (existing == null) {
            // 创建新的用户详情
            userDetail.setCreatedTime(System.currentTimeMillis());
            userDetail.setUpdatedTime(System.currentTimeMillis());
            return userDetailMapper.insertUserDetail(userDetail) > 0;
        } else {
            // 更新现有用户详情
            userDetail.setUpdatedTime(System.currentTimeMillis());

            // 如果avatarUrl为空，保留原有的头像
            if (userDetail.getAvatarUrl() == null || userDetail.getAvatarUrl().trim().isEmpty()) {
                userDetail.setAvatarUrl(existing.getAvatarUrl());
            }

            return userDetailMapper.updateUserDetail(userDetail) > 0;
        }
    }

    /**
     * 只更新用户签名
     */
    public boolean updateUserSignature(String phone, String signature) {
        long updatedTime = System.currentTimeMillis();
        return userDetailMapper.updateUserSignature(phone, signature, updatedTime) > 0;
    }

    /**
     * 只更新用户头像
     */
    public boolean updateUserAvatar(String phone, String avatarUrl) {
        long updatedTime = System.currentTimeMillis();
        return userDetailMapper.updateUserAvatar(phone, avatarUrl, updatedTime) > 0;
    }

    /**
     * 获取用户统计数据
     */
    public Map<String, Object> getUserStats() {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 获取总用户数（从用户DAO）
            int totalUsers = userDao.getTotalUserCount();

            // 获取用户详情总数
            int totalUserDetails = userDetailMapper.getTotalUserDetailCount();

            // 计算活跃用户数（假设80%的用户是活跃的）
            int activeUsers = (int) (totalUsers * 0.8);

            // 计算今日新增用户（模拟数据，实际应该从数据库查询）
            int todayNew = (int) (Math.random() * 10) + 1;

            // 计算VIP用户数（假设10%的用户是VIP）
            int vipUsers = (int) (totalUsers * 0.1);

            stats.put("total", totalUsers);
            stats.put("active", activeUsers);
            stats.put("todayNew", todayNew);
            stats.put("vip", vipUsers);

            System.out.println("📊 用户统计数据: " + stats);

        } catch (Exception e) {
            e.printStackTrace();
            // 返回默认统计数据
            stats.put("total", 0);
            stats.put("active", 0);
            stats.put("todayNew", 0);
            stats.put("vip", 0);
        }

        return stats;
    }
    
    /**
     * 获取用户地址列表
     */
    public List<UserAddress> getUserAddresses(String phone) {
        return userDetailMapper.getUserAddressesByPhone(phone);
    }
    
    /**
     * 添加用户地址
     */
    public boolean addUserAddress(UserAddress address) {
        address.setCreatedTime(System.currentTimeMillis());
        address.setUpdatedTime(System.currentTimeMillis());
        return userDetailMapper.insertUserAddress(address) > 0;
    }
    
    /**
     * 获取用户购物车商品数量
     */
    public Integer getCartItemCount(String phone) {
        Integer count = userDetailMapper.getCartItemCountByPhone(phone);
        return count != null ? count : 0;
    }
    
    /**
     * 获取用户购物车详情
     */
    public List<ShoppingCart> getCartItems(String phone) {
        return userDetailMapper.getCartItemsByPhone(phone);
    }
    
    /**
     * 获取用户订单统计
     */
    public Integer getTotalOrderCount(String phone) {
        Integer count = userDetailMapper.getTotalOrderCountByPhone(phone);
        return count != null ? count : 0;
    }
    
    public Integer getPendingOrderCount(String phone) {
        Integer count = userDetailMapper.getPendingOrderCountByPhone(phone);
        return count != null ? count : 0;
    }
    
    /**
     * 获取用户最近订单
     */
    public List<UserOrder> getRecentOrders(String phone, int limit) {
        return userDetailMapper.getRecentOrdersByPhone(phone, limit);
    }
    
    /**
     * 获取用户好友数量
     */
    public Integer getFriendCount(String phone) {
        Integer count = userDetailMapper.getFriendCountByPhone(phone);
        return count != null ? count : 0;
    }
    
    /**
     * 获取用户好友列表
     */
    public List<UserFriend> getFriends(String phone) {
        return userDetailMapper.getFriendsByPhone(phone);
    }
}
