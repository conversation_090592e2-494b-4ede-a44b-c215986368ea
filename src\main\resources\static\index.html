<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>息壤集 - 自然臻选</title>
    <!-- 添加favicon，防止404错误 -->
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64,AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAAAQAABILAAASCwAAAAAAAAAAAAD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A">
    <link rel="stylesheet" href="index.css?v=2024010308">
    <link rel="stylesheet" href="css/cart-enhanced.css">
    <script src="index_new.js" defer></script>
    <!-- 引入Font Awesome 字体图标库，只需要用这条命令链接Font Awesome上，直接就能在.css调用 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- 引入 Axios 库 -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>

</head>
<body>
<!-- 浮动装饰云朵 -->
<div class="deco-cloud cloud-left"></div>
<div class="deco-cloud cloud-right"></div>

<!-- 头部导航 -->
<header class="sky-header">
    <!-- 息壤集品牌标识 -->
    <div class="brand-logo">
        <div class="logo-container">
            <span class="logo-icon">🌱</span>
            <div class="logo-text">
                <h1 class="brand-name">息壤集</h1>
               
            </div>
        </div>
    </div>
    <nav class="nav-menu">
        <ul> <!-- 所有导航项都包含在同一个ul中 -->


            <!-- 新增搜索容器，保持导航栏布局不破坏 -->
            <li class="search-container">
                <div class="search-box">
                    <!-- 搜索输入框 -->
                    <input type="text"
                           id="searchInput"
                           placeholder="搜索产品名称或描述"
                           class="search-input">
                    <!-- 搜索按钮，包含在搜索栏中 -->
                    <button type="button"
                            id="searchBtn"
                            class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                    <!-- 搜索建议下拉列表 -->
                    <div class="search-suggestions" id="suggestions"></div>
                </div>
            </li>



            <li>
                <a href="#home">
                    <i class="fas fa-cloud"></i><!--图标，在图标库里的名字：fas fa-cloud-->
                    <span>首页</span>
                </a>
            </li>
            <li>
                <a href="#products">
                    <i class="fas fa-spa"></i>
                    <span>产品</span>
                </a>
            </li>
            <!--<li><a href="#tech"><i class="fas fa-flask"></i>科技研发</a></li>
            <li><a href="#contact"><i class="fas fa-envelope"></i>天境之约</a></li>-->
            <!-- 购物车图标 -->
            <li class="cart-li">
                <a href="#cart">
                    <div class="cart-counter">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="cart-count">0</span>
                    </div>
                    <span>购物车</span>
                </a>
            </li>
            <!-- 用户中心图标 -->
            <li class="user-center-li">
                <a href="javascript:void(0)" onclick="window.userCenter?.handleUserCenterClick()">
                    <div class="user-avatar">
                        <!-- 使用同一张图片作为导航栏图标和用户中心头像 -->
                        <img src="images/avatar/default-avatar.jpg" alt="用户头像" class="avatar-icon"  id="userAvatarBtn"><!--使用<img>标签实现可点击区域-->
                    </div>
                    <span></span>
                </a>
            </li>
        </ul>
    </nav>
</header>


<!-- 用户中心模态框 -->
<div class="user-center-modal">
    <div class="user-center-content">
        <!-- 关闭按钮 -->
        <button class="close-user-center" onclick="window.userCenter?.toggleUserCenter(false)">&times;</button>

        <!-- 头部标题 -->
        <div class="user-center-header">
            <h2>个人中心</h2>
            <p class="header-subtitle">管理您的账户信息</p>
        </div>

        <!-- 可滚动内容区域 -->
        <div class="user-center-scrollable">
            <!-- 用户信息区 -->
            <div class="user-profile-section">
                <div class="user-profile">
                    <div class="avatar-container">
                        <img src="images/avatar/default-avatar.jpg" alt="用户头像" class="profile-avatar" id="profileAvatar">
                        <div class="avatar-overlay" onclick="window.userCenter?.showAvatarModal()">
                            <i class="fas fa-camera"></i>
                            <span class="avatar-overlay-text">更换头像</span>
                        </div>
                        <!-- 隐藏的文件上传输入框 -->
                        <input type="file" id="avatarUpload" accept="image/*" style="display: none;">
                    </div>
                    <div class="profile-info">
                        <h3 class="username">点击登录</h3>
                        <p class="account-id">ID: N/A</p>
                        <div class="user-signature" onclick="window.userCenter?.editSignature()">
                            <i class="fas fa-edit signature-edit-icon"></i>
                            <span class="signature-text">这个人很懒，什么都没留下~</span>
                        </div>
                        <div class="user-level">
                            <span class="level-badge">普通会员</span>
                            <div class="level-progress">
                                <div class="progress-bar" style="width: 30%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户统计信息 -->
                <div class="user-stats">
                    <div class="stat-item clickable" onclick="window.userCenter?.viewCart()">
                        <div class="stat-icon">🛒</div>
                        <div class="stat-number stat-cart-count">0</div>
                        <div class="stat-label">购物车</div>
                    </div>
                    <div class="stat-item clickable" onclick="window.userCenter?.viewAllOrders()">
                        <div class="stat-icon">📦</div>
                        <div class="stat-number stat-order-count">0</div>
                        <div class="stat-label">订单</div>
                    </div>
                    <div class="stat-item clickable" onclick="window.userCenter?.viewFriends()">
                        <div class="stat-icon">👥</div>
                        <div class="stat-number stat-friend-count">0</div>
                        <div class="stat-label">好友</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon">💰</div>
                        <div class="stat-number stat-balance">0</div>
                        <div class="stat-label">余额</div>
                    </div>
                </div>
            </div>

            <!-- 快捷功能区 -->
            <div class="quick-actions">
                <h4 class="section-title">快捷功能</h4>
                <div class="action-grid">
                    <button class="action-item" onclick="window.userCenter?.viewOrders()">
                        <i class="fas fa-box-open"></i>
                        <span>我的订单</span>
                    </button>

                    <button class="action-item" onclick="window.userCenter?.viewFavorites()">
                        <i class="fas fa-heart"></i>
                        <span>我的收藏</span>
                    </button>
                    <button class="action-item" onclick="window.userCenter?.viewComments()">
                        <i class="fas fa-comment-dots"></i>
                        <span>我的评论</span>
                    </button>
                    <button class="action-item" onclick="window.userCenter?.viewShares()">
                        <i class="fas fa-share-alt"></i>
                        <span>我的分享</span>
                    </button>
                    <button class="action-item" onclick="window.userCenter?.viewCoupons()">
                        <i class="fas fa-ticket-alt"></i>
                        <span>优惠券</span>
                    </button>
                    <button class="action-item" onclick="window.userCenter?.viewSettings()">
                        <i class="fas fa-cog"></i>
                        <span>设置</span>
                    </button>
                    <button class="action-item" onclick="window.viewShoppingCart()">
                        <i class="fas fa-shopping-cart"></i>
                        <span>购物车</span>
                    </button>
                </div>
            </div>

            <!-- 详细信息区域 -->
            <div class="user-details-section">

            </div>

            <!-- 功能菜单 -->
            <nav class="user-menu">
                <div class="menu-section">
                    <h4 class="menu-section-title">个人信息</h4>
                    <div class="menu-row">
                        <button class="menu-item full-width" onclick="window.userCenter?.viewProfile()">
                            <i class="fas fa-user"></i>
                            <span>个人资料</span>
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                    <div class="menu-row">
                        <button class="menu-item full-width" onclick="window.userCenter?.viewBalance()">
                            <i class="fas fa-wallet"></i>
                            <span>我的余额</span>
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                    <div class="menu-row">
                        <button class="menu-item full-width" onclick="window.userCenter?.viewFriends()">
                            <i class="fas fa-users"></i>
                            <span>我的好友</span>
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>

                <div class="menu-section">
                    <h4 class="menu-section-title">我的订单</h4>
                    <div class="menu-row">
                        <button class="menu-item full-width" onclick="window.userCenter?.viewOrders()">
                            <i class="fas fa-shopping-bag"></i>
                            <span>我的订单</span>
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>

                <div class="menu-section">
                    <h4 class="menu-section-title">我的活动</h4>
                    <div class="menu-row">
                        <button class="menu-item full-width" onclick="window.userCenter?.viewFavorites()">
                            <i class="fas fa-heart"></i>
                            <span>我的收藏</span>
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                    <div class="menu-row">
                        <button class="menu-item full-width" onclick="window.userCenter?.viewComments()">
                            <i class="fas fa-comment"></i>
                            <span>我的评论</span>
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                    <div class="menu-row">
                        <button class="menu-item full-width" onclick="window.userCenter?.viewShares()">
                            <i class="fas fa-share"></i>
                            <span>我的分享</span>
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>

                <div class="menu-section">
                    <h4 class="menu-section-title">购物工具</h4>
                    <div class="menu-row">
                        <button class="menu-item full-width" onclick="window.viewShoppingCart()">
                            <i class="fas fa-shopping-cart"></i>
                            <span>购物车</span>
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>

                <div class="menu-section">
                    <h4 class="menu-section-title">设置与帮助</h4>
                    <div class="menu-row">
                        <button class="menu-item full-width" onclick="window.userCenter?.settings()">
                            <i class="fas fa-cog"></i>
                            <span>设置</span>
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                    <div class="menu-row">
                        <button class="menu-item full-width" onclick="window.userCenter?.helpCenter()">
                            <i class="fas fa-question-circle"></i>
                            <span>帮助中心</span>
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                    <div class="menu-row">
                        <button class="menu-item full-width" onclick="window.userCenter?.feedback()">
                            <i class="fas fa-comment-alt"></i>
                            <span>意见反馈</span>
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                    <div class="menu-row">
                        <button class="menu-item full-width switch-account" onclick="window.userCenter?.showSwitchAccountModal()">
                            <i class="fas fa-exchange-alt"></i>
                            <span>切换账户</span>
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </nav>
        </div>
    </div>
</div>




<!-- 切换账户确认模态框 -->
<div class="confirm-modal">
    <div class="confirm-content">
        <h3>账户切换确认</h3>
        <p>确定要切换当前登录账户吗？</p>
        <div class="confirm-buttons">
            <!-- 取消按钮 -->
            <button class="confirm-cancel" onclick="window.userCenter?.hideSwitchAccountModal()">取消</button>
            <!-- 确认按钮 -->
            <button class="confirm-ok" onclick="window.userCenter?.confirmSwitchAccount()">确定切换</button>
        </div>
    </div>
</div>

<!-- 个性签名编辑模态框 -->
<div class="signature-modal">
    <div class="signature-content">
        <div class="signature-header">
            <h3>编辑个性签名</h3>
            <button class="close-signature" onclick="window.userCenter?.closeSignatureModal()">&times;</button>
        </div>
        <div class="signature-body">
            <textarea id="signatureInput" placeholder="写下您的个性签名..." maxlength="100" oninput="window.userCenter?.updateSignatureCounter()"></textarea>
            <div class="signature-counter">
                <span id="signatureCount">0</span>/100
            </div>
        </div>
        <div class="signature-footer">
            <button class="signature-cancel" onclick="window.userCenter?.closeSignatureModal()">取消</button>
            <button class="signature-save" onclick="window.userCenter?.saveSignature()">保存</button>
        </div>
    </div>
</div>

<!-- 头像上传模态框 -->
<div class="avatar-modal">
    <div class="avatar-content">
        <div class="avatar-header">
            <h3>更换头像</h3>
            <button class="close-avatar" onclick="window.userCenter?.closeAvatarModal()">&times;</button>
        </div>
        <div class="avatar-body">
            <div class="avatar-preview">
                <img id="avatarPreview" src="images/avatar/default-avatar.jpg" alt="头像预览">
            </div>
            <div class="avatar-upload-area">
                <div class="upload-zone" onclick="document.getElementById('avatarFileInput').click()">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <p>点击上传头像</p>
                    <span>支持 JPG、PNG 格式，文件大小不超过 2MB</span>
                </div>
                <input type="file" id="avatarFileInput" accept="image/*" style="display: none;" onchange="window.userCenter?.handleAvatarUpload(this.files[0])">
            </div>
            <div class="avatar-presets">
                <h4>选择默认头像</h4>
                <div class="preset-avatars">
                    <img src="images/avatar/avatar1.jpg" alt="默认头像1" class="preset-avatar" data-avatar="avatar1.jpg" onclick="window.userCenter?.selectPresetAvatar('avatar1.jpg')">
                    <img src="images/avatar/avatar2.jpg" alt="默认头像2" class="preset-avatar" data-avatar="avatar2.jpg" onclick="window.userCenter?.selectPresetAvatar('avatar2.jpg')">
                    <img src="images/avatar/avatar3.jpg" alt="默认头像3" class="preset-avatar" data-avatar="avatar3.jpg" onclick="window.userCenter?.selectPresetAvatar('avatar3.jpg')">
                    <img src="images/avatar/avatar4.jpg" alt="默认头像4" class="preset-avatar" data-avatar="avatar4.jpg" onclick="window.userCenter?.selectPresetAvatar('avatar4.jpg')">
                </div>
            </div>
        </div>
        <div class="avatar-footer">
            <button class="avatar-cancel" onclick="window.userCenter?.closeAvatarModal()">取消</button>
            <button class="avatar-save" onclick="window.userCenter?.saveAvatar()">保存</button>
        </div>
    </div>
</div>

<!-- 我的收藏模态框 -->
<div class="favorites-modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3><i class="fas fa-heart"></i> 我的收藏</h3>
            <button class="close-modal" onclick="window.closeAllModals()">&times;</button>
        </div>
        <div class="modal-body">
            <div class="favorites-list" id="favoritesList">
                <div class="loading-favorites">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>加载中...</p>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <div class="pagination-container" id="favoritesPagination"></div>
        </div>
    </div>
</div>

<!-- 我的评论模态框 -->
<div class="comments-modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3><i class="fas fa-comment-dots"></i> 我的评论</h3>
            <button class="close-modal" onclick="window.closeAllModals()">&times;</button>
        </div>
        <div class="modal-body">
            <div class="comments-list" id="commentsList">
                <div class="loading-comments">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>加载中...</p>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <div class="pagination-container" id="commentsPagination"></div>
        </div>
    </div>
</div>

<!-- 我的分享模态框 -->
<div class="shares-modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3><i class="fas fa-share-alt"></i> 我的分享</h3>
            <button class="close-modal" onclick="window.closeAllModals()">&times;</button>
        </div>
        <div class="modal-body">
            <div class="shares-list" id="sharesList">
                <div class="loading-shares">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>加载中...</p>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <div class="pagination-container" id="sharesPagination"></div>
        </div>
    </div>
</div>



<!-- 轮播图区 -->
<section class="carousel-section" id="home">
    <div class="carousel-wrapper">
        <div class="sky-carousel">
            <div class="carousel-container" id="carouselContainer">
                <!-- 动态加载轮播图 -->
                <div class="carousel-item active" style="background-image: url('/images/074dc74f-ab1e-4241-ad36-40a3dae3234a.jpg')">
                    <div class="carousel-caption">
                        <h2>息壤天然植萃</h2>
                        <p>源自大地的纯净力量，滋养肌肤本真之美</p>
                    </div>
                </div>
                <!-- 更多轮播项将通过JavaScript动态加载 -->
            </div>
            <div class="carousel-dots" id="carouselDots"></div>
        </div>
    </div>
</section>

<!-- 新品展示区 -->
<section class="new-products" id="products">
    <h2 class="section-title"><span>🌿 息壤臻选 · 自然臻选</span></h2>
    <div class="product-grid">
        <!-- 新品将通过JavaScript动态加载 -->
        <div class="loading-hint">
            <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: var(--sky-blue); margin-bottom: 1rem;"></i>
            <p style="color: var(--text-dark);">正在加载新品...</p>
        </div>
    </div>
</section>

<!--产品分类展示区-->
<section class="product-categories" id="categories">
    <h2 class="section-title"><span>🛍️ 产品分类</span></h2>
    <div class="category-filter">
        <ul class="category-list">
            <li class="category-item active" data-category="all" data-category-id="0">
                <i class="fas fa-star"></i>
                <span>全部</span>
            </li>
            <li class="category-item" data-category="skincare" data-category-id="1">
                <i class="fas fa-spa"></i>
                <span>护肤品</span>
            </li>
            <li class="category-item" data-category="makeup" data-category-id="2">
                <i class="fas fa-paint-brush"></i>
                <span>彩妆</span>
            </li>
            <li class="category-item" data-category="perfume" data-category-id="3">
                <i class="fas fa-wine-bottle"></i>
                <span>香水</span>
            </li>
            <li class="category-item" data-category="tools" data-category-id="4">
                <i class="fas fa-toolbox"></i>
                <span>工具</span>
            </li>
        </ul>
    </div>

    <!-- 动态产品容器 -->
    <div class="dynamic-products">
        <div class="category-hint">
            <i class="fas fa-mouse-pointer" style="font-size: 3rem; color: var(--sky-blue); margin-bottom: 1rem;"></i>
            <p style="color: var(--text-dark); font-size: 1.2rem;">点击上方分类标签查看对应产品</p>
        </div>
    </div>
</section>






<!-- 返回顶部 末尾添加 -->
<!-- 返回顶部按钮 -->
<!-- 使用Font Awesome箭头图标，初始状态隐藏 超过一点距离浮现-->
<button class="back-to-top" title="返回顶部" aria-label="返回顶部">
    <i class="fas fa-arrow-up"></i><!--这里给按钮加字但是太丑了-->
</button>


<!-- 页脚 -->
<footer class="sky-footer">
    <div class="footer-content">
        <!-- 版权信息 -->
        <p class="copyright">
            &copy; 2024 fresh skin美肤科技有限公司
            <!-- 备案信息（示例数据，需替换为真实备案号） -->
            <br>  <!-- 换行分隔 -->
            <a href="http://beian.miit.gov.cn/"
               target="_blank"
               rel="noopener noreferrer"
               class="beian-link">
                京ICP备12345678号-1
            </a><!--这个网址是工信部网站-->
        </p>
    </div>
</footer>

<!-- 购物车模态框 -->
<div class="cart-modal">
    <div class="cart-content">
        <div class="cart-header">
            <h3>购物车 (<span class="modal-cart-count">0</span>)</h3>
            <button class="close-cart">&times;</button>
        </div>
        <div class="cart-body">
            <ul class="cart-items"></ul>
            <!-- 空购物车提示 -->
            <div class="empty-cart" style="display: none;">
                <i class="fas fa-shopping-cart" style="font-size: 3rem; color: #ddd; margin-bottom: 1rem;"></i>
                <p>购物车是空的</p>
                <p style="font-size: 14px; color: #999; margin-top: 0.5rem;">快去挑选心仪的商品吧！</p>
                <button class="btn-continue-shopping" onclick="window.location.href='#products'" style="margin-top: 1rem; padding: 8px 16px; background: var(--sky-blue); color: white; border: none; border-radius: 4px; cursor: pointer;">
                    继续购物
                </button>
            </div>
        </div>
        <div class="cart-footer">
            <!-- 批量操作区域 -->
            <div class="cart-batch-operations">
                <label class="select-all">
                    <input type="checkbox" id="selectAllCart"> 全选
                </label>
                <button class="btn-clear-selected" onclick="window.shoppingCart?.clearSelectedItems()">
                    删除选中
                </button>
                <button class="btn-clear-all" onclick="window.shoppingCart?.clearAllItems()" style="padding: 6px 12px; border: 1px solid #ff4757; background: #fff; color: #ff4757; border-radius: 4px; cursor: pointer; font-size: 14px; margin-left: 10px;">
                    清空购物车
                </button>
            </div>

            <!-- 结算区域 -->
            <div class="cart-summary">
                <div class="total-info">
                    <span>已选择 <span id="selectedItemsCount">0</span> 件商品</span>
                    <span class="total-price">合计：¥<span id="totalPrice">0.00</span></span>
                </div>
                <button class="btn-checkout" onclick="window.shoppingCart?.checkout()" disabled>
                    立即结算
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 订单确认模态框 -->
<div class="order-confirmation-modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>确认订单</h3>
            <button class="close-modal" onclick="window.shoppingCart?.closeOrderConfirmation()">&times;</button>
        </div>
        <div class="modal-body">
            <!-- 收货地址选择 -->
            <div class="address-selection">
                <h4>选择收货地址</h4>
                <div class="address-list" id="orderAddressList">
                    <div class="no-address">
                        <p>暂无收货地址，请先添加地址</p>
                        <button class="btn-add-address" onclick="window.userCenter?.manageAddresses()">
                            添加收货地址
                        </button>
                    </div>
                </div>
            </div>

            <!-- 商品清单 -->
            <div class="order-items">
                <h4>商品清单</h4>
                <div class="order-items-list" id="orderItemsList">
                    <!-- 动态生成订单商品列表 -->
                </div>
            </div>

            <!-- 订单备注 -->
            <div class="order-remark">
                <h4>订单备注</h4>
                <textarea id="orderRemark" placeholder="请输入订单备注（选填）" style="width: 100%; height: 60px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; resize: vertical;"></textarea>
            </div>

            <!-- 费用明细 -->
            <div class="order-summary-detail">
                <div class="summary-row">
                    <span>商品总价：</span>
                    <span>¥<span id="orderSubtotal">0.00</span></span>
                </div>
                <div class="summary-row">
                    <span>运费：</span>
                    <span>¥<span id="orderShipping">0.00</span></span>
                </div>
                <div class="summary-row total">
                    <span>应付总额：</span>
                    <span class="total-amount">¥<span id="orderTotal">0.00</span></span>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn-cancel" onclick="window.shoppingCart?.closeOrderConfirmation()">取消</button>
            <button class="btn-confirm-order" onclick="window.shoppingCart?.submitOrder()">确认下单</button>
        </div>
    </div>
</div>

<!-- 支付成功模态框 -->
<div class="payment-success-modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>支付成功</h3>
            <button class="close-modal" onclick="window.shoppingCart?.closePaymentSuccess()">&times;</button>
        </div>
        <div class="modal-body" style="text-align: center; padding: 40px 20px;">
            <i class="fas fa-check-circle" style="font-size: 4rem; color: #52c41a; margin-bottom: 1rem;"></i>
            <h4 style="margin-bottom: 0.5rem;">支付成功！</h4>
            <p style="color: #666; margin-bottom: 1rem;">订单号：<span id="successOrderNo"></span></p>
            <p style="color: #666; margin-bottom: 2rem;">您的订单已提交，我们会尽快为您处理</p>
            <div style="display: flex; gap: 1rem; justify-content: center;">
                <button class="btn-view-order" onclick="window.userCenter?.viewAllOrders()" style="padding: 10px 20px; background: var(--sky-blue); color: white; border: none; border-radius: 4px; cursor: pointer;">
                    查看订单
                </button>
                <button class="btn-continue-shopping" onclick="window.shoppingCart?.closePaymentSuccess(); window.location.href='#products'" style="padding: 10px 20px; background: #fff; color: var(--sky-blue); border: 1px solid var(--sky-blue); border-radius: 4px; cursor: pointer;">
                    继续购物
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 产品评论模态框 -->
<div class="product-comment-modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3><i class="fas fa-comment-dots"></i> 发表评论</h3>
            <button class="close-modal" onclick="window.productComment?.closeCommentModal()">&times;</button>
        </div>
        <div class="modal-body">
            <div class="comment-form">
                <div class="product-info">
                    <img id="commentProductImage" src="" alt="产品图片" class="comment-product-img">
                    <div class="product-details">
                        <h4 id="commentProductName"></h4>
                        <p id="commentProductPrice"></p>
                    </div>
                </div>

                <div class="rating-section">
                    <label>评分：</label>
                    <div class="star-rating" id="starRating">
                        <span class="star" data-rating="1">★</span>
                        <span class="star" data-rating="2">★</span>
                        <span class="star" data-rating="3">★</span>
                        <span class="star" data-rating="4">★</span>
                        <span class="star" data-rating="5">★</span>
                    </div>
                    <span class="rating-text" id="ratingText">请选择评分</span>
                </div>

                <div class="comment-content">
                    <label>评论内容：</label>
                    <textarea id="commentContent" placeholder="请分享您的使用体验..." maxlength="500"></textarea>
                    <div class="char-count">
                        <span id="commentCharCount">0</span>/500
                    </div>
                </div>

                <div class="comment-images">
                    <label>上传图片（可选）：</label>
                    <div class="image-upload-area">
                        <div class="upload-preview" id="commentImagePreview"></div>
                        <div class="upload-btn" onclick="document.getElementById('commentImageInput').click()">
                            <i class="fas fa-camera"></i>
                            <span>添加图片</span>
                        </div>
                        <input type="file" id="commentImageInput" accept="image/*" multiple style="display: none;">
                    </div>
                    <div class="upload-tips">
                        <p>支持JPG、PNG格式，最多上传5张图片，每张不超过2MB</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn-cancel" onclick="window.productComment?.closeCommentModal()">取消</button>
            <button class="btn-submit-comment" onclick="window.productComment?.submitComment()">发表评论</button>
        </div>
    </div>
</div>

<!-- 产品评论列表模态框 -->
<div class="product-comments-list-modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3><i class="fas fa-comments"></i> 用户评论</h3>
            <button class="close-modal" onclick="window.productComment?.closeCommentsListModal()">&times;</button>
        </div>
        <div class="modal-body">
            <div class="comments-summary">
                <div class="rating-overview">
                    <div class="average-rating">
                        <span class="rating-score" id="averageRatingScore">0.0</span>
                        <div class="rating-stars" id="averageRatingStars"></div>
                        <p>基于 <span id="totalCommentsCount">0</span> 条评论</p>
                    </div>
                </div>
            </div>

            <div class="comments-filter">
                <button class="filter-btn active" data-filter="all">全部</button>
                <button class="filter-btn" data-filter="5">5星</button>
                <button class="filter-btn" data-filter="4">4星</button>
                <button class="filter-btn" data-filter="3">3星</button>
                <button class="filter-btn" data-filter="2">2星</button>
                <button class="filter-btn" data-filter="1">1星</button>
                <button class="filter-btn" data-filter="images">有图</button>
            </div>

            <div class="comments-list" id="productCommentsList">
                <div class="loading-comments">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>加载评论中...</p>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <div class="pagination-container" id="commentsListPagination"></div>
        </div>
    </div>
</div>

</body>
</html>
