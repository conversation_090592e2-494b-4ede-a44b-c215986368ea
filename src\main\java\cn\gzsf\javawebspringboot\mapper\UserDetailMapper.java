package cn.gzsf.javawebspringboot.mapper;

import cn.gzsf.javawebspringboot.entity.*;
import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * 用户详情Mapper接口
 */
@Mapper
public interface UserDetailMapper {
    
    // ==================== 用户详情相关 ====================
    
    /**
     * 根据手机号查询用户详情
     */
    @Select("SELECT * FROM user_detail WHERE phone = #{phone}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "phone", column = "phone"),
        @Result(property = "avatarUrl", column = "avatar_url"),
        @Result(property = "signature", column = "signature"),
        @Result(property = "createdTime", column = "created_time"),
        @Result(property = "updatedTime", column = "updated_time")
    })
    UserDetail getUserDetailByPhone(@Param("phone") String phone);
    
    /**
     * 插入用户详情
     */
    @Insert("INSERT INTO user_detail (phone, avatar_url, signature, created_time, updated_time) " +
            "VALUES (#{phone}, #{avatarUrl}, #{signature}, #{createdTime}, #{updatedTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUserDetail(UserDetail userDetail);
    
    /**
     * 更新用户详情
     */
    @Update("UPDATE user_detail SET avatar_url = #{avatarUrl}, signature = #{signature}, " +
            "updated_time = #{updatedTime} WHERE phone = #{phone}")
    int updateUserDetail(UserDetail userDetail);

    /**
     * 只更新用户签名
     */
    @Update("UPDATE user_detail SET signature = #{signature}, updated_time = #{updatedTime} WHERE phone = #{phone}")
    int updateUserSignature(@Param("phone") String phone, @Param("signature") String signature, @Param("updatedTime") Long updatedTime);

    /**
     * 只更新用户头像
     */
    @Update("UPDATE user_detail SET avatar_url = #{avatarUrl}, updated_time = #{updatedTime} WHERE phone = #{phone}")
    int updateUserAvatar(@Param("phone") String phone, @Param("avatarUrl") String avatarUrl, @Param("updatedTime") Long updatedTime);

    /**
     * 获取用户详情总数
     */
    @Select("SELECT COUNT(*) FROM user_detail")
    int getTotalUserDetailCount();
    
    // ==================== 用户地址相关 ====================
    
    /**
     * 根据用户手机号查询地址列表
     */
    @Select("SELECT * FROM user_address WHERE user_phone = #{userPhone} ORDER BY is_default DESC, created_time DESC")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "userPhone", column = "user_phone"),
        @Result(property = "receiverName", column = "receiver_name"),
        @Result(property = "receiverPhone", column = "receiver_phone"),
        @Result(property = "province", column = "province"),
        @Result(property = "city", column = "city"),
        @Result(property = "district", column = "district"),
        @Result(property = "detailAddress", column = "detail_address"),
        @Result(property = "postalCode", column = "postal_code"),
        @Result(property = "isDefault", column = "is_default"),
        @Result(property = "createdTime", column = "created_time"),
        @Result(property = "updatedTime", column = "updated_time")
    })
    List<UserAddress> getUserAddressesByPhone(@Param("userPhone") String userPhone);
    
    /**
     * 插入用户地址
     */
    @Insert("INSERT INTO user_address (user_phone, receiver_name, receiver_phone, province, city, district, " +
            "detail_address, postal_code, is_default, created_time, updated_time) " +
            "VALUES (#{userPhone}, #{receiverName}, #{receiverPhone}, #{province}, #{city}, #{district}, " +
            "#{detailAddress}, #{postalCode}, #{isDefault}, #{createdTime}, #{updatedTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUserAddress(UserAddress userAddress);
    
    // ==================== 购物车相关 ====================
    
    /**
     * 根据用户手机号统计购物车商品数量
     */
    @Select("SELECT COALESCE(SUM(quantity), 0) FROM shopping_cart WHERE user_phone = #{userPhone}")
    Integer getCartItemCountByPhone(@Param("userPhone") String userPhone);
    
    /**
     * 根据用户手机号查询购物车详情
     */
    @Select("SELECT sc.*, p.name as product_name, p.price as product_price, p.image_url as product_image " +
            "FROM shopping_cart sc " +
            "LEFT JOIN products p ON sc.product_id = p.id " +
            "WHERE sc.user_phone = #{userPhone} " +
            "ORDER BY sc.updated_time DESC")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "userPhone", column = "user_phone"),
        @Result(property = "productId", column = "product_id"),
        @Result(property = "quantity", column = "quantity"),
        @Result(property = "createdTime", column = "created_time"),
        @Result(property = "updatedTime", column = "updated_time")
    })
    List<ShoppingCart> getCartItemsByPhone(@Param("userPhone") String userPhone);
    
    // ==================== 订单相关 ====================
    
    /**
     * 根据用户手机号统计订单总数
     */
    @Select("SELECT COUNT(*) FROM user_order WHERE user_phone = #{userPhone}")
    Integer getTotalOrderCountByPhone(@Param("userPhone") String userPhone);
    
    /**
     * 根据用户手机号统计待处理订单数（状态1-3）
     */
    @Select("SELECT COUNT(*) FROM user_order WHERE user_phone = #{userPhone} AND status IN (1, 2, 3)")
    Integer getPendingOrderCountByPhone(@Param("userPhone") String userPhone);
    
    /**
     * 根据用户手机号查询订单列表
     */
    @Select("SELECT * FROM user_order WHERE user_phone = #{userPhone} ORDER BY created_time DESC LIMIT #{limit}")
    List<UserOrder> getRecentOrdersByPhone(@Param("userPhone") String userPhone, @Param("limit") int limit);
    
    // ==================== 好友相关 ====================
    
    /**
     * 根据用户手机号统计好友数量
     */
    @Select("SELECT COUNT(*) FROM user_friends WHERE user_phone = #{userPhone} AND status = 1")
    Integer getFriendCountByPhone(@Param("userPhone") String userPhone);

    /**
     * 获取用户好友列表（包含好友的基本信息）
     */
    @Select("SELECT uf.id, uf.user_phone, uf.friend_phone, uf.friend_name, " +
            "u.username as friend_username, ud.avatar_url as friend_avatar, " +
            "uf.status, uf.created_time, uf.updated_time " +
            "FROM user_friends uf " +
            "LEFT JOIN user u ON uf.friend_phone = u.phone " +
            "LEFT JOIN user_detail ud ON uf.friend_phone = ud.phone " +
            "WHERE uf.user_phone = #{userPhone} AND uf.status = 1 " +
            "ORDER BY uf.created_time DESC")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "userPhone", column = "user_phone"),
        @Result(property = "friendPhone", column = "friend_phone"),
        @Result(property = "friendName", column = "friend_name"),
        @Result(property = "friendUsername", column = "friend_username"),
        @Result(property = "friendAvatar", column = "friend_avatar"),
        @Result(property = "status", column = "status"),
        @Result(property = "createdTime", column = "created_time"),
        @Result(property = "updatedTime", column = "updated_time")
    })
    List<UserFriend> getFriendsByPhone(@Param("userPhone") String userPhone);

}
