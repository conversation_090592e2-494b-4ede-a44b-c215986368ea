package cn.gzsf.javawebspringboot.service.impl;

import cn.gzsf.javawebspringboot.entity.UserAddress;
import cn.gzsf.javawebspringboot.mapper.UserAddressMapper;
import cn.gzsf.javawebspringboot.service.UserAddressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 用户地址服务实现类
 */
@Service
public class UserAddressServiceImpl implements UserAddressService {

    @Autowired
    private UserAddressMapper userAddressMapper;

    @Override
    public List<UserAddress> getAddressByUserPhone(String userPhone) {
        try {
            return userAddressMapper.getAddressByUserPhone(userPhone);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("获取地址列表失败", e);
        }
    }

    @Override
    public boolean addAddress(UserAddress address) {
        try {
            // 设置创建时间
            address.setCreatedTime(System.currentTimeMillis());
            address.setUpdatedTime(System.currentTimeMillis());
            
            // 如果是第一个地址，自动设为默认
            List<UserAddress> existingAddresses = userAddressMapper.getAddressByUserPhone(address.getUserPhone());
            if (existingAddresses.isEmpty()) {
                address.setIsDefault(true);
            } else if (address.getIsDefault() == null) {
                address.setIsDefault(false);
            }
            
            return userAddressMapper.insertAddress(address) > 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean updateAddress(UserAddress address) {
        try {
            address.setUpdatedTime(System.currentTimeMillis());
            return userAddressMapper.updateAddress(address) > 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean deleteAddress(Integer id) {
        try {
            return userAddressMapper.deleteAddress(id) > 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    @Transactional
    public boolean setDefaultAddress(Integer id, String userPhone) {
        try {
            // 先将该用户的所有地址设为非默认
            userAddressMapper.clearDefaultAddress(userPhone);
            
            // 再将指定地址设为默认
            return userAddressMapper.setDefaultAddress(id) > 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
