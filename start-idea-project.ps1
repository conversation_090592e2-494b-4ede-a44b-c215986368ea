# 自动启动IDEA并运行SpringBoot项目
Write-Host "🚀 正在启动IntelliJ IDEA..." -ForegroundColor Green

# 获取当前项目路径
$projectPath = Get-Location
Write-Host "📁 项目路径: $projectPath" -ForegroundColor Yellow

# 启动IDEA并打开项目
Write-Host "🔧 启动IDEA并打开项目..." -ForegroundColor Cyan
Start-Process "idea64" -ArgumentList $projectPath

Write-Host "✅ IDEA已启动！" -ForegroundColor Green
Write-Host "📋 接下来的步骤:" -ForegroundColor Yellow
Write-Host "   1. 等待IDEA完全加载项目" -ForegroundColor White
Write-Host "   2. 找到 JavaWebSpringBootApplication.java 文件" -ForegroundColor White
Write-Host "   3. 点击main方法旁边的绿色运行按钮" -ForegroundColor White
Write-Host "   4. 或者右键 -> Run 'JavaWebSpringBootApplication'" -ForegroundColor White
Write-Host "   5. 应用将在 http://localhost:8082 启动" -ForegroundColor White

Write-Host "🎯 项目修改已完成，包括:" -ForegroundColor Green
Write-Host "   ✅ 息壤臻选区域只显示新品（横向滚动）" -ForegroundColor White
Write-Host "   ✅ 分类导航下方显示分类产品（网格布局）" -ForegroundColor White
Write-Host "   ✅ 清理了所有404错误" -ForegroundColor White
Write-Host "   ✅ 移除了所有死数据引用" -ForegroundColor White

Write-Host "⏳ 等待IDEA启动完成..." -ForegroundColor Magenta
