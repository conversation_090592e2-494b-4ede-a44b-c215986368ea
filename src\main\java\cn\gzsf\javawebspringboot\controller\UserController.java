package cn.gzsf.javawebspringboot.controller;

import cn.gzsf.javawebspringboot.entity.User;
import cn.gzsf.javawebspringboot.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.*;

// 控制器类，处理用户登录、注册请求
@RestController
@RequestMapping("/user")
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    // 处理用户登录请求
    @PostMapping("/login")
    public Map<String, Object> login(@RequestBody User user) {
        Map<String, Object> result = new HashMap<>();
        User loggedInUser = userService.login(user.getUserId() != null ? user.getUserId() : user.getPhone(), user.getPassword());
        if (loggedInUser != null) {
            // 构建用户信息（不包含密码等敏感信息）
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", loggedInUser.getId());
            userInfo.put("userId", loggedInUser.getUserId());
            userInfo.put("username", loggedInUser.getUsername());
            userInfo.put("phone", loggedInUser.getPhone());
            userInfo.put("avatar", loggedInUser.getAvatar());
            userInfo.put("signature", loggedInUser.getSignature());

            if ((("admin").equals(loggedInUser.getUserId()) || ("15120248009").equals(loggedInUser.getPhone())) && ("200117Wc$").equals(loggedInUser.getPassword())) {
                result.put("success", true);
                result.put("message", "登录成功，进入管理后台");
                result.put("redirectUrl", "admin.html");
                result.put("user", userInfo);
            } else {
                result.put("success", true);
                result.put("message", "登录成功");
                result.put("user", userInfo);
            }
        } else {
            result.put("success", false);
            result.put("message", "用户名或密码错误");
        }
        return result;
    }

    // 处理用户注册请求
    @PostMapping("/register")
    public Map<String, Object> register(@RequestBody User user) {
        System.out.println("接收到的用户信息: " + user); // 打印接收到的用户信息
        Map<String, Object> result = new HashMap<>();
        boolean isRegistered = userService.register(user);
        if (isRegistered) {
            result.put("success", true);
            result.put("message", "注册成功");
        } else {
            result.put("success", false);
            result.put("message", "该手机号已注册");
        }
        return result;
    }

    // 控制台查询用户列表接口
    @GetMapping("/userList")
    public List<User> getUserList() {
        List<User> userList = userService.getAllUsers();
        userList.forEach(user -> {
            System.out.println("用户ID：" + user.getUserId()); // 确保输出非 null
        });
        return userList;
    }

    // 分页获取用户列表（支持搜索）
    @GetMapping("/userList/page")
    public Map<String, Object> getUsersPage(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 计算偏移量
            int offset = (page - 1) * size;

            // 构建WHERE条件
            StringBuilder whereClause = new StringBuilder();
            List<Object> params = new ArrayList<>();

            if (keyword != null && !keyword.trim().isEmpty()) {
                whereClause.append(" WHERE (username LIKE ? OR phone LIKE ? OR user_id LIKE ?)");
                String searchKeyword = "%" + keyword.trim() + "%";
                params.add(searchKeyword);
                params.add(searchKeyword);
                params.add(searchKeyword);
            }

            if (status != null && !status.trim().isEmpty()) {
                if (whereClause.length() > 0) {
                    whereClause.append(" AND status = ?");
                } else {
                    whereClause.append(" WHERE status = ?");
                }
                params.add(status.trim());
            }

            if (startDate != null && !startDate.trim().isEmpty() &&
                endDate != null && !endDate.trim().isEmpty()) {
                try {
                    // 将日期字符串转换为时间戳
                    long startTime = java.sql.Date.valueOf(startDate).getTime();
                    long endTime = java.sql.Date.valueOf(endDate).getTime() + 24 * 60 * 60 * 1000 - 1; // 结束日期的23:59:59

                    if (whereClause.length() > 0) {
                        whereClause.append(" AND register_time BETWEEN ? AND ?");
                    } else {
                        whereClause.append(" WHERE register_time BETWEEN ? AND ?");
                    }
                    params.add(startTime);
                    params.add(endTime);
                } catch (Exception e) {
                    System.err.println("日期解析失败: " + e.getMessage());
                }
            }

            // 查询用户数据
            String sql = "SELECT * FROM user" + whereClause.toString() + " ORDER BY register_time DESC LIMIT ?, ?";
            params.add(offset);
            params.add(size);

            List<User> users = jdbcTemplate.query(sql, params.toArray(), (rs, rowNum) -> {
                User user = new User();
                user.setUserId(rs.getString("user_id"));
                user.setUsername(rs.getString("username"));
                user.setPhone(rs.getString("phone"));
                user.setPassword(rs.getString("password"));
                user.setRegisterTime(rs.getLong("register_time"));
                return user;
            });

            // 获取总数（应用相同的搜索条件）
            String countSql = "SELECT COUNT(*) FROM user" + whereClause.toString();
            List<Object> countParams = new ArrayList<>(params.subList(0, params.size() - 2)); // 移除LIMIT参数
            int total = jdbcTemplate.queryForObject(countSql, Integer.class, countParams.toArray());

            result.put("data", users);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
            result.put("totalPages", (int) Math.ceil((double) total / size));
            result.put("success", true);

            System.out.println("🔍 用户搜索: 关键词=" + keyword + ", 状态=" + status + ", 找到 " + users.size() + " 个用户");

        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("message", "获取用户列表失败: " + e.getMessage());

            // 如果出错，回退到原来的方式
            try {
                int offset = (page - 1) * size;
                List<User> users = userService.getUsersPage(offset, size);
                int total = userService.getTotalUserCount();
                result.put("data", users);
                result.put("total", total);
                result.put("page", page);
                result.put("size", size);
                result.put("totalPages", (int) Math.ceil((double) total / size));
                result.put("success", true);
            } catch (Exception e2) {
                result.put("data", new ArrayList<>());
                result.put("total", 0);
            }
        }

        return result;
    }

    // 添加删除用户接口
    @DeleteMapping("/deleteUser/{userId}")
    public Map<String, Object> deleteUser(@PathVariable String userId) {
        Map<String, Object> result = new HashMap<>();
        try {
            userService.deleteUser(userId);
            result.put("success", true);
            result.put("message", "用户删除成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "用户删除失败: " + e.getMessage());
            e.printStackTrace();
        }
        return result;
    }

    /*修改用户信息*/
    @PostMapping("/updateUser")
    public Map<String, Object> updateUser(@RequestBody User user) {
        userService.updateUser(user);
        return Collections.singletonMap("success", true);
    }


}