package cn.gzsf.javawebspringboot.service.impl;

import cn.gzsf.javawebspringboot.entity.ProductShare;
import cn.gzsf.javawebspringboot.mapper.ProductShareMapper;
import cn.gzsf.javawebspringboot.service.ProductShareService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 产品分享服务实现类
 */
@Service
public class ProductShareServiceImpl implements ProductShareService {
    
    @Autowired
    private ProductShareMapper shareMapper;
    
    @Override
    public boolean addShare(String userPhone, Long productId, String shareType) {
        try {
            ProductShare share = new ProductShare();
            share.setUserPhone(userPhone);
            share.setProductId(productId);
            share.setShareType(shareType);
            share.setCreatedTime(System.currentTimeMillis());
            
            return shareMapper.addShare(share) > 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    @Override
    public int getProductShareCount(Long productId) {
        try {
            return shareMapper.getProductShareCount(productId);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }
    
    @Override
    public Map<String, Object> getUserShares(String userPhone, int page, int size) {
        Map<String, Object> result = new HashMap<>();
        try {
            int offset = (page - 1) * size;
            List<ProductShare> shares = shareMapper.getUserShares(userPhone, offset, size);
            int total = shareMapper.getUserShareCount(userPhone);
            
            result.put("success", true);
            result.put("data", shares);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取分享列表失败");
        }
        return result;
    }
    
    @Override
    public List<ProductShare> getPopularShares(int limit) {
        try {
            return shareMapper.getPopularShares(limit);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    
    @Override
    public List<Map<String, Object>> getShareStatsByType() {
        try {
            return shareMapper.getShareStatsByType();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    
    @Override
    public Map<String, Object> getAllShares(int page, int size) {
        Map<String, Object> result = new HashMap<>();
        try {
            int offset = (page - 1) * size;
            List<ProductShare> shares = shareMapper.getAllShares(offset, size);
            int total = shareMapper.getTotalShareCount();
            
            result.put("success", true);
            result.put("data", shares);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取分享列表失败");
        }
        return result;
    }

    @Override
    public boolean deleteShareById(Long id) {
        try {
            return shareMapper.deleteShareById(id) > 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
