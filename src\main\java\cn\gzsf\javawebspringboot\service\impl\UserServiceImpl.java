package cn.gzsf.javawebspringboot.service.impl;

import cn.gzsf.javawebspringboot.dao.UserDao;
import cn.gzsf.javawebspringboot.entity.User;
import cn.gzsf.javawebspringboot.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

// 用户服务实现类，实现UserService接口
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserDao userDao;

    // 用户登录实现方法
    @Override
    public User login(String idOrPhone, String password) {
        return userDao.findUserByIdOrPhoneAndPassword(idOrPhone, password);
    }

    // 用户注册实现方法
    @Override
    public boolean register(User user) {
        // 检查手机号是否已注册
        User existingUser = userDao.findUserByPhone(user.getPhone());
        if (existingUser != null) {
            return false;
        }
        // 插入新用户
        int result = userDao.insertUser(user);
        return result > 0;
    }

    @Override
    public List<User> getAllUsers() {
        return userDao.getAllUsers();
    }

    // 新增删除用户方法实现
    @Override
    public void deleteUser(String userId) {
        userDao.deleteUser(userId);
    }

    //编辑用户信息
    @Override
    public boolean updateUser(User user) {
        try {
            userDao.updateUser(user);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public List<User> getUsersPage(int offset, int size) {
        return userDao.findUsersPage(offset, size);
    }

    @Override
    public int getTotalUserCount() {
        return userDao.getTotalUserCount();
    }

    @Override
    public User findByPhone(String phone) {
        return userDao.findUserByPhone(phone);
    }

    @Override
    public User findByUserId(String userId) {
        return userDao.findUserByUserId(userId);
    }

}