# 检查SpringBoot应用启动状态
Write-Host "🔍 检查SpringBoot应用状态..." -ForegroundColor Green

$maxAttempts = 30
$attempt = 0

while ($attempt -lt $maxAttempts) {
    $attempt++
    Write-Host "⏳ 尝试 $attempt/$maxAttempts - 检查应用状态..." -ForegroundColor Yellow
    
    # 检查端口是否监听
    $portCheck = netstat -an | findstr ":8082.*LISTENING"
    if ($portCheck) {
        Write-Host "✅ 应用已启动！端口8082正在监听" -ForegroundColor Green
        Write-Host "🌐 访问地址: http://localhost:8082/index.html" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "🎉 功能验证:" -ForegroundColor Magenta
        Write-Host "   ✅ 息壤臻选区域只显示新品（横向滚动）" -ForegroundColor White
        Write-Host "   ✅ 分类导航下方显示分类产品（网格布局）" -ForegroundColor White
        Write-Host "   ✅ 清理了所有404错误" -ForegroundColor White
        Write-Host "   ✅ 点击分类可查看对应产品" -ForegroundColor White
        break
    }

    # 尝试HTTP请求
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8082" -TimeoutSec 2 -ErrorAction SilentlyContinue
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ 应用已启动并响应HTTP请求！" -ForegroundColor Green
            Write-Host "🌐 访问地址: http://localhost:8082/index.html" -ForegroundColor Cyan
            break
        }
    }
    catch {
        # 继续等待
    }
    
    Start-Sleep -Seconds 2
}

if ($attempt -eq $maxAttempts) {
    Write-Host "⚠️ 应用启动超时，请检查日志" -ForegroundColor Red
    Write-Host "💡 建议:" -ForegroundColor Yellow
    Write-Host "   1. 检查Java版本兼容性" -ForegroundColor White
    Write-Host "   2. 检查端口8082是否被占用" -ForegroundColor White
    Write-Host "   3. 查看应用启动日志" -ForegroundColor White
}
