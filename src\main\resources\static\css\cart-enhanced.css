/* 增强购物车样式 */

/* 购物车项目样式 */
.cart-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s;
}

.cart-item:hover {
    background-color: #f9f9f9;
}

.cart-item-checkbox {
    margin-right: 12px;
}

.cart-item-checkbox input[type="checkbox"] {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.cart-item-image {
    width: 60px;
    height: 60px;
    margin-right: 12px;
    border-radius: 6px;
    overflow: hidden;
}

.cart-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cart-item-info {
    flex: 1;
    margin-right: 12px;
}

.cart-item-name {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
    line-height: 1.4;
}

.cart-item-price {
    font-size: 16px;
    color: #ff6b6b;
    font-weight: 600;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    margin-right: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.cart-item-controls button {
    width: 32px;
    height: 32px;
    border: none;
    background: #fff;
    cursor: pointer;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.cart-item-controls button:hover:not(:disabled) {
    background-color: #f5f5f5;
}

.cart-item-controls button:disabled {
    color: #ccc;
    cursor: not-allowed;
}

.cart-item-quantity {
    min-width: 40px;
    text-align: center;
    padding: 0 8px;
    font-weight: 500;
    border-left: 1px solid #e0e0e0;
    border-right: 1px solid #e0e0e0;
}

.cart-item-total {
    min-width: 80px;
    text-align: right;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-right: 12px;
}

.cart-item-actions button {
    width: 32px;
    height: 32px;
    border: none;
    background: #ff4757;
    color: white;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.cart-item-actions button:hover {
    background-color: #ff3742;
}

/* 购物车底部操作区 */
.cart-footer {
    padding: 15px;
    border-top: 1px solid #e0e0e0;
    background: #fff;
}

.cart-batch-operations {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    gap: 15px;
}

.select-all {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: #666;
}

.select-all input[type="checkbox"] {
    margin-right: 6px;
    width: 16px;
    height: 16px;
}

.btn-clear-selected {
    padding: 6px 12px;
    border: 1px solid #ff4757;
    background: #fff;
    color: #ff4757;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.btn-clear-selected:hover {
    background: #ff4757;
    color: white;
}

.cart-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.total-info {
    font-size: 14px;
    color: #666;
}

.total-price {
    font-size: 20px;
    font-weight: 700;
    color: #ff6b6b;
    margin-left: 8px;
}

.btn-checkout {
    padding: 12px 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
}

.btn-checkout:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-checkout:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 空购物车样式 */
.empty-cart {
    text-align: center;
    padding: 40px 20px;
    color: #999;
    font-size: 16px;
}

/* 地址管理模态框样式 */
.address-management-modal,
.order-management-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.address-management-modal .modal-content,
.order-management-modal .modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.close-modal {
    width: 32px;
    height: 32px;
    border: none;
    background: #f5f5f5;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.close-modal:hover {
    background: #e0e0e0;
}

.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

/* 地址项目样式 */
.address-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 12px;
    transition: border-color 0.2s;
}

.address-item:hover {
    border-color: #667eea;
}

.address-item.default {
    border-color: #52c41a;
    background: #f6ffed;
}

.address-info {
    flex: 1;
}

.receiver-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
}

.receiver-name {
    font-weight: 600;
    color: #333;
}

.receiver-phone {
    color: #666;
}

.default-tag {
    background: #52c41a;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.address-detail {
    color: #666;
    line-height: 1.4;
}

.address-actions {
    display: flex;
    gap: 8px;
}

.address-actions button {
    padding: 6px 12px;
    border: 1px solid #d9d9d9;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s;
}

.btn-edit:hover {
    border-color: #1890ff;
    color: #1890ff;
}

.btn-delete:hover {
    border-color: #ff4d4f;
    color: #ff4d4f;
}

.btn-default:hover {
    border-color: #52c41a;
    color: #52c41a;
}

.btn-add-address {
    width: 100%;
    padding: 12px;
    border: 2px dashed #d9d9d9;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    transition: all 0.2s;
}

.btn-add-address:hover {
    border-color: #1890ff;
    color: #1890ff;
}

/* 购物车提示框样式 */
.cart-alert {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10001;
    max-width: 300px;
}

.cart-alert-item {
    padding: 12px 16px;
    margin-bottom: 8px;
    border-radius: 6px;
    color: white;
    font-size: 14px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: slideInRight 0.3s ease-out;
}

.cart-alert-item.success {
    background: linear-gradient(135deg, #52c41a, #73d13d);
}

.cart-alert-item.error {
    background: linear-gradient(135deg, #ff4d4f, #ff7875);
}

.cart-alert-item.warning {
    background: linear-gradient(135deg, #faad14, #ffc53d);
}

.cart-alert-item.info {
    background: linear-gradient(135deg, #1890ff, #40a9ff);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 可点击统计项样式 */
.stat-item.clickable {
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 8px;
    padding: 8px;
}

.stat-item.clickable:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.stat-item.clickable:active {
    transform: translateY(0);
}

/* 好友管理模态框样式 */
.friends-management-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.friends-management-modal .modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.friends-stats {
    padding: 15px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
}

.friends-list {
    max-height: 400px;
    overflow-y: auto;
    padding: 15px;
}

.friend-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 10px;
    transition: border-color 0.2s;
}

.friend-item:hover {
    border-color: #667eea;
}

/* 订单确认模态框样式 */
.order-confirmation-modal,
.payment-success-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10001;
}

.order-confirmation-modal .modal-content,
.payment-success-modal .modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 700px;
    max-height: 85vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    background: #f8f9fa;
}

.modal-footer button {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
}

.btn-cancel {
    background: #f5f5f5;
    color: #666;
}

.btn-cancel:hover {
    background: #e8e8e8;
}

.btn-confirm-order {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-confirm-order:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* 地址选择样式 */
.address-selection,
.order-items,
.order-remark {
    margin-bottom: 20px;
}

.address-selection h4,
.order-items h4,
.order-remark h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.address-item {
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.2s;
}

.address-item:hover {
    border-color: #667eea;
}

.address-item.selected {
    border-color: #667eea;
    background: #f0f4ff;
}

.address-item .receiver-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
}

.address-item .receiver-name {
    font-weight: 600;
    color: #333;
}

.address-item .receiver-phone {
    color: #666;
    font-size: 14px;
}

.address-item .default-tag {
    background: #52c41a;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.address-item .address-detail {
    color: #666;
    font-size: 14px;
    line-height: 1.4;
}

/* 订单商品列表样式 */
.order-items-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
}

.order-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
}

.order-item:last-child {
    border-bottom: none;
}

.order-item-image {
    width: 50px;
    height: 50px;
    border-radius: 4px;
    overflow: hidden;
    margin-right: 12px;
}

.order-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.order-item-info {
    flex: 1;
}

.order-item-name {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
}

.order-item-price {
    font-size: 14px;
    color: #666;
}

.order-item-quantity {
    font-size: 14px;
    color: #666;
    margin-left: 12px;
}

/* 费用明细样式 */
.order-summary-detail {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
    color: #666;
}

.summary-row:last-child {
    margin-bottom: 0;
}

.summary-row.total {
    border-top: 1px solid #e0e0e0;
    padding-top: 8px;
    margin-top: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.summary-row.total .total-amount {
    color: #ff6b6b;
    font-size: 18px;
}

/* 清空购物车按钮样式 */
.btn-clear-all {
    transition: all 0.2s;
}

.btn-clear-all:hover {
    background: #ff4757 !important;
    color: white !important;
}

/* 继续购物按钮样式 */
.btn-continue-shopping {
    transition: all 0.2s;
}

.btn-continue-shopping:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(135, 206, 235, 0.4);
}

.friend-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 12px;
}

.friend-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.friend-info {
    flex: 1;
}

.friend-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.friend-status {
    color: #666;
    font-size: 12px;
}

.friend-actions {
    display: flex;
    gap: 8px;
}

.friend-actions button {
    padding: 6px 12px;
    border: 1px solid #d9d9d9;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s;
}

.btn-chat:hover {
    border-color: #1890ff;
    color: #1890ff;
}

.btn-remove:hover {
    border-color: #ff4d4f;
    color: #ff4d4f;
}

.no-friends {
    text-align: center;
    padding: 40px 20px;
    color: #999;
    font-size: 16px;
}

.friends-actions {
    padding: 15px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    gap: 12px;
    justify-content: center;
}

.btn-add-friend,
.btn-find-friends {
    padding: 10px 20px;
    border: 1px solid #667eea;
    background: white;
    color: #667eea;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.btn-add-friend:hover,
.btn-find-friends:hover {
    background: #667eea;
    color: white;
}
