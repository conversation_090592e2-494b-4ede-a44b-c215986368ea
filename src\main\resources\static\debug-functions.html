<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .debug-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .debug-section h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .debug-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .debug-button:hover {
            background: #0056b3;
        }
        .debug-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .success { border-left-color: #28a745; background: #d4edda; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔧 功能调试页面</h1>
    <p>这个页面用于调试和测试各个功能的API接口</p>

    <!-- 1. 时间格式化测试 -->
    <div class="debug-section">
        <h2>⏰ 时间格式化测试</h2>
        <button class="debug-button" onclick="testTimeFormat()">测试时间格式化</button>
        <div id="time-result" class="debug-result"></div>
    </div>

    <!-- 2. 产品分类API测试 -->
    <div class="debug-section">
        <h2>📂 产品分类API测试</h2>
        <button class="debug-button" onclick="testCategoryAPI(0)">测试全部分类 (ID=0)</button>
        <button class="debug-button" onclick="testCategoryAPI(1)">测试分类1</button>
        <button class="debug-button" onclick="testCategoryAPI(2)">测试分类2</button>
        <div id="category-result" class="debug-result"></div>
    </div>

    <!-- 3. 评论API测试 -->
    <div class="debug-section">
        <h2>💬 评论API测试</h2>
        <button class="debug-button" onclick="testCommentsAPI()">测试评论列表</button>
        <button class="debug-button" onclick="testAdminCommentsAPI()">测试管理员评论列表</button>
        <div id="comments-result" class="debug-result"></div>
    </div>

    <!-- 4. 收藏API测试 -->
    <div class="debug-section">
        <h2>❤️ 收藏API测试</h2>
        <button class="debug-button" onclick="testFavoritesAPI()">测试收藏列表</button>
        <div id="favorites-result" class="debug-result"></div>
    </div>

    <!-- 5. 分享API测试 -->
    <div class="debug-section">
        <h2>📤 分享API测试</h2>
        <button class="debug-button" onclick="testSharesAPI()">测试分享列表</button>
        <div id="shares-result" class="debug-result"></div>
    </div>

    <!-- 6. 订单API测试 -->
    <div class="debug-section">
        <h2>📦 订单API测试</h2>
        <button class="debug-button" onclick="testOrdersAPI()">测试订单列表</button>
        <button class="debug-button" onclick="testOrderDetailAPI()">测试订单详情</button>
        <div id="orders-result" class="debug-result"></div>
    </div>

    <!-- 7. 头像路径测试 -->
    <div class="debug-section">
        <h2>👤 头像路径测试</h2>
        <button class="debug-button" onclick="testAvatarPaths()">测试头像路径处理</button>
        <div id="avatar-result" class="debug-result"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script>
        // 时间格式化测试
        function testTimeFormat() {
            const result = document.getElementById('time-result');
            const testTimestamps = [
                Date.now(), // 毫秒时间戳
                Math.floor(Date.now() / 1000), // 秒时间戳
                '1703145600000', // 字符串毫秒时间戳
                '1703145600', // 字符串秒时间戳
                null,
                undefined,
                'invalid'
            ];

            let html = '<h4>时间格式化测试结果：</h4>';
            testTimestamps.forEach((timestamp, index) => {
                try {
                    const formatted = formatTime(timestamp);
                    html += `<div>测试${index + 1}: ${timestamp} → ${formatted}</div>`;
                } catch (error) {
                    html += `<div style="color: red;">测试${index + 1}: ${timestamp} → 错误: ${error.message}</div>`;
                }
            });

            result.innerHTML = html;
        }

        // 时间格式化函数（复制自index_new.js）
        function formatTime(timestamp) {
            if (!timestamp) return '未知时间';
            
            let time = timestamp;
            if (typeof timestamp === 'string') {
                time = parseInt(timestamp);
            }
            
            if (time.toString().length === 10) {
                time = time * 1000;
            }
            
            const date = new Date(time);
            
            if (isNaN(date.getTime())) {
                return '时间格式错误';
            }
            
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            
            return `${year}-${month}-${day} ${hours}:${minutes}`;
        }

        // 产品分类API测试
        function testCategoryAPI(categoryId) {
            const result = document.getElementById('category-result');
            result.innerHTML = `<div>正在测试分类 ${categoryId}...</div>`;

            axios.get(`/products/category/${categoryId}`)
                .then(response => {
                    result.className = 'debug-result success';
                    result.innerHTML = `
                        <h4>✅ 分类 ${categoryId} API测试成功</h4>
                        <div><strong>成功:</strong> ${response.data.success}</div>
                        <div><strong>产品数量:</strong> ${response.data.data ? response.data.data.length : 0}</div>
                        <div><strong>消息:</strong> ${response.data.message || '无'}</div>
                        <details>
                            <summary>完整响应数据</summary>
                            <pre>${JSON.stringify(response.data, null, 2)}</pre>
                        </details>
                    `;
                })
                .catch(error => {
                    result.className = 'debug-result error';
                    result.innerHTML = `
                        <h4>❌ 分类 ${categoryId} API测试失败</h4>
                        <div><strong>错误:</strong> ${error.message}</div>
                        <div><strong>状态码:</strong> ${error.response?.status || '无'}</div>
                        <details>
                            <summary>错误详情</summary>
                            <pre>${JSON.stringify(error.response?.data || error, null, 2)}</pre>
                        </details>
                    `;
                });
        }

        // 评论API测试
        function testCommentsAPI() {
            const result = document.getElementById('comments-result');
            result.innerHTML = '<div>正在测试评论API...</div>';

            axios.get('/api/comments/product/1?page=1&size=5')
                .then(response => {
                    result.className = 'debug-result success';
                    result.innerHTML = `
                        <h4>✅ 评论API测试成功</h4>
                        <div><strong>成功:</strong> ${response.data.success}</div>
                        <div><strong>评论数量:</strong> ${response.data.data ? response.data.data.length : 0}</div>
                        <details>
                            <summary>评论数据示例</summary>
                            <pre>${JSON.stringify(response.data.data?.slice(0, 2) || [], null, 2)}</pre>
                        </details>
                    `;
                })
                .catch(error => {
                    result.className = 'debug-result error';
                    result.innerHTML = `
                        <h4>❌ 评论API测试失败</h4>
                        <div><strong>错误:</strong> ${error.message}</div>
                        <pre>${JSON.stringify(error.response?.data || error, null, 2)}</pre>
                    `;
                });
        }

        // 管理员评论API测试
        function testAdminCommentsAPI() {
            const result = document.getElementById('comments-result');
            result.innerHTML = '<div>正在测试管理员评论API...</div>';

            axios.get('/api/comments/admin/all?page=1&size=5')
                .then(response => {
                    result.className = 'debug-result success';
                    result.innerHTML = `
                        <h4>✅ 管理员评论API测试成功</h4>
                        <div><strong>成功:</strong> ${response.data.success}</div>
                        <div><strong>评论数量:</strong> ${response.data.data ? response.data.data.length : 0}</div>
                        <div><strong>总数:</strong> ${response.data.total || 0}</div>
                        <details>
                            <summary>评论数据示例（检查用户信息）</summary>
                            <pre>${JSON.stringify(response.data.data?.slice(0, 2) || [], null, 2)}</pre>
                        </details>
                    `;
                })
                .catch(error => {
                    result.className = 'debug-result error';
                    result.innerHTML = `
                        <h4>❌ 管理员评论API测试失败</h4>
                        <div><strong>错误:</strong> ${error.message}</div>
                        <pre>${JSON.stringify(error.response?.data || error, null, 2)}</pre>
                    `;
                });
        }

        // 收藏API测试
        function testFavoritesAPI() {
            const result = document.getElementById('favorites-result');
            result.innerHTML = '<div>正在测试收藏API...</div>';

            axios.get('/api/favorites/admin/all?page=1&size=5')
                .then(response => {
                    result.className = 'debug-result success';
                    result.innerHTML = `
                        <h4>✅ 收藏API测试成功</h4>
                        <div><strong>成功:</strong> ${response.data.success}</div>
                        <div><strong>收藏数量:</strong> ${response.data.data ? response.data.data.length : 0}</div>
                        <div><strong>总数:</strong> ${response.data.total || 0}</div>
                        <details>
                            <summary>收藏数据示例（检查用户信息）</summary>
                            <pre>${JSON.stringify(response.data.data?.slice(0, 2) || [], null, 2)}</pre>
                        </details>
                    `;
                })
                .catch(error => {
                    result.className = 'debug-result error';
                    result.innerHTML = `
                        <h4>❌ 收藏API测试失败</h4>
                        <div><strong>错误:</strong> ${error.message}</div>
                        <pre>${JSON.stringify(error.response?.data || error, null, 2)}</pre>
                    `;
                });
        }

        // 分享API测试
        function testSharesAPI() {
            const result = document.getElementById('shares-result');
            result.innerHTML = '<div>正在测试分享API...</div>';

            axios.get('/api/shares/admin/all?page=1&size=5')
                .then(response => {
                    result.className = 'debug-result success';
                    result.innerHTML = `
                        <h4>✅ 分享API测试成功</h4>
                        <div><strong>成功:</strong> ${response.data.success}</div>
                        <div><strong>分享数量:</strong> ${response.data.data ? response.data.data.length : 0}</div>
                        <div><strong>总数:</strong> ${response.data.total || 0}</div>
                        <details>
                            <summary>分享数据示例（检查用户信息）</summary>
                            <pre>${JSON.stringify(response.data.data?.slice(0, 2) || [], null, 2)}</pre>
                        </details>
                    `;
                })
                .catch(error => {
                    result.className = 'debug-result error';
                    result.innerHTML = `
                        <h4>❌ 分享API测试失败</h4>
                        <div><strong>错误:</strong> ${error.message}</div>
                        <pre>${JSON.stringify(error.response?.data || error, null, 2)}</pre>
                    `;
                });
        }

        // 订单API测试
        function testOrdersAPI() {
            const result = document.getElementById('orders-result');
            result.innerHTML = '<div>正在测试订单API...</div>';

            axios.get('/api/order/list/13220248009')
                .then(response => {
                    result.className = 'debug-result success';
                    result.innerHTML = `
                        <h4>✅ 订单API测试成功</h4>
                        <div><strong>成功:</strong> ${response.data.success}</div>
                        <div><strong>订单数量:</strong> ${response.data.data ? response.data.data.length : 0}</div>
                        <details>
                            <summary>订单数据示例</summary>
                            <pre>${JSON.stringify(response.data.data?.slice(0, 2) || [], null, 2)}</pre>
                        </details>
                    `;
                })
                .catch(error => {
                    result.className = 'debug-result error';
                    result.innerHTML = `
                        <h4>❌ 订单API测试失败</h4>
                        <div><strong>错误:</strong> ${error.message}</div>
                        <pre>${JSON.stringify(error.response?.data || error, null, 2)}</pre>
                    `;
                });
        }

        // 订单详情API测试
        function testOrderDetailAPI() {
            const result = document.getElementById('orders-result');
            result.innerHTML = '<div>正在测试订单详情API...</div>';

            axios.get('/api/order/ORD20241201001')
                .then(response => {
                    result.className = 'debug-result success';
                    result.innerHTML = `
                        <h4>✅ 订单详情API测试成功</h4>
                        <div><strong>成功:</strong> ${response.data.success}</div>
                        <div><strong>商品数量:</strong> ${response.data.data?.items ? response.data.data.items.length : 0}</div>
                        <details>
                            <summary>订单详情数据（检查商品信息）</summary>
                            <pre>${JSON.stringify(response.data.data || {}, null, 2)}</pre>
                        </details>
                    `;
                })
                .catch(error => {
                    result.className = 'debug-result error';
                    result.innerHTML = `
                        <h4>❌ 订单详情API测试失败</h4>
                        <div><strong>错误:</strong> ${error.message}</div>
                        <pre>${JSON.stringify(error.response?.data || error, null, 2)}</pre>
                    `;
                });
        }

        // 头像路径测试
        function testAvatarPaths() {
            const result = document.getElementById('avatar-result');
            const testPaths = [
                'avatar1.jpg',
                'avatar_123456789.jpg',
                '/images/avatar/avatar1.jpg',
                '/images/avatars/avatar_123456789.jpg',
                'http://example.com/avatar.jpg',
                'data:image/jpeg;base64,/9j/4AAQ...',
                null,
                undefined
            ];

            let html = '<h4>头像路径处理测试结果：</h4>';
            testPaths.forEach((path, index) => {
                const processed = processAvatarPath(path);
                html += `<div>测试${index + 1}: "${path}" → "${processed}"</div>`;
            });

            result.innerHTML = html;
        }

        // 头像路径处理函数（复制自index_new.js）
        function processAvatarPath(avatarSrc) {
            if (!avatarSrc) return '/images/default-avatar.png';
            
            let displaySrc = avatarSrc;

            if (displaySrc.startsWith('images/')) {
                // 已经是完整路径，直接使用
            } else if (!displaySrc.startsWith('http') && !displaySrc.startsWith('/') && !displaySrc.startsWith('data:')) {
                if (displaySrc.startsWith('avatar_')) {
                    displaySrc = `images/avatars/${displaySrc}`;
                } else {
                    displaySrc = `images/avatar/${displaySrc}`;
                }
            }

            return displaySrc;
        }
    </script>
</body>
</html>
