


/*//b.css*/
/* 基础重置与主题色 */
:root {
    --sky-blue: #87CEEB;
    --deep-sky: #00BFFF;
    --cloud-white: #F0FBFF;
    --accent-teal: #48D1CC;
    --text-dark: #2C3E50;/*效果实现不了换种方式*/
    /*font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #4c8174;*/ /* 浅青色背景 */
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
    background: linear-gradient(180deg, var(--cloud-white) 60%, rgba(135,206,235,0.1));
    color: var(--text-dark);
}






/* 动态云朵装饰 */
.deco-cloud {
    position: fixed;
    width: 180px; /* 增大尺寸 */
    height: 80px;
    background: rgba(255,255,255,0.95);
    border-radius: 40px;
    filter: blur(15px); /* 增强模糊效果 */
    z-index: 999; /* 提升层级 */
    animation: float 8s infinite;
    opacity: 0.9; /* 新增透明度 */
    box-shadow: 0 10px 30px rgba(135,206,235,0.2); /* 添加阴影 */
}

/* 调整云朵位置 */
.cloud-left {
    left: 5%;
    top: 20%;
    animation-delay: 0.5s; /* 错开动画时间 */
}

.cloud-right {
    right: 5%;
    top: 30%;
    animation-duration: 9s; /* 不同动画速度 */
}

/* 新增云朵动态细节 */
@keyframes float {
    0%, 100% {
        transform: translateY(0) scale(1);
    }
    50% {
        transform: translateY(-25px) scale(1.05);
        filter: blur(18px); /* 动态模糊变化 */
    }
}

/* 添加移动端适配 这个有很多条，不够为了能够更好区分，我分别在不同位置设置了*/
@media (max-width: 768px) {
    .deco-cloud {
        width: 120px;
        height: 50px;
        filter: blur(10px);
    }
}





/*导航栏 头部 */
.sky-header {
    height: 80px; /* 新增：固定导航栏高度 */
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 5%;
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(5px);
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 15px rgba(135,206,235,0.1);
}

/* 息壤集品牌标识 */
.brand-logo {
    display: flex;
    align-items: center;
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.3s ease;
    padding: 8px 16px;
    border-radius: 20px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(232, 245, 233, 0.8));
    border: 2px solid transparent;
    background-clip: padding-box;
    position: relative;
}

.logo-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 20px;
    padding: 2px;
    background: linear-gradient(135deg, #4CAF50, #81C784, #A5D6A7);
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: exclude;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    z-index: -1;
}

.logo-container:hover {
    transform: scale(1.05) translateY(-2px);
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.2);
}

.logo-icon {
    font-size: 2.5rem;
    filter: drop-shadow(0 2px 4px rgba(139, 115, 85, 0.3));
    animation: gentle-sway 3s ease-in-out infinite;
}

@keyframes gentle-sway {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(3deg); }
}

.logo-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.brand-name {
    font-size: 1.8rem;
    font-weight: 700;
    background: linear-gradient(135deg, #4CAF50, #66BB6A, #81C784);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    letter-spacing: 2px;
    text-shadow: 0 2px 4px rgba(76, 175, 80, 0.2);
    font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
}

.brand-slogan {
    font-size: 0.85rem;
    background: linear-gradient(135deg, #66BB6A, #81C784);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    letter-spacing: 1px;
    font-weight: 400;
    opacity: 0.9;
}

/* 导航栏 */
.nav-menu ul {
    height: 100%; /* 使导航项高度填满导航栏 */
    display: flex; /* 启用flex布局 */
    list-style-type: none; /* 去除列表项符号 */
    gap: 2rem; /* 设置导航项间距 */
    align-items: center; /* 垂直居中 确保图标和文字居中 */
    margin: 0;
    padding: 0;
}

.nav-menu li {
    position: relative; /* 为子元素定位提供基准 */
    padding: 0.5rem 0;
}

.nav-menu a {
    display: flex;
    flex-direction: column; /* 垂直排列图标和文字 */
    align-items: center;
    gap: 0.3rem; /* 图标与文字间距 */
    padding: 0.8rem 1.2rem;
    text-decoration: none;
    color: var(--text-dark);
    border-radius: 15px;
    transition: all 0.3s;
}

.nav-menu a:hover {
    background: var(--sky-blue);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(135,206,235,0.3);
}

#home, #products {
    padding-top: 60px; /* 导航栏高度 */
    box-sizing: border-box; /* 确保内边距不影响内容区域 */
}

section[id] {
    scroll-margin-top: 100px;
}

/* 通用定位规则 */
.product-card {
    scroll-margin-top: 120px; /* 大于导航栏高度 */
}

/* 确保动态加载的产品也应用相同规则 */
.dynamic-products .product-card {
    scroll-margin-top: inherit;
}

/* 搜索容器样式，融入导航栏 */
.search-container {
    flex-grow: 1; /* 占据剩余空间，保持导航栏布局 */
    max-width: 400px; /* 限制最大宽度 */
}

.search-box {
    position: relative; /* 作为建议列表的定位基准 */
    display: flex;
    align-items: center;
    background: var(--cloud-white); /* 云朵白背景 */
    border-radius: 25px; /* 圆角设计 */
    padding: 0.5rem 1rem;
    box-shadow: 0 2px 5px rgba(135, 206, 235, 0.1); /* 轻量阴影 */
}

.search-input {
    flex-grow: 1;
    padding: 0.8rem;
    border: 2px solid transparent;
    border-radius: 25px;
    font-size: 1rem;
    outline: none;

    /* 过渡效果 */
    transition: border-color 0.3s, box-shadow 0.3s;
}

.search-input:focus {
    border-color: var(--deep-sky); /* 聚焦时天蓝色边框 */
    box-shadow: 0 0 10px rgba(0, 191, 255, 0.2);
}

.search-btn {
    background: transparent;
    border: none;
    padding: 0.8rem;
    cursor: pointer;

    /* 图标颜色 */
    color: var(--text-dark);
    transition: color 0.3s;
}

.search-btn:hover {
    color: var(--deep-sky); /* 悬停时天蓝色 */
}

/* 搜索建议样式 */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(135, 206, 235, 0.2);
    padding: 0.5rem 0;
    max-height: 200px; /* 最大高度，超出滚动 */
    overflow-y: auto;
    display: none; /* 初始隐藏 */
}

/* 建议项样式 */
.suggestion-item {
    padding: 0.8rem 1.5rem;
    cursor: pointer;
    text-align: left;
    white-space: nowrap; /* 防止换行 */
    overflow: hidden;
    text-overflow: ellipsis; /* 省略号处理 */

    /* 过渡效果 */
    transition: background-color 0.3s;
}

.suggestion-item:hover {
    background-color: var(--cloud-white); /* 悬停时浅云背景 */
}

/* 无结果提示 */
.no-results {
    padding: 0.8rem 1.5rem;
    color: #666;
    font-size: 0.9rem;
    text-align: center;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .search-container {
        max-width: 280px;
    }

    .search-input {
        font-size: 0.9rem;
    }
}




/* 购物车和用户头像的特殊处理 */
.cart-counter, .user-avatar {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 统一图标尺寸 */
.nav-menu .fas {
    font-size: 1.2rem;
}


/* 购物车按钮样式 */
.btn-cart {
    background: var(--accent-teal);
    color: white;
    padding: 8px 20px;
    border-radius: 20px;
    border: none;
    margin: 10px 0;
    cursor: pointer;
    transition: transform 0.3s, box-shadow 0.3s;
}

.btn-cart:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 10px rgba(72, 209, 204, 0.3);
}

/* 导航栏购物车图标 */
.cart-counter {
    position: relative;
    display: inline-block;
    margin-left: 15px;
}

.cart-counter .fa-shopping-cart {
    font-size: 1.2em;
}


/* 购物车数量标签定位 */
.cart-count {
    position: absolute;
    top: -5px;
    right: -8px;
    background: var(--deep-sky);
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    font-size: 0.7em;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 购物车模态框 */
.cart-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: none;
    z-index: 99999;
}

.cart-content {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    width: 90%;
    max-width: 500px;
    border-radius: 10px;
    padding: 20px;
    max-height: 80vh;
    overflow-y: auto;
}

/* 导航栏原有的.cart-count样式（保持不变） */
.cart-counter .cart-count {
    position: absolute;
    top: -10px;
    right: -15px;
    /* 其他原有样式 */
}

/* 模态框内的数量标志样式 */
.modal-cart-count {
    /* 基础样式 */
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-dark);

    /* 布局调整 */
    margin-left: 2px;/*设置模态框内的数量标志位置*/

    vertical-align: baseline;
    background: rgba(135, 206, 235, 0.1); /* 可选：添加背景色区分 */
    border-radius: 20px; /* 圆角设计 */
}


/* 购物车参加成功与否的提示样式 */
.cart-alert {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 999999;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.cart-alert-item {
    padding: 15px 25px;
    border-radius: 8px;
    font-size: 14px;
    animation: alertSlide 0.3s ease-out;
    backdrop-filter: blur(5px);
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 10px;
}

.cart-alert-item.success {
    background: rgba(103, 194, 58, 0.9);
    color: #fff;
    border: 1px solid #67c23a;
}

.cart-alert-item.error {
    background: rgba(245, 108, 108, 0.9);
    color: #fff;
    border: 1px solid #f56c6c;
}

.cart-alert-item.warning {
    background: rgba(230, 162, 60, 0.9);
    color: #fff;
    border: 1px solid #e6a23c;
}

@keyframes alertSlide {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}


/* 购物车项样式 */
.cart-items li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
}

.cart-item-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.cart-item-amount {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
    color: #666;
}

.cart-item-amount button {
    width: 25px;
    height: 25px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background: #f8f8f8;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 删除按钮样式 */
.cart-item-remove {
    background: none;
    border: none;
    color: #ff0000;
    font-size: 1.2rem;
    cursor: pointer;
    transition: color 0.3s ease;
}

.cart-item-remove:hover {
    color: #cc0000;
}

/* 商品删除动画 */
@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }

    to {
        opacity: 0;
        transform: translateX(-20px);
    }
}

.fade-out {
    animation: fadeOut 0.3s ease forwards;
}

/* 结算按钮样式 */
.btn-checkout {
    width: 100%;
    padding: 10px;
    background: var(--deep-sky);
    color: white;
    border: none;
    border-radius: 25px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: transform 0.3s;
}

.btn-checkout:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 10px rgba(0, 191, 255, 0.3);
}

.close-cart {
    position: absolute;
    right: 15px;
    top: 15px;
    font-size: 1.5em;
    background: none;
    border: none;
    cursor: pointer;
}





/* 用户中心图标 */
.user-center-li {
    margin-left: 20px;
}

/* 用户头像尺寸 */
.user-avatar {
    position: relative;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s;
}

.avatar-icon {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: none; /* 移除过渡效果 */
}

.user-avatar:hover {
    /* 移除放大效果，只保留轻微的透明度变化 */
    opacity: 0.8;
    transform: none !important; /* 强制禁用任何transform效果 */
}

/* 强制禁用所有头像相关元素的transform效果 */
.user-avatar, .user-avatar *,
.avatar-icon, .avatar-icon *,
.profile-avatar, .profile-avatar * {
    transform: none !important;
    transition: opacity 0.3s ease !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .nav-menu ul {
        gap: 1rem;
    }

    .nav-menu a {
        padding: 0.5rem;
    }

    .nav-menu span {
        font-size: 0.8rem;
    }
}

/* 用户中心模态框 */
.user-center-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.6);
    z-index: 10000;
    backdrop-filter: blur(5px);
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
}

.user-center-modal.show {
    display: flex;
}

.user-center-content {
    position: relative;
    background: linear-gradient(135deg, #ffffff 0%, #f8fffe 100%);
    width: 100%;
    max-width: 480px;
    max-height: calc(100vh - 40px);
    margin: 0;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
    animation: modalSlide 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    overflow: hidden;
    border: 1px solid rgba(255,255,255,0.2);
}

@keyframes modalSlide {
    from {
        transform: translateY(-30px) scale(0.9);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

/* 关闭按钮 */
.close-user-center {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: rgba(255,255,255,0.8);
    cursor: pointer;
    z-index: 10001;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-user-center:hover {
    background: rgba(255,255,255,0.2);
    color: white;
    transform: scale(1.1);
}

/* 用户中心头部 */
.user-center-header {
    background: linear-gradient(135deg, var(--sky-blue), var(--deep-sky));
    color: white;
    padding: 25px 30px 20px;
    text-align: center;
    position: relative;
}

.user-center-header h2 {
    margin: 0 0 5px 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.header-subtitle {
    margin: 0;
    opacity: 0.9;
    font-size: 0.9rem;
}

/* 可滚动内容区域 */
.user-center-scrollable {
    max-height: calc(100vh - 160px);
    overflow-y: auto;
    padding: 0;
    scrollbar-width: thin;
    scrollbar-color: var(--sky-blue) transparent;
}

.user-center-scrollable::-webkit-scrollbar {
    width: 6px;
}

.user-center-scrollable::-webkit-scrollbar-track {
    background: transparent;
}

.user-center-scrollable::-webkit-scrollbar-thumb {
    background: var(--sky-blue);
    border-radius: 3px;
}

/* 用户资料区 */
.user-profile-section {
    padding: 25px 30px;
    background: white;
}

.user-profile {
    display: flex;
    align-items: flex-start;
    margin-bottom: 25px;
}

/* 头像容器 */
.avatar-container {
    position: relative;
    margin-right: 20px;
}

.profile-avatar {
    width: 90px;
    height: 90px;
    border-radius: 50%;
    border: 4px solid var(--sky-blue);
    object-fit: cover;
    transition: all 0.3s ease;
}

.avatar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 90px;
    height: 90px;
    border-radius: 50%;
    background: rgba(0,0,0,0.6);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    cursor: pointer;
    color: white;
    font-size: 0.8rem;
}

.avatar-overlay:hover {
    opacity: 1;
}

.avatar-overlay i {
    font-size: 1.2rem;
    margin-bottom: 5px;
}

.profile-info {
    flex: 1;
    min-width: 0;
}

.username {
    color: var(--text-dark);
    margin: 0 0 8px 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.account-id {
    color: #666;
    font-size: 0.9rem;
    margin: 0 0 12px 0;
}

/* 个性签名 */
.user-signature {
    background: #f8f9fa;
    padding: 12px 15px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    margin-bottom: 15px;
}

.user-signature:hover {
    background: #e3f2fd;
    border-color: var(--sky-blue);
}

.signature-edit-icon {
    position: absolute;
    top: 8px;
    right: 10px;
    color: #999;
    font-size: 0.8rem;
}

.signature-text {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
    display: block;
    padding-right: 25px;
}

/* 用户等级 */
.user-level {
    margin-top: 10px;
}

.level-badge {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #333;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 8px;
}

.level-progress {
    background: #e0e0e0;
    height: 6px;
    border-radius: 3px;
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, var(--sky-blue), var(--deep-sky));
    height: 100%;
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* 用户统计信息 */
.user-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    margin-top: 20px;
}

.stat-item {
    text-align: center;
    padding: 15px 10px;
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border-radius: 12px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    cursor: pointer;
}

.stat-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border-color: var(--sky-blue);
}

.stat-icon {
    font-size: 1.5rem;
    margin-bottom: 8px;
}

.stat-number {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--deep-sky);
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.8rem;
    color: #666;
    font-weight: 500;
}

/* 快捷功能区 */
.quick-actions {
    padding: 25px 30px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.section-title {
    margin: 0 0 20px 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-dark);
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 15px;
    background: white;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    color: var(--text-dark);
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.action-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    background: linear-gradient(135deg, var(--sky-blue), var(--deep-sky));
    color: white;
}

.action-item i {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: var(--deep-sky);
    transition: color 0.3s ease;
}

.action-item:hover i {
    color: white;
}

.action-item span {
    font-size: 0.9rem;
    font-weight: 500;
}

/* 详细信息区域 */
.user-details-section {
    padding: 25px 30px;
    background: white;
}

.detail-card {
    margin-bottom: 25px;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    overflow: hidden;
    background: white;
}

.detail-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border-bottom: 1px solid #e9ecef;
}

.detail-header i {
    color: var(--deep-sky);
    margin-right: 10px;
}

.detail-header span {
    font-weight: 600;
    color: var(--text-dark);
    flex: 1;
}

.detail-action {
    background: var(--sky-blue);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.detail-action:hover {
    background: var(--deep-sky);
    transform: scale(1.05);
}

/* 无数据状态 */
.no-address, .no-orders {
    text-align: center;
    padding: 30px 20px;
    color: #666;
}

.no-address i, .no-orders i {
    font-size: 2.5rem;
    color: #ddd;
    margin-bottom: 15px;
    display: block;
}

.no-address p, .no-orders p {
    margin: 0 0 15px 0;
    font-size: 0.9rem;
}

.add-address-btn, .shop-now-btn {
    background: linear-gradient(135deg, var(--sky-blue), var(--deep-sky));
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.add-address-btn:hover, .shop-now-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* 账户安全 */
.security-info {
    padding: 20px;
}

.security-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
}

.security-item:last-child {
    border-bottom: none;
}

.security-label {
    font-weight: 500;
    color: var(--text-dark);
}

.security-status {
    color: #666;
    font-size: 0.9rem;
}

.security-action {
    background: transparent;
    color: var(--deep-sky);
    border: 1px solid var(--sky-blue);
    padding: 5px 15px;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
}

.security-action:hover {
    background: var(--sky-blue);
    color: white;
}

/* 功能菜单 */
.user-menu {
    padding: 25px 30px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.menu-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 18px 20px;
    margin-bottom: 10px;
    border-radius: 12px;
    color: var(--text-dark);
    text-decoration: none;
    transition: all 0.3s ease;
    background: white;
    border: 1px solid #e9ecef;
    cursor: pointer;
}

.menu-item:hover {
    background: linear-gradient(135deg, var(--sky-blue), var(--deep-sky));
    color: white;
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.menu-item i:first-child {
    width: 25px;
    margin-right: 15px;
    color: var(--deep-sky);
    transition: color 0.3s ease;
}

.menu-item:hover i {
    color: white;
}

.menu-item span {
    flex: 1;
    font-weight: 500;
}

.menu-item i:last-child {
    color: #ccc;
    font-size: 0.8rem;
    transition: color 0.3s ease;
}

.menu-item:hover i:last-child {
    color: white;
}

/* 新增样式：全宽菜单项 */
.menu-section {
    margin-bottom: 25px;
}

.menu-section-title {
    font-size: 14px;
    font-weight: 600;
    color: #666;
    margin: 0 0 12px 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.menu-row {
    width: 100%;
    margin-bottom: 8px;
}

.menu-item.full-width {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    margin-bottom: 0;
    border-radius: 12px;
    margin: 0 10px 8px 10px;
    width: calc(100% - 20px);
    transition: all 0.3s ease;
}

.menu-item.full-width:hover {
    background: linear-gradient(135deg, #f0f8ff 0%, #e3f2fd 100%);
    transform: translateX(5px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.close-user-center {
    position: absolute;
    top: 20px;
    right: 25px;
    background: rgba(255,255,255,0.2);
    border: none;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    font-size: 18px;
    cursor: pointer;
    color: white;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.close-user-center:hover {
    background: rgba(255,255,255,0.3);
    transform: scale(1.1);
}




/* 确认对话框容器 */
.confirm-modal {
    display: none; /* 默认隐藏 */
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5); /* 半透明黑色遮罩 */
    z-index: 999999; /* 确保在最顶层 */
}

/* 对话框内容区域 */
.confirm-content {
    position: relative;
    background: white;
    width: 90%;
    max-width: 400px; /* 最大宽度限制 */
    margin: 100px auto; /* 垂直居中 */
    padding: 25px;
    border-radius: 10px;
    text-align: center;
    animation: modalSlide 0.3s ease-out; /* 入场动画 */
}

/* 按钮容器 */
.confirm-buttons {
    margin-top: 25px;
    display: flex;
    gap: 15px; /* 按钮间距 */
    justify-content: center; /* 水平居中 */
}

/* 通用按钮样式 */
.confirm-buttons button {
    padding: 8px 25px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s; /* 过渡动画 */
}

/* 确认按钮样式 */
.confirm-ok {
    background: var(--deep-sky); /* 使用主题色 */
    color: white;
}

/* 确认按钮悬停效果 */
.confirm-ok:hover {
    background: #009acd; /* 加深颜色 */
    transform: translateY(-2px); /* 轻微上移 */
}

/* 取消按钮样式 */
.confirm-cancel {
    background: #f0f0f0;
    color: #666;
}

/* 取消按钮悬停效果 */
.confirm-cancel:hover {
    background: #e0e0e0;
}

/* 对话框入场动画 */
@keyframes modalSlide {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}



/* 轮播图外层容器 */
.carousel-section {
    padding: 0;
    background: none;
}

.carousel-wrapper {
    width: 100%;
    margin: 0;
    padding: 0;
}

/* 轮播图 */
.sky-carousel {
    height: 70vh;
    position: relative;
    overflow: hidden;
    border-radius: 0;
    box-shadow: none;
    background: none;
}

.carousel-item {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1s ease-in-out;
    background-size: cover;
    background-position: center;
    border-radius: 0;
}

.carousel-item.active {
    opacity: 1;
}

.carousel-caption {
    position: absolute;
    bottom: 15%;
    left: 8%;
    background: rgba(255,255,255,0.95);
    padding: 2rem 2.5rem;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    backdrop-filter: blur(10px);    /* 毛玻璃效果 */
    border: 1px solid rgba(255,255,255,0.2);
    max-width: 400px;               /* 限制标题框最大宽度 */
}

.carousel-caption h2 {
    color: var(--deep-sky);
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.8rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.carousel-caption p {
    color: #555;
    font-size: 1.1rem;
    line-height: 1.6;
    margin: 0;
}

/* 轮播图指示点 */
.carousel-dots {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 12px;
    z-index: 10;
}

.carousel-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255,255,255,0.5);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid rgba(255,255,255,0.8);
}

.carousel-dot.active {
    background: var(--sky-blue);
    border-color: white;
    transform: scale(1.2);
}

.carousel-dot:hover {
    background: rgba(255,255,255,0.8);
    transform: scale(1.1);
}

/* 轮播图响应式设计 */
@media (max-width: 1200px) {
    .carousel-wrapper {
        padding: 0;
    }

    .sky-carousel {
        height: 60vh;
    }
}

@media (max-width: 768px) {
    .carousel-section {
        padding: 0;
    }

    .carousel-wrapper {
        padding: 0;
    }

    .sky-carousel {
        height: 50vh;
        border-radius: 0;
    }

    .carousel-caption {
        bottom: 10%;
        left: 5%;
        right: 5%;
        max-width: none;
        padding: 1.5rem;
    }

    .carousel-caption h2 {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .carousel-caption p {
        font-size: 1rem;
    }

    .carousel-dots {
        bottom: 15px;
    }

    .carousel-dot {
        width: 10px;
        height: 10px;
    }
}

@media (max-width: 480px) {
    .carousel-section {
        padding: 0;
    }

    .carousel-wrapper {
        padding: 0;
    }

    .sky-carousel {
        height: 40vh;
        border-radius: 0;
    }

    .carousel-caption {
        padding: 1rem;
    }

    .carousel-caption h2 {
        font-size: 1.2rem;
    }

    .carousel-caption p {
        font-size: 0.9rem;
    }
}



/* 产品网格容器 */
.product-grid {
    display: flex;           /* 改用flex布局 */
    flex-wrap: nowrap;       /* 禁止换行 */
    overflow-x: auto;       /* 允许横向滚动 */
    padding: 2rem 5%;
    gap: 2rem;              /* 项目间距 */
    scrollbar-width: thin;  /* 美化滚动条 */
    scrollbar-color: var(--sky-blue) var(--cloud-white);
}

/* Webkit浏览器滚动条美化 */
.product-grid::-webkit-scrollbar {
    height: 8px;
    background-color: var(--cloud-white);
}

.product-grid::-webkit-scrollbar-thumb {
    background-color: var(--sky-blue);
    border-radius: 4px;
}

/* 产品卡片调整 */
.product-card {
    flex: 0 0 auto;         /* 禁止伸缩保持原始尺寸 */
    width: 300px;           /* 固定卡片宽度 */
    height: 400px;
    margin-right: 2rem;     /* 右侧间距 */
    transition: transform 0.3s;
}

/* 移除最后一个卡片的右边距 */
.product-card:last-child {
    margin-right: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .product-card {
        width: 280px;
        height: 380px;
    }
}

/* 3D翻转产品卡片 */
.product-card {
    perspective: 1000px;
    width: 300px;
    height: 400px;
}

.card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    transition: transform 0.8s;
    transform-style: preserve-3d;
}

.product-card:hover .card-inner {
    transform: rotateY(180deg);
}

.card-front, .card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: 15px;
    box-shadow: 0 10px 20px rgba(135,206,235,0.2);
}

.card-front {
    background-size: cover;
    background-position: center;
}

.card-back {
    background: white;
    transform: rotateY(180deg);
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

/* “新品”两个字徽章美化 */

/*页面加载时带有缩放弹出动画（badgePop）

持续微浮动效果（badgeFloat）

悬停时颜色加深+阴影增强

红色渐变背景（#ff4d4d → #ff3333）

非对称圆角边框（15px左上/右下，3px右上/左下）

5度倾斜角度增加设计感

多层阴影营造立体感*/
.product-badge {
    /* 基础定位 */
    position: absolute;
    top: 15px;
    right: -5px;
    z-index: 10;

    /* 文字样式 */
    font: bold 0.9rem/1 'Microsoft YaHei';
    color: #fff;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
    letter-spacing: 1px;
    padding: 8px 20px;

    /* 背景与装饰 */
    background: linear-gradient(135deg, #ff4d4d 0%, #ff3333 100%);
    border-radius: 15px 3px 15px 3px;
    box-shadow: 0 3px 10px rgba(255,77,77,0.3);

    /* 动态效果 */
    transform: rotate(5deg);
    animation: badgePop 0.6s ease-out, badgeFloat 3s ease-in-out infinite 1s;
}

/* 徽章弹出动画 */
@keyframes badgePop {
    from {
        opacity: 0;
        transform: rotate(5deg) scale(0);
    }
    to {
        opacity: 1;
        transform: rotate(5deg) scale(1);
    }
}

/* 浮动效果 */
@keyframes badgeFloat {
    0%, 100% { transform: rotate(5deg) translateY(0); }
    50% { transform: rotate(5deg) translateY(-5px); }
}

/* 悬停互动效果 */
.product-card:hover .product-badge {
    background: linear-gradient(135deg, #ff3333 0%, #ff1a1a 100%);
    box-shadow: 0 5px 15px rgba(255,77,77,0.5);
    transition: all 0.3s ease;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .product-badge {
        font-size: 0.7rem;
        padding: 6px 15px;
        right: -8px;
        transform: rotate(5deg) scale(0.9);
    }
}

/* 针对不支持transform的备用样式 */
@supports not (transform: rotate(5deg)) {
    .product-badge {
        right: 0;
        border-radius: 15px;
    }
}

/*翻转后的卡片背面添加统一背景照片*/
.card-back {
    background: white;
    transform: rotateY(180deg);
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: center;

    /* 背景图已移除 */
    background-size: cover; /* 保证背景图覆盖整个卡片 */
    background-position: center; /* 居中显示图片核心区域 */
    position: relative; /* 为遮罩层定位做准备 */
    z-index: 1; /* 建立新的层叠上下文 */
}

/* 添加半透明遮罩层提升文字可读性 */
.card-back::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.85); /* 白色半透明遮罩 */
    z-index: -1; /* 置于背景图之上，内容之下 */
    border-radius: 15px; /* 与卡片圆角保持一致 */
}

/* 调整文字颜色适应新背景 */
.card-back h3,
.card-back p,
.card-back .price {
    position: relative; /* 确保文字在遮罩层之上 */
    z-index: 2; /* 提升文字层级 */
    color: #2C3E50; /* 保持原有深色文字 */
    text-shadow: 0 1px 2px rgba(255,255,255,0.3); /* 增加文字阴影提升可读性 */
}

/*为翻转卡片后的"查看详情"按钮设计的样式*/
/* 详情按钮基础样式 */
.btn-detail {
    /* 布局属性 */
    display: inline-block;
    padding: 12px 30px;
    margin-top: 1.5rem;

    /* 文字样式 */
    font: 500 1rem/1 'Microsoft YaHei', sans-serif;
    color: #fff !important; /* 强制白色文字 */
    text-decoration: none;
    letter-spacing: 1px;

    /* 背景与边框 */
    background: linear-gradient(135deg, var(--sky-blue) 0%, var(--deep-sky) 100%);
    border: none;
    border-radius: 25px;
    cursor: pointer;

    /* 阴影与过渡 */
    box-shadow: 0 4px 15px rgba(135,206,235,0.3);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    /* 3D立体效果 */
    position: relative;
    overflow: hidden;
}

/* 鼠标悬停效果 */
.btn-detail:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(135,206,235,0.5);
    background: linear-gradient(135deg, var(--deep-sky) 0%, #1E90FF 100%);

    /* 添加光晕动画 */
    animation: buttonGlow 1.5s ease-in-out infinite;
}

/* 点击按压效果 */
.btn-detail:active {
    transform: translateY(1px);
    box-shadow: 0 2px 8px rgba(135,206,235,0.2);
}

/* 光流动画 */
@keyframes buttonGlow {
    0% {
        box-shadow: 0 0 10px rgba(135,206,235,0.3);
    }
    50% {
        box-shadow: 0 0 15px rgba(135,206,235,0.6);
    }
    100% {
        box-shadow: 0 0 10px rgba(135,206,235,0.3);
    }
}

/* 移动端优化 */
@media (max-width: 768px) {
    .btn-detail {
        padding: 10px 25px;
        font-size: 0.9rem;

    }
}

/* 兼容性处理 */
@supports not (background: linear-gradient(red, blue)) {
    .btn-detail {
        background: var(--deep-sky);
    }
}


/* 响应式设计 */
@media (max-width: 768px) {
    .nav-menu ul {
        flex-direction: column;
        gap: 1rem;
    }

    .product-card {
        width: 100%;
        height: 500px;
    }
}




/* 商品分类样式 */
.product-categories {
    padding: 2rem 5%;
    background: var(--cloud-white);
}

.category-filter {
    margin-bottom: 2rem;
    overflow-x: auto;
}

.category-list {
    display: flex;
    gap: 1.5rem;
    padding: 1rem 0;
    list-style: none;
}

.category-item {
    flex-shrink: 0;
    padding: 1rem 2rem;
    border-radius: 30px;
    background: rgba(135, 206, 235, 0.1);
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.category-item:hover {
    background: var(--sky-blue);
    transform: translateY(-3px);
}

.category-item.active {
    background: var(--deep-sky);
    color: white;
    box-shadow: 0 5px 15px rgba(0, 191, 255, 0.3);
}

/* 动态产品网格 */
.dynamic-products {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 2rem;
    padding: 1rem 0;
    min-height: 200px;
}

/* 分类提示样式 */
.category-hint {
    grid-column: 1 / -1; /* 占满整行 */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 2rem;
    text-align: center;
    background: linear-gradient(135deg, rgba(135,206,235,0.05) 0%, rgba(240,251,255,0.1) 100%);
    border-radius: 15px;
    border: 2px dashed rgba(135,206,235,0.3);
    transition: all 0.3s ease;
}

.category-hint:hover {
    background: linear-gradient(135deg, rgba(135,206,235,0.1) 0%, rgba(240,251,255,0.2) 100%);
    border-color: rgba(135,206,235,0.5);
    transform: translateY(-2px);
}

/* 加载提示样式 */
.loading-hint {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 2rem;
    text-align: center;
    background: linear-gradient(135deg, rgba(135,206,235,0.05) 0%, rgba(240,251,255,0.1) 100%);
    border-radius: 15px;
    border: 2px dashed rgba(135,206,235,0.3);
    min-width: 300px;
    flex-shrink: 0;
}




/* 返回顶部按钮 */
.back-to-top {
    position: fixed;        /* 固定定位 */
    right: 30px;            /* 距离右侧30px */
    bottom: -60px;          /* 初始位置在可视区域下方 */
    width: 50px;            /* 按钮宽度 */
    height: 50px;           /* 按钮高度 */
    border: none;           /* 去除边框 */
    border-radius: 50%;     /* 圆形按钮 */
    background: var(--deep-sky); /* 使用主题色中的深天蓝色 */
    color: white;           /* 图标颜色 */
    cursor: pointer;        /* 鼠标手势 */
    opacity: 0;             /* 初始完全透明 */
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); /* 贝塞尔曲线过渡 */
    z-index: 9999;          /* 确保悬浮在所有内容之上 */
    box-shadow: 0 5px 15px rgba(0, 191, 255, 0.3); /* 蓝色系阴影 */
    display: flex;          /* 弹性布局用于居中图标 */
    align-items: center;    /* 垂直居中 */
    justify-content: center;/* 水平居中 */
}

/* 按钮显示状态 */
.back-to-top.active {
    bottom: 30px;           /* 距离底部30px */
    opacity: 0.9;           /* 显示按钮 */
}

/* 悬停互动效果 */
.back-to-top:hover {
    background: var(--sky-blue);    /* 变为浅蓝色 */
    transform: translateY(-3px) rotate(360deg); /* 上移3px并旋转 */
    box-shadow: 0 8px 25px rgba(135, 206, 235, 0.5); /* 增强阴影 */
    opacity: 1;             /* 完全显示 */
}

/* 移动端适配 */
@media (max-width: 768px) {
    .back-to-top {
        right: 15px;        /* 缩小右侧间距 */
        bottom: -50px;      /* 调整隐藏位置 */
        width: 40px;        /* 缩小尺寸 */
        height: 40px;
    }

    .back-to-top.active {
        bottom: 20px;       /* 显示位置上移 */
    }
}



/* 云端风格页脚 */
.sky-footer {
    background: rgba(255, 255, 255, 0.95);  /* 半透明白色背景 */
    backdrop-filter: blur(5px);             /* 毛玻璃效果 */
    padding: 1.5rem 0;                      /* 上下内边距 */
    margin-top: 4rem;                       /* 与上方内容间距 */
    box-shadow: 0 -5px 20px rgba(135, 206, 235, 0.1); /* 顶部浅蓝色阴影 */
    border-top: 1px solid rgba(135, 206, 235, 0.2);   /* 浅蓝色分隔线 */
}

.footer-content {
    max-width: 1200px;        /* 内容最大宽度 */
    margin: 0 auto;           /* 水平居中 */
    text-align: center;       /* 文本居中 */
}

.copyright {
    color: var(--text-dark);  /* 使用主题深色文字 */
    font-size: 0.9rem;        /* 适当缩小字号 */
    line-height: 1.6;         /* 行高优化 */
}

/* 备案链接样式 */
.beian-link {
    color: var(--deep-sky);   /* 使用主题深蓝色 */
    text-decoration: none;    /* 去除下划线 */
    transition: color 0.3s ease; /* 颜色过渡动画 */
    padding: 0.3rem 0.5rem;   /* 增加可点击区域 */
    border-radius: 4px;       /* 圆角边框 */
}

.beian-link:hover {
    color: var(--sky-blue);   /* 悬停变为浅蓝色 */
    background: rgba(135, 206, 235, 0.1); /* 浅色背景反馈 */
}


/* 移动端适配 */
@media (max-width: 768px) {
    .sky-footer {
        padding: 1rem 0;      /* 缩小内边距 */
        margin-top: 2rem;     /* 减少上方间距 */
    }
    .copyright {
        font-size: 0.8rem;    /* 缩小字号 */
        padding: 0 1rem;      /* 左右留白 */
    }
}

/* 动态内容样式 */
/* 无产品消息样式 */
.no-products-message, .error-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    text-align: center;
    color: #666;
    grid-column: 1 / -1; /* 占满整个网格 */
    min-height: 200px;
}

.no-products-message p, .error-message p {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 500;
}

/* 购物车商品控制按钮样式增强 */
.cart-item-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
}

.cart-item-controls button {
    padding: 4px 8px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.cart-item-increase, .cart-item-decrease {
    background: #e3f2fd;
    color: #1976d2;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cart-item-increase:hover, .cart-item-decrease:hover {
    background: #bbdefb;
}

.cart-item-remove {
    background: #ffebee;
    color: #d32f2f;
    padding: 4px 12px;
}

.cart-item-remove:hover {
    background: #ffcdd2;
}

.cart-item-quantity {
    min-width: 20px;
    text-align: center;
    font-weight: 500;
}

.cart-item-name {
    font-weight: 500;
    color: var(--text-dark);
}

.cart-item-price {
    color: var(--deep-sky);
    font-weight: 600;
}

/* 用户中心详细样式 */
.user-profile-section {
    margin-bottom: 20px;
}

.user-profile {
    display: flex;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;
    margin-bottom: 15px;
}

.profile-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 3px solid rgba(255, 255, 255, 0.3);
    margin-right: 15px;
}

.profile-info {
    flex: 1;
}

.username {
    margin: 0 0 5px 0;
    font-size: 1.4rem;
    font-weight: 600;
}

.account-id {
    margin: 0 0 10px 0;
    opacity: 0.9;
    font-size: 0.9rem;
}

.user-signature {
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
}

.user-signature:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

/* 用户统计信息 */
.user-stats {
    display: flex;
    justify-content: space-around;
    background: white;
    border-radius: 12px;
    padding: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.stat-item {
    text-align: center;
    flex: 1;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--deep-sky);
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
}

/* 详细信息区域 */
.user-details-section {
    margin-bottom: 20px;
}

.detail-card {
    background: white;
    border-radius: 12px;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.detail-header {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.detail-header i {
    color: var(--deep-sky);
    margin-right: 10px;
    font-size: 1.1rem;
}

.detail-header span {
    flex: 1;
    font-weight: 600;
    color: var(--text-dark);
}

.detail-action {
    background: var(--deep-sky);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.detail-action:hover {
    background: var(--primary-blue);
    transform: translateY(-1px);
}

/* 地址列表样式 */
.address-list {
    padding: 15px 20px;
}

.no-address {
    text-align: center;
    color: #999;
    padding: 20px;
    font-style: italic;
}

.address-item {
    padding: 12px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.address-item:hover {
    border-color: var(--deep-sky);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.address-item.default {
    border-color: var(--deep-sky);
    background: rgba(102, 126, 234, 0.05);
}

.address-header {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.receiver-name {
    font-weight: 600;
    margin-right: 10px;
}

.receiver-phone {
    color: #666;
    margin-right: 10px;
}

.default-tag {
    background: var(--deep-sky);
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.7rem;
}

.address-detail {
    color: #666;
    font-size: 0.9rem;
}

/* 订单信息样式 */
.order-summary {
    padding: 15px 20px;
}

.order-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.order-stat:last-child {
    border-bottom: none;
}

.order-label {
    color: #666;
}

.order-count {
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.9rem;
}

.order-count.pending {
    background: #fff3cd;
    color: #856404;
}

.order-count.total {
    background: #d1ecf1;
    color: #0c5460;
}

/* 好友信息样式 */
.friend-summary {
    padding: 15px 20px;
    text-align: center;
}

.friend-stat {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.friend-stat i {
    color: var(--deep-sky);
    font-size: 1.2rem;
}

.friend-count {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--deep-sky);
}

.friend-label {
    color: #666;
}

/* 个性签名编辑模态框 */
.signature-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.6);
    z-index: 100000;
    backdrop-filter: blur(5px);
}

.signature-content {
    position: relative;
    background: white;
    width: 90%;
    max-width: 400px;
    margin: 20% auto;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
    animation: modalSlide 0.3s ease-out;
}

.signature-header {
    background: linear-gradient(135deg, var(--sky-blue), var(--deep-sky));
    color: white;
    padding: 20px 25px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.signature-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.close-signature {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.close-signature:hover {
    background: rgba(255,255,255,0.2);
}

.signature-body {
    padding: 25px;
}

#signatureInput {
    width: 100%;
    height: 100px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 15px;
    font-size: 0.9rem;
    resize: none;
    outline: none;
    transition: border-color 0.3s ease;
    font-family: inherit;
    box-sizing: border-box;
}

#signatureInput:focus {
    border-color: var(--sky-blue);
}

.signature-counter {
    text-align: right;
    margin-top: 10px;
    font-size: 0.8rem;
    color: #666;
}

.signature-footer {
    padding: 20px 25px;
    background: #f8f9fa;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

.signature-cancel, .signature-save {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.signature-cancel {
    background: #e9ecef;
    color: #666;
}

.signature-cancel:hover {
    background: #dee2e6;
}

.signature-save {
    background: linear-gradient(135deg, var(--sky-blue), var(--deep-sky));
    color: white;
}

.signature-save:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* 头像上传模态框 */
.avatar-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.6);
    z-index: 100000;
    backdrop-filter: blur(5px);
}

.avatar-content {
    position: relative;
    background: white;
    width: 90%;
    max-width: 500px;
    margin: 10% auto;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
    animation: modalSlide 0.3s ease-out;
}

.avatar-header {
    background: linear-gradient(135deg, var(--sky-blue), var(--deep-sky));
    color: white;
    padding: 20px 25px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.avatar-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.close-avatar {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.close-avatar:hover {
    background: rgba(255,255,255,0.2);
}

.avatar-body {
    padding: 25px;
}

.avatar-preview {
    text-align: center;
    margin-bottom: 25px;
}

#avatarPreview {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid var(--sky-blue);
    object-fit: cover;
}

.avatar-upload-area {
    margin-bottom: 25px;
}

.upload-zone {
    border: 2px dashed #ddd;
    border-radius: 10px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.upload-zone:hover {
    border-color: var(--sky-blue);
    background: #e3f2fd;
}

.upload-zone i {
    font-size: 2rem;
    color: var(--sky-blue);
    margin-bottom: 15px;
    display: block;
}

.upload-zone p {
    margin: 0 0 10px 0;
    font-weight: 600;
    color: var(--text-dark);
}

.upload-zone span {
    font-size: 0.8rem;
    color: #666;
}

.avatar-presets h4 {
    margin: 0 0 15px 0;
    font-size: 1rem;
    color: var(--text-dark);
}

.preset-avatars {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
}

.preset-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 2px solid #ddd;
    cursor: pointer;
    transition: all 0.3s ease;
    object-fit: cover;
}

.preset-avatar:hover {
    border-color: var(--sky-blue);
    transform: scale(1.1);
}

.preset-avatar.selected {
    border-color: var(--deep-sky);
    border-width: 3px;
}

.avatar-footer {
    padding: 20px 25px;
    background: #f8f9fa;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

.avatar-cancel, .avatar-save {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.avatar-cancel {
    background: #e9ecef;
    color: #666;
}

.avatar-cancel:hover {
    background: #dee2e6;
}

.avatar-save {
    background: linear-gradient(135deg, var(--sky-blue), var(--deep-sky));
    color: white;
}

.avatar-save:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .user-center-modal {
        padding: 10px;
    }

    .user-center-content {
        width: 100%;
        margin: 0;
        max-height: calc(100vh - 20px);
    }

    .user-center-header {
        padding: 20px 25px 15px;
    }

    .user-center-header h2 {
        font-size: 1.3rem;
    }

    .user-profile-section {
        padding: 20px 25px;
    }

    .user-profile {
        flex-direction: column;
        text-align: center;
        align-items: center;
    }

    .avatar-container {
        margin-right: 0;
        margin-bottom: 15px;
    }

    .profile-avatar, .avatar-overlay {
        width: 80px;
        height: 80px;
    }

    .user-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .stat-item {
        padding: 12px 8px;
    }

    .quick-actions {
        padding: 20px 25px;
    }

    .action-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .action-item {
        padding: 15px 10px;
    }

    .user-details-section {
        padding: 20px 25px;
    }

    .detail-card {
        margin-bottom: 20px;
    }

    .user-menu {
        padding: 20px 25px;
    }

    .menu-item {
        padding: 15px 18px;
    }

    .signature-content, .avatar-content {
        width: 95%;
        margin: 10% auto;
    }

    .preset-avatars {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* ==================== 收藏、评论、分享功能样式 ==================== */

/* 收藏、评论、分享模态框样式 */
.favorites-modal,
.comments-modal,
.shares-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
}

/* 产品评论模态框需要更高的z-index */
.product-comment-modal,
.product-comments-list-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 15000;
    backdrop-filter: blur(8px);
}

.favorites-modal .modal-content,
.comments-modal .modal-content,
.shares-modal .modal-content,
.product-comment-modal .modal-content,
.product-comments-list-modal .modal-content {
    background: white;
    border-radius: 16px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #f0fdfa 0%, #ecfeff 100%);
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #0891b2;
    display: flex;
    align-items: center;
    gap: 8px;
}

.modal-header .close-modal {
    background: none;
    border: none;
    font-size: 24px;
    color: #666;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-header .close-modal:hover {
    background: rgba(0, 0, 0, 0.1);
    color: #333;
}

.modal-body {
    padding: 20px 25px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 15px 25px;
    border-top: 1px solid #e0e0e0;
    background: #f8f9fa;
    display: flex;
    justify-content: center;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.empty-state i {
    font-size: 3rem;
    color: #ddd;
    margin-bottom: 1rem;
}

.empty-state p {
    margin: 10px 0;
    font-size: 16px;
}

.btn-browse-products {
    background: var(--sky-blue);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    margin-top: 15px;
    transition: all 0.3s ease;
}

.btn-browse-products:hover {
    background: var(--deep-sky);
    transform: translateY(-2px);
}

/* 收藏列表样式 */
.favorites-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.favorite-item {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.favorite-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.favorite-image {
    width: 100%;
    height: 120px;
    overflow: hidden;
    border-radius: 8px 8px 0 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
}

.favorite-image img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.favorite-image:hover img {
    transform: scale(1.05);
}

.favorite-info {
    padding: 15px;
}

.favorite-info h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    color: var(--text-dark);
}

.favorite-price {
    color: var(--deep-sky);
    font-weight: 600;
    font-size: 18px;
    margin-bottom: 15px;
}

.favorite-actions {
    display: flex;
    gap: 10px;
}

.btn-view-detail {
    flex: 1;
    background: var(--sky-blue);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.btn-view-detail:hover {
    background: var(--deep-sky);
}

.btn-remove-favorite {
    background: #ff4757;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-remove-favorite:hover {
    background: #ff3742;
}

/* 评论列表样式 */
.comments-list-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.comment-item {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 4px solid var(--sky-blue);
}

.comment-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    gap: 15px;
}

.comment-product-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    object-fit: cover;
}

.comment-product-info {
    flex: 1;
}

.comment-product-info h4 {
    margin: 0 0 5px 0;
    font-size: 16px;
    color: var(--text-dark);
}

.comment-rating {
    color: #ffd700;
    font-size: 14px;
    margin-bottom: 5px;
}

.comment-date {
    color: #666;
    font-size: 12px;
}

.comment-status {
    display: flex;
    align-items: center;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.status-approved {
    background: #d4edda;
    color: #155724;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.status-rejected {
    background: #f8d7da;
    color: #721c24;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.comment-content p {
    margin: 0 0 10px 0;
    line-height: 1.6;
    color: var(--text-dark);
}

.comment-images {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.comment-image {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    object-fit: cover;
    cursor: pointer;
    transition: all 0.3s ease;
}

.comment-image:hover {
    transform: scale(1.1);
}

/* 分享列表样式 */
.shares-list-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.share-item {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 12px;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.share-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.share-product-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    object-fit: cover;
    margin-right: 15px;
}

.share-info {
    flex: 1;
}

.share-info h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    color: var(--text-dark);
}

.share-details {
    display: flex;
    gap: 15px;
    align-items: center;
}

.share-type {
    background: var(--sky-blue);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.share-date {
    color: #666;
    font-size: 12px;
}

.share-actions {
    display: flex;
    gap: 10px;
}

.btn-view-product {
    background: var(--sky-blue);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.btn-view-product:hover {
    background: var(--deep-sky);
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 20px;
}

.page-btn {
    background: white;
    border: 1px solid #ddd;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.page-btn:hover {
    background: var(--sky-blue);
    color: white;
    border-color: var(--sky-blue);
}

.page-btn.active {
    background: var(--deep-sky);
    color: white;
    border-color: var(--deep-sky);
}

/* ==================== 产品详情页面社交功能样式 ==================== */

/* 社交功能按钮区域 */
.social-buttons {
    display: flex;
    gap: 15px;
    margin-top: 20px;
    padding: 20px 0;
    border-top: 1px solid #e0e0e0;
    flex-wrap: wrap;
    justify-content: center;
}

/* 基础按钮样式 */
.btn-favorite,
.btn-comment,
.btn-view-comments,
.btn-share {
    display: flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid #e3f2fd;
    padding: 12px 18px;
    border-radius: 30px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-dark);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    text-decoration: none;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 按钮悬停效果 */
.btn-favorite:hover,
.btn-comment:hover,
.btn-view-comments:hover,
.btn-share:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
    border-color: var(--sky-blue);
}

/* 收藏按钮特殊样式 */
.btn-favorite {
    border-color: #ffcdd2;
    color: #d32f2f;
}

.btn-favorite:hover {
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
    border-color: #f44336;
    color: #c62828;
}

.btn-favorite.favorited {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    color: white;
    border-color: #d32f2f;
}

.btn-favorite.favorited:hover {
    background: linear-gradient(135deg, #d32f2f 0%, #b71c1c 100%);
}

/* 评论按钮样式 */
.btn-comment {
    border-color: #c8e6c9;
    color: #388e3c;
}

.btn-comment:hover {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    border-color: #4caf50;
    color: #2e7d32;
}

/* 查看评论按钮样式 */
.btn-view-comments {
    border-color: #bbdefb;
    color: #1976d2;
}

.btn-view-comments:hover {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-color: #2196f3;
    color: #1565c0;
}

/* 移除重复的样式定义，已在上面定义过 */

/* 数量显示样式 */
.favorite-count,
.share-count,
.comment-count {
    background: linear-gradient(135deg, var(--sky-blue) 0%, var(--deep-sky) 100%);
    color: white;
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    min-width: 24px;
    text-align: center;
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    animation: pulse 2s infinite;
}

/* 收藏数量特殊颜色 */
.favorite-count {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
}

/* 分享数量特殊颜色 */
.share-count {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

/* 评论数量特殊颜色 */
.comment-count {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
}

/* 数量变化动画 */
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* 分享下拉菜单 */
.share-dropdown {
    position: relative;
    display: inline-block;
}

/* 分享按钮样式 */
.btn-share {
    border-color: #fff3e0;
    color: #f57c00;
}

.btn-share:hover {
    background: linear-gradient(135deg, #fff8e1 0%, #ffcc02 100%);
    border-color: #ff9800;
    color: #e65100;
}

.share-options {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    padding: 10px 0;
    min-width: 150px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.share-dropdown.active .share-options {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.share-options button {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
    background: none;
    border: none;
    padding: 12px 16px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-dark);
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 2px 8px;
}

.share-options button:hover {
    background: linear-gradient(135deg, #f0f8ff 0%, #e3f2fd 100%);
    color: var(--deep-sky);
    transform: translateX(5px);
}

/* 分享选项图标颜色 */
.share-options button:nth-child(1):hover { /* 微信 */
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    color: #2e7d32;
}

.share-options button:nth-child(2):hover { /* 微博 */
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
    color: #c62828;
}

.share-options button:nth-child(3):hover { /* QQ */
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #1565c0;
}

.share-options button:nth-child(4):hover { /* 复制链接 */
    background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
    color: #7b1fa2;
}

.share-options i {
    width: 16px;
    text-align: center;
}

/* 产品评分显示 */
.product-rating-section {
    margin-top: 20px;
    padding: 15px 0;
    border-top: 1px solid #e0e0e0;
}

.rating-display {
    display: flex;
    align-items: center;
    gap: 10px;
}

.rating-stars {
    display: flex;
    gap: 2px;
}

.rating-stars .star {
    font-size: 16px;
    color: #ddd;
}

.rating-stars .star.filled {
    color: #ffd700;
}

.rating-stars .star.half {
    background: linear-gradient(90deg, #ffd700 50%, #ddd 50%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.rating-score {
    font-size: 18px;
    font-weight: 600;
    color: var(--deep-sky);
}

.rating-count {
    color: #666;
    font-size: 14px;
}

/* ==================== 评论功能样式 ==================== */

/* 评论表单样式 */
.comment-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.product-info {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 12px;
}

.comment-product-img {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    object-fit: cover;
}

.product-details h4 {
    margin: 0 0 5px 0;
    font-size: 16px;
    color: var(--text-dark);
}

.product-details p {
    margin: 0;
    color: var(--deep-sky);
    font-weight: 600;
}

/* 星级评分 */
.rating-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.rating-section label {
    font-weight: 600;
    color: var(--text-dark);
}

.star-rating {
    display: flex;
    gap: 5px;
}

.star-rating .star {
    font-size: 24px;
    color: #ddd;
    cursor: pointer;
    transition: all 0.3s ease;
}

.star-rating .star:hover,
.star-rating .star.active {
    color: #ffd700;
    transform: scale(1.1);
}

.rating-text {
    color: #666;
    font-size: 14px;
}

/* 评论内容 */
.comment-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.comment-content label {
    font-weight: 600;
    color: var(--text-dark);
}

.comment-content textarea {
    width: 100%;
    min-height: 100px;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    font-family: inherit;
    resize: vertical;
    transition: border-color 0.3s ease;
}

.comment-content textarea:focus {
    outline: none;
    border-color: var(--sky-blue);
}

.char-count {
    text-align: right;
    color: #666;
    font-size: 12px;
}

/* 评论图片上传 */
.comment-images {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.comment-images label {
    font-weight: 600;
    color: var(--text-dark);
}

.image-upload-area {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.upload-preview {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.preview-image {
    position: relative;
    width: 80px;
    height: 80px;
}

.preview-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
}

.remove-image {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff4757;
    color: white;
    border: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.upload-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    border: 2px dashed #ddd;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.upload-btn:hover {
    border-color: var(--sky-blue);
    background: var(--cloud-white);
}

.upload-btn i {
    font-size: 20px;
    color: var(--sky-blue);
    margin-bottom: 5px;
}

.upload-btn span {
    font-size: 12px;
    color: #666;
}

.upload-tips {
    margin-top: 10px;
}

.upload-tips p {
    margin: 0;
    color: #666;
    font-size: 12px;
}

/* 评论模态框按钮 */
.btn-cancel,
.btn-submit-comment {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-cancel {
    background: #e9ecef;
    color: #666;
}

.btn-cancel:hover {
    background: #dee2e6;
}

.btn-submit-comment {
    background: linear-gradient(135deg, var(--sky-blue), var(--deep-sky));
    color: white;
}

.btn-submit-comment:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* 评论列表模态框 */
.comments-summary {
    margin-bottom: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
}

.rating-overview {
    text-align: center;
}

.average-rating {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.average-rating .rating-score {
    font-size: 36px;
    font-weight: 700;
    color: var(--deep-sky);
}

.average-rating .rating-stars {
    font-size: 20px;
}

.average-rating p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

/* 评论筛选 */
.comments-filter {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.filter-btn {
    background: white;
    border: 2px solid #e0e0e0;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    color: var(--text-dark);
    transition: all 0.3s ease;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--sky-blue);
    border-color: var(--sky-blue);
    color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .social-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .btn-favorite,
    .btn-comment,
    .btn-view-comments,
    .btn-share {
        justify-content: center;
    }

    .share-options {
        left: 50%;
        transform: translateX(-50%) translateY(-10px);
    }

    .share-dropdown.active .share-options {
        transform: translateX(-50%) translateY(0);
    }

    .rating-display {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .comments-filter {
        justify-content: center;
    }

    .favorites-grid {
        grid-template-columns: 1fr;
    }
}

/* 评论和分享网格样式 */
.comments-grid,
.shares-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    padding: 20px 0;
}

.comment-item,
.share-item {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.comment-item:hover,
.share-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.comment-product,
.share-product {
    display: flex;
    align-items: center;
    padding: 15px;
    gap: 12px;
}

.comment-product img,
.share-product img {
    width: 60px;
    height: 60px;
    object-fit: contain;
    border-radius: 8px;
    background: #f8f9fa;
    flex-shrink: 0;
}

.comment-product-info,
.share-product-info {
    flex: 1;
    min-width: 0;
}

.comment-product-info h4,
.share-product-info h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: var(--text-dark);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.comment-rating {
    color: #ffa500;
    font-size: 14px;
}

.share-platform {
    color: var(--sky-blue);
    font-size: 12px;
    font-weight: 500;
}

.comment-content {
    padding: 0 15px 15px 15px;
}

.comment-content p {
    margin: 0 0 10px 0;
    color: var(--text-dark);
    font-size: 14px;
    line-height: 1.5;
}

.comment-time,
.share-time {
    color: #999;
    font-size: 12px;
    padding: 0 15px 15px 15px;
}

.no-comments,
.no-shares {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.no-comments i,
.no-shares i {
    display: block;
    margin-bottom: 15px;
}

@media (max-width: 768px) {
    .comments-grid,
    .shares-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .comment-product img,
    .share-product img {
        width: 50px;
        height: 50px;
    }
}