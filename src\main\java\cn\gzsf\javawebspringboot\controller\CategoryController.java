package cn.gzsf.javawebspringboot.controller;

import cn.gzsf.javawebspringboot.entity.Category;
import cn.gzsf.javawebspringboot.service.CategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/admin/category")
public class CategoryController {

    @Autowired
    private CategoryService categoryService;

    // 获取所有分类
    @GetMapping("/all")
    public List<Category> getAllCategories() {
        return categoryService.getAllCategories();
    }

    // 获取前端分类数据（用于index.html）
    @GetMapping("/frontend")
    public Map<String, Object> getFrontendCategories() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Category> categories = categoryService.getAllCategories();
            result.put("success", true);
            result.put("data", categories);
            result.put("message", "获取分类数据成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取分类数据失败: " + e.getMessage());
            e.printStackTrace();
        }
        return result;
    }

    // 分页获取分类列表
    @GetMapping("/page")
    public Map<String, Object> getCategoriesPage(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        Map<String, Object> result = new HashMap<>();

        // 计算偏移量
        int offset = (page - 1) * size;

        // 获取分页数据
        List<Category> categories = categoryService.getCategoriesPage(offset, size);
        int total = categoryService.getTotalCategoryCount();

        result.put("data", categories);
        result.put("total", total);
        result.put("page", page);
        result.put("size", size);
        result.put("totalPages", (int) Math.ceil((double) total / size));

        return result;
    }

    // 根据 ID 获取分类
    @GetMapping("/{id}")
    public Category getCategoryById(@PathVariable("id") Long id) {
        return categoryService.getCategoryById(id);
    }

    // 新增分类
    @PostMapping("/add")
    public boolean addCategory(@RequestBody Category category) {
        return categoryService.addCategory(category);
    }

    // 更新分类信息
    @PutMapping("/update")
    public boolean updateCategory(@RequestBody Category category) {
        return categoryService.updateCategory(category);
    }

    // 删除分类
    @DeleteMapping("/delete/{id}")
    public boolean deleteCategory(@PathVariable("id") Long id) {
        return categoryService.deleteCategory(id);
    }
}