-- 测试数据SQL文件
-- 用于确保各个功能有基础数据可以测试

-- 1. 插入测试分类数据
INSERT IGNORE INTO categories (id, name, description, created_time, updated_time) VALUES
(1, '护肤品', '各种护肤产品', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(2, '彩妆', '化妆品类产品', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(3, '香水', '各种香水产品', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(4, '身体护理', '身体护理产品', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);

-- 2. 插入测试产品数据
INSERT IGNORE INTO products (id, name, description, price, stock, image_url, created_time, updated_time) VALUES
(1, '兰蔻小黑瓶精华', '经典抗老精华，改善肌肤质感', 899.00, 100, 'images/products/lancome-serum.jpg', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(2, 'SK-II神仙水', '明星产品，改善肌肤纹理', 1299.00, 50, 'images/products/skii-essence.jpg', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(3, '雅诗兰黛小棕瓶', '修护精华，抗氧化', 799.00, 80, 'images/products/estee-lauder-serum.jpg', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(4, 'MAC口红', '经典红色口红', 199.00, 200, 'images/products/mac-lipstick.jpg', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(5, '香奈儿5号香水', '经典香水', 1599.00, 30, 'images/products/chanel-perfume.jpg', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);

-- 3. 插入产品分类关联
INSERT IGNORE INTO product_category (product_id, category_id) VALUES
(1, 1), (2, 1), (3, 1), -- 护肤品
(4, 2), -- 彩妆
(5, 3); -- 香水

-- 4. 插入产品图片
INSERT IGNORE INTO product_images (id, product_id, image_url, is_primary, created_time) VALUES
(1, 1, 'images/products/lancome-serum.jpg', 1, UNIX_TIMESTAMP() * 1000),
(2, 2, 'images/products/skii-essence.jpg', 1, UNIX_TIMESTAMP() * 1000),
(3, 3, 'images/products/estee-lauder-serum.jpg', 1, UNIX_TIMESTAMP() * 1000),
(4, 4, 'images/products/mac-lipstick.jpg', 1, UNIX_TIMESTAMP() * 1000),
(5, 5, 'images/products/chanel-perfume.jpg', 1, UNIX_TIMESTAMP() * 1000);

-- 5. 插入用户详情数据（如果不存在）
INSERT IGNORE INTO user_detail (phone, avatar_url, signature, created_time, updated_time) 
SELECT phone, 'default-avatar.jpg', '这个人很懒，什么都没留下~', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000
FROM user 
WHERE phone NOT IN (SELECT phone FROM user_detail);

-- 6. 插入测试订单数据
INSERT IGNORE INTO user_order (id, order_no, user_phone, total_amount, status, receiver_name, receiver_phone, receiver_address, created_time, updated_time) VALUES
(1, 'ORD20250616001', '13220248009', 1098.00, 2, '张三', '13220248009', '北京市朝阳区测试地址', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(2, 'ORD20250616002', '13220248009', 398.00, 4, '张三', '13220248009', '北京市朝阳区测试地址', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);

-- 7. 插入订单详情数据
INSERT IGNORE INTO order_detail (id, order_id, product_id, quantity, price, subtotal, created_time) VALUES
(1, 1, 1, 1, 899.00, 899.00, UNIX_TIMESTAMP() * 1000),
(2, 1, 4, 1, 199.00, 199.00, UNIX_TIMESTAMP() * 1000),
(3, 2, 4, 2, 199.00, 398.00, UNIX_TIMESTAMP() * 1000);

-- 8. 插入测试收藏数据
INSERT IGNORE INTO product_favorite (id, user_phone, product_id, created_time, updated_time) VALUES
(1, '13220248009', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(2, '13220248009', 2, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(3, '18823912577', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);

-- 9. 插入测试评论数据
INSERT IGNORE INTO product_comment (id, user_phone, product_id, content, rating, status, created_time, updated_time) VALUES
(1, '13220248009', 1, '非常好用的精华，效果很明显！', 5, 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(2, '18823912577', 2, '神仙水确实名不虚传，皮肤变好了很多', 5, 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(3, '13220248009', 4, '颜色很正，质地也不错', 4, 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);

-- 10. 插入测试分享数据
INSERT IGNORE INTO product_share (id, user_phone, product_id, share_type, share_content, created_time) VALUES
(1, '13220248009', 1, '微信', '推荐这款精华给大家', UNIX_TIMESTAMP() * 1000),
(2, '18823912577', 2, '微博', '神仙水真的很好用', UNIX_TIMESTAMP() * 1000);

-- 提示信息
SELECT '测试数据插入完成！' as message;
SELECT '产品数量:' as info, COUNT(*) as count FROM products;
SELECT '用户数量:' as info, COUNT(*) as count FROM user;
SELECT '订单数量:' as info, COUNT(*) as count FROM user_order;
SELECT '收藏数量:' as info, COUNT(*) as count FROM product_favorite;
SELECT '评论数量:' as info, COUNT(*) as count FROM product_comment;
