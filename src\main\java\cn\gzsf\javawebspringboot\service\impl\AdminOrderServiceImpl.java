package cn.gzsf.javawebspringboot.service.impl;

import cn.gzsf.javawebspringboot.entity.UserOrder;
import cn.gzsf.javawebspringboot.mapper.AdminOrderMapper;
import cn.gzsf.javawebspringboot.mapper.OrderMapper;
import cn.gzsf.javawebspringboot.service.AdminOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 管理员订单服务实现类
 */
@Service
public class AdminOrderServiceImpl implements AdminOrderService {

    @Autowired
    private AdminOrderMapper adminOrderMapper;
    
    @Autowired
    private OrderMapper orderMapper;

    @Override
    public Map<String, Object> getOrderList(int page, int size, Integer status) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 计算偏移量
            int offset = (page - 1) * size;
            
            // 获取订单列表
            List<UserOrder> orders = adminOrderMapper.getOrderList(offset, size, status);
            
            // 获取总数
            int total = adminOrderMapper.getOrderCount(status);
            
            result.put("data", orders);
            result.put("total", total);
            
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("获取订单列表失败", e);
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getOrderStats() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 总订单数
            int totalOrders = adminOrderMapper.getTotalOrderCount();
            
            // 待处理订单数（待付款 + 待发货）
            int pendingOrders = adminOrderMapper.getPendingOrderCount();
            
            // 已完成订单数
            int completedOrders = adminOrderMapper.getCompletedOrderCount();
            
            // 总销售额
            Double totalAmount = adminOrderMapper.getTotalSalesAmount();
            
            stats.put("totalOrders", totalOrders);
            stats.put("pendingOrders", pendingOrders);
            stats.put("completedOrders", completedOrders);
            stats.put("totalAmount", totalAmount != null ? totalAmount.toString() : "0");
            
        } catch (Exception e) {
            e.printStackTrace();
            // 返回默认值
            stats.put("totalOrders", 0);
            stats.put("pendingOrders", 0);
            stats.put("completedOrders", 0);
            stats.put("totalAmount", "0");
        }
        
        return stats;
    }

    @Override
    public boolean shipOrder(String orderNo) {
        try {
            UserOrder order = orderMapper.getOrderByOrderNo(orderNo);
            if (order != null && order.getStatus() == 2) { // 只有待发货状态才能发货
                order.setStatus(3); // 3-待收货
                order.setUpdatedTime(System.currentTimeMillis());
                return orderMapper.updateOrderStatus(order) > 0;
            }
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean cancelOrder(String orderNo) {
        try {
            UserOrder order = orderMapper.getOrderByOrderNo(orderNo);
            if (order != null && (order.getStatus() == 1 || order.getStatus() == 2)) { // 待付款或待发货状态才能取消
                order.setStatus(5); // 5-已取消
                order.setUpdatedTime(System.currentTimeMillis());
                return orderMapper.updateOrderStatus(order) > 0;
            }
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public UserOrder getOrderDetail(String orderNo) {
        try {
            return orderMapper.getOrderByOrderNo(orderNo);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
