package cn.gzsf.javawebspringboot.dao;

import cn.gzsf.javawebspringboot.entity.Category;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CategoryDao {
    // 查询所有分类
    List<Category> findAllCategories();
    // 根据 ID 查询分类
    Category findCategoryById(@Param("id") Long id);
    // 插入新分类
    int insertCategory(Category category);
    // 更新分类信息
    int updateCategory(Category category);
    // 删除分类
    int deleteCategory(@Param("id") Long id);

    // 分页查询分类
    List<Category> findCategoriesPage(@Param("offset") int offset, @Param("size") int size);

    // 获取分类总数
    int getTotalCategoryCount();
}