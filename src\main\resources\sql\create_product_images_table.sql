-- 创建产品图片表
CREATE TABLE IF NOT EXISTS `product_images` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '图片ID',
  `product_id` bigint(20) NOT NULL COMMENT '产品ID',
  `image_url` varchar(500) NOT NULL COMMENT '图片URL',
  `image_name` varchar(255) DEFAULT NULL COMMENT '图片名称',
  `is_primary` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为主图(0:否, 1:是)',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序顺序',
  `created_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `updated_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_is_primary` (`is_primary`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品图片表';

-- 为现有产品添加一些示例图片数据
INSERT INTO `product_images` (`product_id`, `image_url`, `image_name`, `is_primary`, `sort_order`, `created_time`, `updated_time`) VALUES
(1, '/images/product1_main.jpg', '产品1主图.jpg', 1, 0, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(1, '/images/product1_detail1.jpg', '产品1详情图1.jpg', 0, 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(1, '/images/product1_detail2.jpg', '产品1详情图2.jpg', 0, 2, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(2, '/images/product2_main.jpg', '产品2主图.jpg', 1, 0, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(2, '/images/product2_detail1.jpg', '产品2详情图1.jpg', 0, 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(3, '/images/product3_main.jpg', '产品3主图.jpg', 1, 0, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);
