package cn.gzsf.javawebspringboot.entity;

import java.time.LocalDateTime;

/**
 * 系统备份实体类
 */
public class SystemBackup {
    private Long id;
    private String name;            // 备份名称
    private String type;            // 备份类型：full, data, structure
    private String filePath;        // 备份文件路径
    private Long fileSize;          // 文件大小（字节）
    private String status;          // 备份状态：success, failed, processing
    private LocalDateTime createTime; // 创建时间
    private String description;     // 备份描述
    private String creator;         // 创建者

    public SystemBackup() {}

    public SystemBackup(String name, String type, String filePath, String creator) {
        this.name = name;
        this.type = type;
        this.filePath = filePath;
        this.creator = creator;
        this.createTime = LocalDateTime.now();
        this.status = "processing";
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    @Override
    public String toString() {
        return "SystemBackup{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", type='" + type + '\'' +
                ", filePath='" + filePath + '\'' +
                ", fileSize=" + fileSize +
                ", status='" + status + '\'' +
                ", createTime=" + createTime +
                ", creator='" + creator + '\'' +
                '}';
    }
}
