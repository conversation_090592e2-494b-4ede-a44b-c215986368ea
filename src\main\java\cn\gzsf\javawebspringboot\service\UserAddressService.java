package cn.gzsf.javawebspringboot.service;

import cn.gzsf.javawebspringboot.entity.UserAddress;

import java.util.List;

/**
 * 用户地址服务接口
 */
public interface UserAddressService {
    
    /**
     * 根据用户手机号获取地址列表
     * @param userPhone 用户手机号
     * @return 地址列表
     */
    List<UserAddress> getAddressByUserPhone(String userPhone);
    
    /**
     * 添加地址
     * @param address 地址信息
     * @return 是否成功
     */
    boolean addAddress(UserAddress address);
    
    /**
     * 更新地址
     * @param address 地址信息
     * @return 是否成功
     */
    boolean updateAddress(UserAddress address);
    
    /**
     * 删除地址
     * @param id 地址ID
     * @return 是否成功
     */
    boolean deleteAddress(Integer id);
    
    /**
     * 设为默认地址
     * @param id 地址ID
     * @param userPhone 用户手机号
     * @return 是否成功
     */
    boolean setDefaultAddress(Integer id, String userPhone);
}
