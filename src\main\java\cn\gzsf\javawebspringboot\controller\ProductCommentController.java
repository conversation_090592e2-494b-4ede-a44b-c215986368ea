package cn.gzsf.javawebspringboot.controller;

import cn.gzsf.javawebspringboot.entity.ProductComment;
import cn.gzsf.javawebspringboot.service.ProductCommentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 产品评论控制器
 */
@RestController
@RequestMapping("/api/comments")
public class ProductCommentController {
    
    @Autowired
    private ProductCommentService commentService;
    
    /**
     * 添加评论
     */
    @PostMapping("/add")
    public Map<String, Object> addComment(@RequestBody ProductComment comment) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = commentService.addComment(comment);
            if (success) {
                result.put("success", true);
                result.put("message", "评论提交成功，等待审核");
            } else {
                result.put("success", false);
                result.put("message", "评论提交失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "评论提交失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 上传评论图片
     */
    @PostMapping("/upload-images")
    public Map<String, Object> uploadCommentImages(@RequestParam("files") MultipartFile[] files) {
        Map<String, Object> result = new HashMap<>();
        List<String> imageUrls = new ArrayList<>();

        try {
            // 使用与其他图片上传一致的路径策略
            String projectPath = System.getProperty("user.dir");
            String uploadDir = projectPath + "/src/main/resources/static/images/";
            File dir = new File(uploadDir);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                System.out.println("📁 创建图片目录: " + uploadDir + ", 结果: " + created);
            }

            for (MultipartFile file : files) {
                if (!file.isEmpty()) {
                    String originalFilename = file.getOriginalFilename();
                    String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
                    String filename = "comment_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8) + extension;

                    File targetFile = new File(uploadDir + filename);
                    file.transferTo(targetFile);

                    System.out.println("📸 评论图片保存成功: " + targetFile.getAbsolutePath());
                    imageUrls.add("/images/" + filename);
                }
            }

            result.put("success", true);
            result.put("imageUrls", imageUrls);
            result.put("message", "图片上传成功");
            System.out.println("✅ 评论图片上传成功，返回URLs: " + imageUrls);
        } catch (IOException e) {
            System.err.println("❌ 评论图片上传失败: " + e.getMessage());
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "图片上传失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取产品评论列表
     */
    @GetMapping("/product/{productId}")
    public Map<String, Object> getProductComments(@PathVariable Long productId,
                                                @RequestParam(defaultValue = "1") Integer status,
                                                @RequestParam(defaultValue = "1") int page,
                                                @RequestParam(defaultValue = "10") int size) {
        return commentService.getProductComments(productId, status, page, size);
    }
    
    /**
     * 获取用户评论列表
     */
    @GetMapping("/user/{userPhone}")
    public Map<String, Object> getUserComments(@PathVariable String userPhone,
                                             @RequestParam(defaultValue = "1") int page,
                                             @RequestParam(defaultValue = "10") int size) {
        return commentService.getUserComments(userPhone, page, size);
    }
    
    /**
     * 获取产品平均评分
     */
    @GetMapping("/rating/{productId}")
    public Map<String, Object> getProductAverageRating(@PathVariable Long productId) {
        Map<String, Object> result = new HashMap<>();
        try {
            Double rating = commentService.getProductAverageRating(productId);
            result.put("success", true);
            result.put("rating", rating);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取评分失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 更新评论状态（管理员用）
     */
    @PostMapping("/admin/status")
    public Map<String, Object> updateCommentStatus(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();
        try {
            Long id = Long.valueOf(request.get("id").toString());
            Integer status = Integer.valueOf(request.get("status").toString());
            
            boolean success = commentService.updateCommentStatus(id, status);
            if (success) {
                result.put("success", true);
                result.put("message", "状态更新成功");
            } else {
                result.put("success", false);
                result.put("message", "状态更新失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "状态更新失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 删除评论（管理员用）
     */
    @DeleteMapping("/admin/{id}")
    public Map<String, Object> deleteComment(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = commentService.deleteComment(id);
            if (success) {
                result.put("success", true);
                result.put("message", "删除成功");
            } else {
                result.put("success", false);
                result.put("message", "删除失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取所有评论（管理员用）
     */
    @GetMapping("/admin/all")
    public Map<String, Object> getAllComments(@RequestParam(required = false) Integer status,
                                            @RequestParam(defaultValue = "1") int page,
                                            @RequestParam(defaultValue = "10") int size) {
        return commentService.getAllComments(status, page, size);
    }
    
    /**
     * 获取评论统计（管理员用）
     */
    @GetMapping("/admin/stats")
    public Map<String, Object> getCommentStats() {
        Map<String, Object> result = new HashMap<>();
        try {
            result.put("success", true);
            result.put("data", commentService.getCommentStats());
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取评论统计失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 获取评论详情
     */
    @GetMapping("/{id}")
    public Map<String, Object> getCommentById(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            ProductComment comment = commentService.getCommentById(id);
            if (comment != null) {
                result.put("success", true);
                result.put("data", comment);
            } else {
                result.put("success", false);
                result.put("message", "评论不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取评论详情失败：" + e.getMessage());
        }
        return result;
    }
}
