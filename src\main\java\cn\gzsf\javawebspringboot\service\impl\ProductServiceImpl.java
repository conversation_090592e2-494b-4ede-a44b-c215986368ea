package cn.gzsf.javawebspringboot.service.impl;

import cn.gzsf.javawebspringboot.dao.ProductDao;
import cn.gzsf.javawebspringboot.entity.Product;
import cn.gzsf.javawebspringboot.service.ProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

// 产品服务实现类，实现产品服务接口中的方法
@Service
public class ProductServiceImpl implements ProductService {
    @Autowired
    private ProductDao productDao;

    @Override
    public List<Product> getAllProducts() {
        return productDao.findAllProducts();
    }

    @Override
    public List<Product> searchProducts(String keyword) {
        return productDao.searchProducts(keyword);
    }

    @Override
    public boolean updateProduct(Product product) {
        int result = productDao.updateProduct(product);
        return result > 0;
    }

    @Override
    public boolean addProduct(Product product) {
        int result = productDao.addProduct(product);
        System.out.println("添加产品成功");
        return result > 0;
    }


    @Override
    public boolean deleteProduct(Long id) {
        try {
            // 先删除 product_category 表中与产品关联的记录
            productDao.deleteProductCategoriesByProductId(id);
            // 再删除产品记录
            int result = productDao.deleteProduct(id);
            return result > 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public Product getProductById(Long id) {
        // 假设 ProductDao 中有根据 ID 查询产品的方法
        return productDao.findProductById(id);
    }

    @Override
    public List<Product> getProductsPage(int offset, int size) {
        return productDao.findProductsPage(offset, size);
    }

    @Override
    public int getTotalProductCount() {
        return productDao.getTotalProductCount();
    }
}