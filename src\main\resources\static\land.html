<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <!-- 基本元数据 -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>息壤集 | 登录注册</title>
    <!-- 引入Font Awesome图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- 引入Axios库 -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        /* 全局样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        /* 页面主体样式 */
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh; /* 确保页面高度至少为视口高度 */
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); /* 渐变背景 */
            overflow: hidden; /* 隐藏溢出内容 */
        }

        /* 装饰元素容器 */
        .decorations {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none; /* 防止装饰元素干扰交互 */
        }

        /* 气泡样式 */
        .bubble {
            position: absolute;
            background: rgba(255, 255, 255, 0.2); /* 半透明白色 */
            border-radius: 50%; /* 圆形 */
            animation: float 15s infinite linear; /* 浮动动画 */
        }

        /* 添加浮动动画关键帧定义 */
        @keyframes float {
            0% {
                transform: translate(0, 0);
            }
            50% {
                transform: translate(50px, 50px);
            }
            100% {
                transform: translate(0, 0);
            }
        }

        /* 主容器样式 */
        .container {
            position: relative;
            width: 400px;
            background: rgba(255, 255, 255, 0.95); /* 半透明白色背景 */
            border-radius: 20px; /* 圆角 */
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1); /* 阴影效果 */
            overflow: hidden; /* 隐藏溢出内容 */
            transition: 0.5s cubic-bezier(0.4, 0, 0.2, 1); /* 平滑过渡效果 */
        }

        /* 表单容器样式 */
        .form-container {
            padding: 40px;
            transition: 0.5s;
        }

        /* 表单标题样式 */
        .form-header {
            text-align: center;
            margin-bottom: 40px;
            transform: translateY(-20px); /* 初始位置，用于动画 */
            opacity: 0; /* 初始透明，用于动画 */
            animation: slideIn 0.6s 0.2s forwards; /* 滑入动画 */
        }

        /* 标题滑入动画 */
        @keyframes slideIn {
            to { transform: translateY(0); opacity: 1; }
        }

        /* 主标题样式 */
        .form-header h2 {
            color: #2d3436;
            font-size: 2.2em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #4ecdc4, #45b7b0); /* 渐变文字效果 */
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        /* 表单组样式 */
        .form-group {
            position: relative;
            margin-bottom: 25px;
            transform: translateX(-30px); /* 初始位置，用于动画 */
            opacity: 0; /* 初始透明，用于动画 */
            animation: formItem 0.6s forwards; /* 表单项动画 */
        }

        /* 表单项动画 */
        @keyframes formItem {
            to { transform: translateX(0); opacity: 1; }
        }

        /* 图标样式 */
        .form-group i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #a8edea;
            transition: 0.3s;
        }

        /* 输入框样式 */
        input {
            width: 100%;
            padding: 12px 20px 12px 40px; /* 左侧留出图标空间 */
            border: 2px solid #e0f2f1;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
        }

        /* 输入框聚焦效果 */
        input:focus {
            border-color: #4ecdc4;
            box-shadow: 0 0 15px rgba(78, 205, 196, 0.2);
        }

        /* 输入框聚焦时图标颜色变化 */
        input:focus + i {

            color: #4ecdc4;
        }

        /* 按钮样式 */
        button {
            width: 100%;
            padding: 14px;
            background: linear-gradient(45deg, #4ecdc4, #45b7b0); /* 渐变背景 */
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: 0.3s;
            margin-top: 15px;
            position: relative;
            overflow: hidden;
        }

        /* 按钮悬停效果 */
        button:hover {
            transform: translateY(-2px); /* 轻微上移 */
            box-shadow: 0 5px 15px rgba(78, 205, 196, 0.3); /* 阴影效果 */
        }

        /* 按钮光效 */
        button::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: rgba(255, 255, 255, 0.1);
            transform: rotate(45deg);
            transition: 0.5s;
        }

        /* 按钮悬时光效动画 */
        button:hover::after {
            left: 120%;
        }

        /* 表单切换链接样式 */
        .switch-form {
            text-align: center;
            margin-top: 25px;
            animation: fadeIn 1s 0.8s forwards; /* 淡入动画 */
            opacity: 0; /* 初始透明，用于动画 */
        }

        /* 淡入动画 */
        @keyframes fadeIn {
            to { opacity: 1; }
        }

        /* 切换链接样式 */
        .switch-form a {
            color: #4ecdc4;
            text-decoration: none;
            font-weight: 500;
            cursor: pointer;
            position: relative;
        }

        /* 链接下划线效果 */
        .switch-form a::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background: #4ecdc4;
            transition: 0.3s;
        }

        /* 链接悬停时下划线展开 */
        .switch-form a:hover::after {
            width: 100%;
        }

        /* 错误消息样式 */
        .error-message {
            color: #ff6b6b;
            font-size: 14px;
            margin-top: 5px;
            display: none; /* 默认隐藏 */
        }

        /* 响应式设计 - 小屏幕适配 */
        @media (max-width: 480px) {
            .container {
                width: 90%; /* 小屏幕上宽度调整为90% */
            }
        }

        /* 手机号输入组专属样式 */
        .phone-input-group {
            display: flex; /* 使用flex布局 */
            align-items: center; /* 垂直居中对齐 */
            gap: 10px; /* 输入框和按钮之间的间距 */
        }

        /* 输入框包裹层 */
        .input-wrapper {
            position: relative;
            flex: 1; /* 输入框占据剩余空间 */
        }

        /* 返回按钮样式 */
        .back-button {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            padding: 12px 20px;
            background: linear-gradient(45deg, #4ecdc4, #45b7b0);
            color: white !important;
            border-radius: 30px;
            text-decoration: none;
            font-weight: 500;
            box-shadow: 0 3px 15px rgba(78, 205, 196, 0.3);
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            border: 2px solid rgba(255,255,255,0.2);
        }

        .back-button i {
            margin-right: 8px;
            font-size: 0.9em;
        }

        /* 悬停动效 */
        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(78, 205, 196, 0.5);
        }

        .back-button:active {
            transform: translateY(0);
        }

        /* 响应式调整 */
        @media (max-width: 480px) {
            .back-button {
                padding: 10px 15px;
                font-size: 14px;
                top: 15px;
                right: 15px;
            }
        }

        /****** 短信按钮样式 ******/
        .sms-btn {
            width: auto; /* 自适应宽度 */
            padding: 8px 15px;
            font-size: 12px;
            background: #4ecdc4; /* 保持统一配色 */
            color: white;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            transition: 0.3s;
            /* 移除原浮动动画属性 */
        }

        /* 禁用状态 */
        .sms-btn:disabled {
            background: #f0f0f0;
            border-color: #ddd;
            color: #999;
            cursor: not-allowed;
        }

        /****** 密码强度指示器 ******/
        .password-strength {
            margin-top: 8px;
            height: 4px;
            border-radius: 2px;
            background: #eee;
            overflow: hidden;
        }

        .strength-bar {
            height: 100%;
            width: 0;
            transition: 0.3s;
        }

        /* 强度颜色规则 */
        .strength-weak { background: #ff6b6b; width: 33% !important; }
        .strength-medium { background: #feca57; width: 66% !important; }
        .strength-strong { background: #4ecdc4; width: 100% !important; }

        /* 调整输入框右侧间距 */
        /* 修改电话号码输入框样式 */
        #registerPhone {
            padding-right: 20px !important; /* 恢复默认右侧间距 */
        }

        /* 隐藏数字输入框的上下箭头 */
        #smsCode::-webkit-inner-spin-button,
        #smsCode::-webkit-outer-spin-button {
            -webkit-appearance: none; /* Chrome/Safari */
            margin: 0;
        }
        #smsCode {
            -moz-appearance: textfield; /* Firefox */
        }

        /* 新增：保持按钮与输入框高度一致 */
        .input-wrapper {
            position: relative;
            display: inline-block; /* 改为行内块级元素 */
            width: 100%; /* 占满父容器 */
        }

        /* 优化移动端显示 */
        @media (max-width: 480px) {
            .sms-btn {
                padding: 6px 12px;
                font-size: 11px;
            }
        }




        /* 新增雪花样式 */
        .snowflake {
            position: absolute;
            pointer-events: none;
            animation: snowfall linear forwards;
            opacity: 0;
            filter: drop-shadow(0 0 2px rgba(255,255,255,0.5));
        }

        /* 雪花动画 */
        @keyframes snowfall {
            0% {
                opacity: 1;
                transform: translate(-50%, -50%) scale(0.8) rotate(0deg);
            }
            100% {
                opacity: 0;
                transform: translate(-50%, calc(-50% + 100px)) scale(0.2) rotate(360deg);
                filter: blur(2px);
            }
        }

        /* 新增六种不同雪花形状 */
        .snowflake:nth-child(6n+1) { clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%); } /* 星形 */
        .snowflake:nth-child(6n+2) { clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%); } /* 菱形 */
        .snowflake:nth-child(6n+3) { clip-path: circle(50% at 50% 50%); } /* 圆形 */
        .snowflake:nth-child(6n+4) { clip-path: polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%); } /* 五边形 */
        .snowflake:nth-child(6n+5) { clip-path: polygon(20% 0%, 80% 0%, 100% 100%, 0% 100%); } /* 平行四边形 */
        .snowflake:nth-child(6n+6) { clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%); } /* 六边形 */
        /*雪花样式结束*/

    </style>
</head>
<body>
<!-- 右上角返回图标 -->
<a href="index.html" class="back-button">
    <i class="fas fa-arrow-left"></i>
    返回首页
</a>
<!-- 装饰元素 - 气泡 -->
<div class="decorations">
    <div class="bubble" style="width:30px; height:30px; left:10%; top:20%"></div>
    <div class="bubble" style="width:50px; height:50px; left:80%; top:60%"></div>
    <div class="bubble" style="width:40px; height:40px; left:70%; top:30%"></div>
</div>

<!-- 主容器 -->
<div class="container" id="mainContainer">
    <!-- 登录表单容器 -->
    <div class="form-container" id="loginFormContainer">
        <div class="form-header">
            <h2>欢迎回来</h2>
            <p>让美好继续发生</p>
        </div>
        <form id="loginForm">
            <div class="form-group">
                <i class="fas fa-user"></i>
                <input type="text" id="loginUsername" placeholder="账号/电话" required>
                <div class="error-message" id="loginUsernameError"></div>
            </div>

            <div class="form-group">
                <i class="fas fa-lock"></i>
                <input type="password" id="loginPassword" placeholder="密码" required>
                <div class="error-message" id="loginPasswordError"></div>
            </div>

            <button type="button" onclick="login()">登录</button>
            <div class="switch-form">
                新用户？ <a onclick="showRegister()">立即注册</a>
            </div>
        </form>
    </div>

    <!-- 注册表单容器 (初始隐藏) -->
    <div class="form-container" id="registerFormContainer" style="display: none;">
        <div class="form-header">
            <h2>加入我们</h2>
            <p>开启全新旅程</p>
        </div>

        <!-- 修改后的注册表单结构 -->
        <form id="registerForm">
            <!-- 用户名输入 -->
            <div class="form-group">
                <i class="fas fa-user"></i>
                <input type="text" id="registerUsername" placeholder="用户名（2-16位）" required>
                <div class="error-message" id="registerUsernameError"></div>
            </div>

            <!-- 手机号输入+短信验证 -->
            <div class="form-group phone-input-group">
                <div class="input-wrapper">
                    <i class="fas fa-mobile-alt"></i>
                    <input type="tel" id="registerPhone" placeholder="中国大陆手机号" required>
                </div>
                <!-- 将短信按钮移到输入框右侧 -->
                <button type="button" id="sendCodeBtn" class="sms-btn">获取验证码</button>
            </div>

            <!-- 短信验证码输入 -->
            <div class="form-group">
                <i class="fas fa-shield-alt"></i>
                <input type="number" id="smsCode" placeholder="6位短信验证码" required>
                <div class="error-message" id="smsCodeError"></div>
            </div>

            <!-- 密码输入 -->
            <div class="form-group">
                <i class="fas fa-lock"></i>
                <input type="password" id="registerPassword" placeholder="密码（8-20位，含大小写+特殊字符）" required>
                <div class="error-message" id="registerPasswordError"></div>
                <!-- 密码强度指示条 -->
                <div class="password-strength">
                    <div class="strength-bar"></div>
                </div>
            </div>

            <!-- 确认密码 -->
            <div class="form-group">
                <i class="fas fa-lock"></i>
                <input type="password" id="registerConfirmPassword" placeholder="确认密码" required>
                <div class="error-message" id="registerConfirmPasswordError"></div>
            </div>

            <button type="button" onclick="register()">立即注册</button>

            <div class="switch-form">
                已有账号？ <a onclick="showLogin()">立即登录</a>
            </div>
        </form>
    </div>
</div>

<script>
    // 动态生成气泡
    function createBubbles() {
        const container = document.querySelector('.decorations');
        for(let i = 0; i < 5; i++) {
            const bubble = document.createElement('div');
            bubble.className = 'bubble';
            const size = Math.random() * 30 + 20; // 随机大小 (20-50px)
            bubble.style.cssText = `
                    width: ${size}px;
                    height: ${size}px;
                    left: ${Math.random() * 90}%; // 随机水平位置
                    top: ${Math.random() * 90}%; // 随机垂直位置
                    animation-delay: ${Math.random() * 5}s; // 随机动画延迟
                `;
            container.appendChild(bubble);
        }
    }
    createBubbles(); // 调用函数生成气泡

    // 新增登录成功提示样式
    const style = document.createElement('style');
    style.innerHTML = `
            .success-message {
                color: #4ecdc4;
                font-size: 16px;
                text-align: center;
                margin-top: 20px;
                padding: 15px;
                background: rgba(78, 205, 196, 0.1);
                border-radius: 8px;
                animation: fadeIn 0.6s forwards;
            }
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(-10px); }
                to { opacity: 1; transform: translateY(0); }
            }
        `;
    document.head.appendChild(style);

    // 显示注册表单
    function showRegister() {
        document.getElementById('loginFormContainer').style.display = 'none';
        document.getElementById('registerFormContainer').style.display = 'block';
        // 添加缩放动画效果
        document.getElementById('mainContainer').style.transform = 'scale(0.95)';
        setTimeout(() => {
            document.getElementById('mainContainer').style.transform = 'scale(1)';
        }, 10);
    }

    // 显示登录表单
    function showLogin() {
        document.getElementById('registerFormContainer').style.display = 'none';
        document.getElementById('loginFormContainer').style.display = 'block';
        // 添加缩放动画效果
        document.getElementById('mainContainer').style.transform = 'scale(0.95)';
        setTimeout(() => {
            document.getElementById('mainContainer').style.transform = 'scale(1)';
        }, 10);
    }

    /****************** 数据存储配置 ******************/
        // 本地存储键名配置
    const STORAGE_KEYS = {
            USERS: 'sky_users',       // 用户数据
            SMS_CODES: 'sky_sms_codes' // 短信验证码
        };

    // 初始化本地存储数据
    let users = JSON.parse(localStorage.getItem(STORAGE_KEYS.USERS)) || [];
    let smsCodes = JSON.parse(localStorage.getItem(STORAGE_KEYS.SMS_CODES)) || {};

    /****************** 工具函数 ******************/
    /**
     * 显示错误提示
     * @param {string} elementId - 错误消息容器的ID
     * @param {string} message - 要显示的错误信息
     */
    function showError(elementId, message) {
        const errorElement = document.getElementById(elementId);
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = 'block';
            // 关联输入框高亮
            const input = errorElement.previousElementSibling;
            if (input) {
                input.style.borderColor = '#ff6b6b';
                input.focus();
            }
        } else {
            console.error('错误元素未找到:', elementId, '错误信息:', message);
            alert(message); // 备用显示方式
        }
    }

    /**
     * 清除所有注册错误提示
     */
    function clearRegisterErrors() {
        document.querySelectorAll('#registerForm .error-message').forEach(el => {
            el.style.display = 'none';
            el.previousElementSibling.style.borderColor = '#e0f2f1';
        });
    }

    /****************** 短信验证模块 ******************/
    // 短信验证码发送功能
    document.getElementById('sendCodeBtn').addEventListener('click', function() {
        const phone = document.getElementById('registerPhone').value.trim();
        clearRegisterErrors();

        // 手机号格式验证（中国大陆）
        if (!/^1[3-9]\d{9}$/.test(phone)) {
            showError('registerPhoneError', '请输入有效的手机号码');
            return;
        }

        // 防重复发送检查（60秒间隔）
        if (smsCodes[phone] && Date.now() - smsCodes[phone].timestamp < 60000) {
            const remainTime = 60 - Math.floor((Date.now() - smsCodes[phone].timestamp)/1000);
            showError('registerPhoneError', `${remainTime}秒后可重新获取`);
            return;
        }

        // 生成6位随机验证码
        const code = Math.floor(100000 + Math.random() * 900000);

        // 存储验证码记录
        smsCodes[phone] = {
            code: code,
            timestamp: Date.now(),
            attempts: 0  // 错误尝试次数
        };
        localStorage.setItem(STORAGE_KEYS.SMS_CODES, JSON.stringify(smsCodes));

        // 启动60秒倒计时
        let seconds = 60;
        this.disabled = true;
        const timer = setInterval(() => {
            this.textContent = `${seconds}秒后重发`;
            if (--seconds < 0) {
                clearInterval(timer);
                this.disabled = false;
                this.textContent = '获取验证码';
            }
        }, 1000);

        // 开发环境打印验证码（生产环境需删除）
        console.log(`[开发模式] 短信验证码：${code}`);
    });

    /****************** 实时密码强度检测 ******************/
    document.getElementById('registerPassword').addEventListener('input', function(e) {
        const strengthBar = document.querySelector('.strength-bar');
        const pwd = e.target.value;

        let strength = 0;
        if (pwd.match(/[a-z]/)) strength++;  // 包含小写字母
        if (pwd.match(/[A-Z]/)) strength++;  // 包含大写字母
        if (pwd.match(/[0-9]/)) strength++;  // 包含数字
        if (pwd.match(/[^a-zA-Z0-9]/)) strength++; // 包含特殊字符

        // 根据强度设置样式
        strengthBar.className = 'strength-bar ' + (
            strength < 2 ? 'strength-weak' :
                strength < 4 ? 'strength-medium' : 'strength-strong'
        );
    });

    // 登录函数
    function login() {
        const username = document.getElementById('loginUsername').value;
        const password = document.getElementById('loginPassword').value;

        axios.post('/user/login', {
            userId: username,
            phone: username,
            password: password
        })
            .then(response => {
                const data = response.data;
                if (data.success) {
                    // 保存用户信息到本地存储
                    if (data.user) {
                        const userData = {
                            id: data.user.id,
                            userId: data.user.userId,
                            username: data.user.username,
                            phone: data.user.phone,
                            avatar: data.user.avatar || '/images/avatar/default-avatar.jpg',
                            signature: data.user.signature,
                            loginTime: Date.now()
                        };
                        localStorage.setItem('sky_current_user', JSON.stringify(userData));
                    }

                    if (data.redirectUrl) {
                        window.location.href = data.redirectUrl;
                    } else {
                        const successMessage = document.createElement('div');
                        successMessage.className = 'success-message';
                        successMessage.innerHTML = `
                            <i class="fas fa-check-circle"></i>
                            ${data.message}
                        `;
                        const submitButton = document.querySelector('#loginForm button');
                        submitButton.parentNode.insertBefore(successMessage, submitButton.nextSibling);
                        setTimeout(() => {
                            window.location.href = 'index.html';
                        }, 2000);
                    }
                } else {
                    showError('loginPasswordError', data.message);
                    const form = document.getElementById('loginForm');
                    form.style.animation = 'shake 0.4s';
                    setTimeout(() => form.style.animation = '', 400);
                }
            })
            .catch(error => {
                console.error('登录请求出错:', error);
                showError('loginPasswordError', '登录请求出错，请稍后重试');
            });
    }

    // 注册函数
    function register() {
        clearRegisterErrors();

        const username = document.getElementById('registerUsername').value.trim();
        const phone = document.getElementById('registerPhone').value.trim();
        const code = document.getElementById('smsCode').value.trim();
        const password = document.getElementById('registerPassword').value;
        const confirmPassword = document.getElementById('registerConfirmPassword').value;

        // 简单的客户端验证
        let isValid = true;
        if (username.length < 2 || username.length > 16) {
            showError('registerUsernameError', '用户名需2-16个字符');
            isValid = false;
        }
        const pwdRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,20}$/;
        if (!pwdRegex.test(password)) {
            showError('registerPasswordError', '需8-20位，包含大小写字母、数字和特殊字符');
            isValid = false;
        }
        if (password !== confirmPassword) {
            showError('registerConfirmPasswordError', '两次密码输入不一致');
            isValid = false;
        }
        if (!smsCodes[phone] || smsCodes[phone].code !== parseInt(code)) {
            showError('smsCodeError', '验证码错误');
            isValid = false;
        } else if (Date.now() - smsCodes[phone].timestamp > 300000) { // 5分钟有效期
            showError('smsCodeError', '验证码已过期');
            isValid = false;
        }

        if (isValid) {
            const newUser = {
                userId: 'U' + Date.now().toString(36),// 生成唯一的 userId
                username: username,
                phone: phone,
                password: password,
                registerTime: Date.now()
            };

            axios.post('/user/register', newUser)
                .then(response => {
                    const data = response.data;
                    if (data.success) {
                        const successMsg = document.createElement('div');
                        successMsg.className = 'success-message';
                        successMsg.innerHTML = `
                                <i class="fas fa-check-circle"></i>
                                ${data.message}
                            `;
                        const form = document.getElementById('registerForm');
                        form.appendChild(successMsg);
                        setTimeout(() => {
                            form.reset();
                            successMsg.remove();
                            showLogin();
                        }, 2000);
                    } else {
                        showError('registerPhoneError', data.message);
                    }
                })
                .catch(error => {
                    console.error('注册请求出错:', error);
                    showError('registerPhoneError', '注册请求出错，请稍后重试');
                });
        }
    }

    // 雪花生成函数
    function createSnowflakes(e) {
        const colors = [
            'hsl(200, 60%, 80%)',   // 淡蓝
            'hsl(260, 60%, 85%)',  // 浅紫
            'hsl(330, 60%, 85%)',  // 淡粉
            'hsl(170, 60%, 80%)',  // 薄荷绿
            'hsl(40, 60%, 90%)'    // 浅黄
        ];

        for(let i = 0; i < 10; i++) { // 每次点击生成10片雪花
            const snowflake = document.createElement('div');
            snowflake.className = 'snowflake';

            // 随机参数
            const size = Math.random() * 15 + 10; // 10-25px
            const delay = Math.random() * 0.5;
            const duration = Math.random() * 1.5 + 1; // 1-2.5秒
            const color = colors[Math.floor(Math.random() * colors.length)];
            const offsetX = (Math.random() - 0.5) * 40;
            const offsetY = (Math.random() - 0.5) * 40;

            // 应用样式
            snowflake.style.cssText = `
                    left: ${e.clientX + offsetX}px;
                    top: ${e.clientY + offsetY}px;
                    width: ${size}px;
                    height: ${size}px;
                    background: ${color};
                    animation-duration: ${duration}s;
                    animation-delay: ${delay}s;
                `;

            // 添加到页面并自动移除
            document.body.appendChild(snowflake);
            snowflake.addEventListener('animationend', () => {
                snowflake.remove();
            });
        }
    }

    // 绑定点击事件
    document.addEventListener('click', createSnowflakes);
</script>
</body>
</html>