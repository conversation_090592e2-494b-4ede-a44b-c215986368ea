<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin API接口测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            line-height: 1.6;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #667eea;
        }
        .header h1 {
            color: #333;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .test-section {
            margin: 25px 0;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
        }
        .test-section h3 {
            color: #667eea;
            margin-top: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .api-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .api-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
        }
        .api-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.15);
        }
        .api-card h4 {
            color: #667eea;
            margin-top: 0;
            font-size: 0.9rem;
        }
        .api-method {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-right: 8px;
        }
        .method-get { background: #10b981; color: white; }
        .method-post { background: #3b82f6; color: white; }
        .method-delete { background: #ef4444; color: white; }
        .test-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            margin: 5px 5px 5px 0;
        }
        .test-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
        }
        .result-area {
            background: #1e293b;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
            white-space: pre-wrap;
        }
        .status-success { color: #10b981; }
        .status-error { color: #ef4444; }
        .status-info { color: #3b82f6; }
        .clear-button {
            background: #6b7280;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Admin API接口测试工具</h1>
            <p style="color: #666;">测试新增的后端API接口功能</p>
        </div>

        <div class="test-section">
            <h3>📋 日志管理API</h3>
            <div class="api-grid">
                <div class="api-card">
                    <h4><span class="api-method method-get">GET</span>/api/admin/logs/stats</h4>
                    <p>获取日志统计信息</p>
                    <button class="test-button" onclick="testAPI('GET', '/api/admin/logs/stats')">测试</button>
                </div>
                <div class="api-card">
                    <h4><span class="api-method method-get">GET</span>/api/admin/logs</h4>
                    <p>获取日志列表（分页）</p>
                    <button class="test-button" onclick="testAPI('GET', '/api/admin/logs?page=1&size=5')">测试</button>
                </div>
                <div class="api-card">
                    <h4><span class="api-method method-post">POST</span>/api/admin/logs/clear</h4>
                    <p>清理旧日志</p>
                    <button class="test-button" onclick="testAPI('POST', '/api/admin/logs/clear')">测试</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>💾 备份管理API</h3>
            <div class="api-grid">
                <div class="api-card">
                    <h4><span class="api-method method-get">GET</span>/api/admin/backup/stats</h4>
                    <p>获取备份统计信息</p>
                    <button class="test-button" onclick="testAPI('GET', '/api/admin/backup/stats')">测试</button>
                </div>
                <div class="api-card">
                    <h4><span class="api-method method-get">GET</span>/api/admin/backup/list</h4>
                    <p>获取备份列表</p>
                    <button class="test-button" onclick="testAPI('GET', '/api/admin/backup/list?page=1&size=5')">测试</button>
                </div>
                <div class="api-card">
                    <h4><span class="api-method method-post">POST</span>/api/admin/backup/create</h4>
                    <p>创建手动备份</p>
                    <button class="test-button" onclick="testCreateBackup()">测试</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🛡️ 安全中心API</h3>
            <div class="api-grid">
                <div class="api-card">
                    <h4><span class="api-method method-get">GET</span>/api/admin/security/stats</h4>
                    <p>获取安全统计信息</p>
                    <button class="test-button" onclick="testAPI('GET', '/api/admin/security/stats')">测试</button>
                </div>
                <div class="api-card">
                    <h4><span class="api-method method-get">GET</span>/api/admin/security/logs</h4>
                    <p>获取安全日志</p>
                    <button class="test-button" onclick="testAPI('GET', '/api/admin/security/logs?page=1&size=5')">测试</button>
                </div>
                <div class="api-card">
                    <h4><span class="api-method method-get">GET</span>/api/admin/security/blacklist</h4>
                    <p>获取黑名单IP</p>
                    <button class="test-button" onclick="testAPI('GET', '/api/admin/security/blacklist')">测试</button>
                </div>
                <div class="api-card">
                    <h4><span class="api-method method-get">GET</span>/api/admin/security/whitelist</h4>
                    <p>获取白名单IP</p>
                    <button class="test-button" onclick="testAPI('GET', '/api/admin/security/whitelist')">测试</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>⚙️ 系统设置API</h3>
            <div class="api-grid">
                <div class="api-card">
                    <h4><span class="api-method method-get">GET</span>/api/admin/settings</h4>
                    <p>获取系统设置</p>
                    <button class="test-button" onclick="testAPI('GET', '/api/admin/settings')">测试</button>
                </div>
                <div class="api-card">
                    <h4><span class="api-method method-get">GET</span>/api/admin/settings/basic</h4>
                    <p>获取基本设置</p>
                    <button class="test-button" onclick="testAPI('GET', '/api/admin/settings/basic')">测试</button>
                </div>
                <div class="api-card">
                    <h4><span class="api-method method-post">POST</span>/api/admin/settings/save</h4>
                    <p>保存设置</p>
                    <button class="test-button" onclick="testSaveSettings()">测试</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 测试结果</h3>
            <button class="clear-button" onclick="clearResults()">清空结果</button>
            <div id="resultArea" class="result-area">等待测试结果...</div>
        </div>
    </div>

    <script>
        let resultArea = document.getElementById('resultArea');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = `status-${type}`;
            resultArea.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            resultArea.scrollTop = resultArea.scrollHeight;
        }

        function clearResults() {
            resultArea.innerHTML = '测试结果已清空...\n';
        }

        async function testAPI(method, url, data = null) {
            log(`🔄 测试 ${method} ${url}`, 'info');
            
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };

                if (data) {
                    options.body = JSON.stringify(data);
                }

                const response = await fetch(url, options);
                const result = await response.json();
                
                if (response.ok) {
                    log(`✅ 成功: ${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    log(`❌ 失败: ${response.status} - ${JSON.stringify(result, null, 2)}`, 'error');
                }
            } catch (error) {
                log(`❌ 错误: ${error.message}`, 'error');
            }
        }

        function testCreateBackup() {
            const data = {
                name: '测试备份_' + Date.now(),
                type: 'full'
            };
            testAPI('POST', '/api/admin/backup/create', data);
        }

        function testSaveSettings() {
            const data = {
                type: 'basic',
                data: {
                    siteName: '测试网站',
                    siteDescription: '这是一个测试描述'
                }
            };
            testAPI('POST', '/api/admin/settings/save', data);
        }

        // 页面加载完成后自动测试基础API
        window.addEventListener('load', function() {
            log('🚀 API测试工具已加载', 'info');
            log('💡 点击上方按钮测试对应的API接口', 'info');
            
            // 自动测试一些基础API
            setTimeout(() => {
                log('🔄 开始自动测试基础API...', 'info');
                testAPI('GET', '/api/admin/logs/stats');
            }, 1000);
            
            setTimeout(() => {
                testAPI('GET', '/api/admin/backup/stats');
            }, 2000);
            
            setTimeout(() => {
                testAPI('GET', '/api/admin/security/stats');
            }, 3000);
        });
    </script>
</body>
</html>
