package cn.gzsf.javawebspringboot.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 轮播图控制器
 */
@RestController
@RequestMapping("/carousel")
public class CarouselController {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 轮播图专用上传接口
     */
    @PostMapping("/upload")
    public Map<String, Object> uploadCarouselImage(@RequestParam("file") MultipartFile file) {
        Map<String, Object> result = new HashMap<>();

        try {
            if (file.isEmpty()) {
                result.put("success", false);
                result.put("message", "上传的文件为空");
                return result;
            }

            // 获取文件扩展名
            String originalFilename = file.getOriginalFilename();
            String fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));

            // 检查文件扩展名
            if (!(".jpg".equalsIgnoreCase(fileExtension) || ".png".equalsIgnoreCase(fileExtension) || ".jpeg".equalsIgnoreCase(fileExtension))) {
                result.put("success", false);
                result.put("message", "仅支持上传 .jpg、.jpeg 或 .png 格式的图片");
                return result;
            }

            // 检查文件大小（2MB）
            long maxFileSize = 2 * 1024 * 1024; // 2MB
            if (file.getSize() > maxFileSize) {
                result.put("success", false);
                result.put("message", "文件大小不能超过 2MB");
                return result;
            }

            // 生成唯一的文件名
            String uniqueFileName = UUID.randomUUID().toString() + fileExtension;

            // 获取项目根目录下的banner文件夹路径
            String currentDir = System.getProperty("user.dir");
            String bannerDir = currentDir + "/src/main/resources/static/images/banner";

            File targetDir = new File(bannerDir);
            if (!targetDir.exists()) {
                boolean created = targetDir.mkdirs();
                System.out.println("📁 创建轮播图目录: " + (created ? "成功" : "失败") + " - " + bannerDir);
            }

            // 保存文件
            File targetFile = new File(targetDir, uniqueFileName);
            file.transferTo(targetFile);

            String imageUrl = "/images/banner/" + uniqueFileName;

            result.put("success", true);
            result.put("imageUrl", imageUrl);
            result.put("message", "轮播图上传成功");

            System.out.println("🎠 轮播图上传成功: " + targetFile.getAbsolutePath());
            System.out.println("🔗 访问URL: " + imageUrl);

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "轮播图上传失败: " + e.getMessage());
            System.err.println("❌ 轮播图上传失败: " + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }
    
    /**
     * 获取轮播图列表（管理端用，包含所有状态）
     */
    @GetMapping("/images")
    public Map<String, Object> getCarouselImages() {
        Map<String, Object> result = new HashMap<>();
        try {
            String sql = "SELECT ci.id, ci.name, ci.image_url, ci.title, ci.description, " +
                        "ci.sort_order, ci.category_id, ci.is_active, " +
                        "c.name as category_name " +
                        "FROM carousel_images ci " +
                        "LEFT JOIN categories c ON ci.category_id = c.id " +
                        "WHERE ci.name = '轮播图' " +
                        "ORDER BY ci.sort_order ASC, ci.id ASC";

            List<Map<String, Object>> images = jdbcTemplate.queryForList(sql);

            result.put("success", true);
            result.put("data", images);
            result.put("count", images.size());
            result.put("message", "获取轮播图成功");

            System.out.println("📋 获取轮播图列表成功，共 " + images.size() + " 张");

        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("message", "获取轮播图失败");
            System.err.println("❌ 获取轮播图失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取前端轮播图列表（仅启用状态）
     */
    @GetMapping("/active-images")
    public Map<String, Object> getActiveCarouselImages() {
        Map<String, Object> result = new HashMap<>();
        try {
            String sql = "SELECT ci.id, ci.name, ci.image_url, ci.title, ci.description, " +
                        "ci.sort_order, ci.category_id, c.name as category_name " +
                        "FROM carousel_images ci " +
                        "LEFT JOIN categories c ON ci.category_id = c.id " +
                        "WHERE ci.name = '轮播图' AND ci.is_active = 1 " +
                        "ORDER BY ci.sort_order ASC, ci.id ASC";

            List<Map<String, Object>> images = jdbcTemplate.queryForList(sql);

            result.put("success", true);
            result.put("data", images);
            result.put("count", images.size());
            result.put("message", "获取轮播图成功");

        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("message", "获取轮播图失败");
        }
        return result;
    }

    /**
     * 添加轮播图
     */
    @PostMapping("/add")
    public Map<String, Object> addCarouselImage(@RequestBody Map<String, Object> carouselData) {
        Map<String, Object> result = new HashMap<>();
        try {
            String name = (String) carouselData.get("name");
            String imageUrl = (String) carouselData.get("imageUrl");
            String title = (String) carouselData.get("title");
            String description = (String) carouselData.get("description");
            Integer sortOrder = (Integer) carouselData.get("sortOrder");
            Long categoryId = carouselData.get("categoryId") != null ?
                            Long.valueOf(carouselData.get("categoryId").toString()) : null;

            if (name == null || imageUrl == null) {
                result.put("success", false);
                result.put("message", "轮播图名称和图片URL不能为空");
                return result;
            }

            String insertSql = "INSERT INTO carousel_images (name, image_url, title, description, sort_order, category_id, is_active, created_time, updated_time) " +
                             "VALUES (?, ?, ?, ?, ?, ?, 1, ?, ?)";

            long currentTime = System.currentTimeMillis();
            jdbcTemplate.update(insertSql, name, imageUrl, title, description,
                              sortOrder != null ? sortOrder : 0, categoryId, currentTime, currentTime);

            result.put("success", true);
            result.put("message", "轮播图添加成功");

            System.out.println("✅ 轮播图添加成功: " + title + " - " + imageUrl);

        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "添加轮播图失败: " + e.getMessage());
            System.err.println("❌ 添加轮播图失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 更新轮播图
     */
    @PutMapping("/update")
    public Map<String, Object> updateCarouselImage(@RequestBody Map<String, Object> carouselData) {
        Map<String, Object> result = new HashMap<>();
        try {
            Long id = Long.valueOf(carouselData.get("id").toString());
            String name = (String) carouselData.get("name");
            String imageUrl = (String) carouselData.get("imageUrl");
            String title = (String) carouselData.get("title");
            String description = (String) carouselData.get("description");
            Integer sortOrder = (Integer) carouselData.get("sortOrder");
            Long categoryId = carouselData.get("categoryId") != null ?
                            Long.valueOf(carouselData.get("categoryId").toString()) : null;

            if (id == null || name == null || imageUrl == null) {
                result.put("success", false);
                result.put("message", "ID、轮播图名称和图片URL不能为空");
                return result;
            }

            String updateSql = "UPDATE carousel_images SET name = ?, image_url = ?, title = ?, description = ?, " +
                             "sort_order = ?, category_id = ?, updated_time = ? WHERE id = ?";

            long currentTime = System.currentTimeMillis();
            int updatedRows = jdbcTemplate.update(updateSql, name, imageUrl, title, description,
                                                sortOrder != null ? sortOrder : 0, categoryId, currentTime, id);

            if (updatedRows > 0) {
                result.put("success", true);
                result.put("message", "轮播图更新成功");
                System.out.println("✅ 轮播图更新成功: ID=" + id + ", " + title);
            } else {
                result.put("success", false);
                result.put("message", "轮播图不存在或更新失败");
            }

        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "更新轮播图失败: " + e.getMessage());
            System.err.println("❌ 更新轮播图失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 删除轮播图
     */
    @DeleteMapping("/delete/{id}")
    public Map<String, Object> deleteCarouselImage(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            String deleteSql = "DELETE FROM carousel_images WHERE id = ?";
            int deletedRows = jdbcTemplate.update(deleteSql, id);

            if (deletedRows > 0) {
                result.put("success", true);
                result.put("message", "轮播图删除成功");
                System.out.println("✅ 轮播图删除成功: ID=" + id);
            } else {
                result.put("success", false);
                result.put("message", "轮播图不存在或删除失败");
            }

        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "删除轮播图失败: " + e.getMessage());
            System.err.println("❌ 删除轮播图失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 切换轮播图状态
     */
    @PutMapping("/toggle-status/{id}")
    public Map<String, Object> toggleCarouselStatus(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 先获取当前状态
            String getCurrentStatusSql = "SELECT is_active FROM carousel_images WHERE id = ?";
            Boolean currentStatus = jdbcTemplate.queryForObject(getCurrentStatusSql, Boolean.class, id);

            if (currentStatus == null) {
                result.put("success", false);
                result.put("message", "轮播图不存在");
                return result;
            }

            // 切换状态
            boolean newStatus = !currentStatus;
            String updateStatusSql = "UPDATE carousel_images SET is_active = ?, updated_time = ? WHERE id = ?";
            long currentTime = System.currentTimeMillis();
            int updatedRows = jdbcTemplate.update(updateStatusSql, newStatus, currentTime, id);

            if (updatedRows > 0) {
                result.put("success", true);
                result.put("message", "轮播图状态切换成功");
                result.put("newStatus", newStatus);
                System.out.println("✅ 轮播图状态切换成功: ID=" + id + ", 新状态=" + (newStatus ? "启用" : "禁用"));
            } else {
                result.put("success", false);
                result.put("message", "状态切换失败");
            }

        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "切换轮播图状态失败: " + e.getMessage());
            System.err.println("❌ 切换轮播图状态失败: " + e.getMessage());
        }
        return result;
    }
    
    /**
     * 根据分类获取产品列表（用于首页分类展示）
     */
    @GetMapping("/category/{categoryId}/products")
    public Map<String, Object> getProductsByCategory(@PathVariable Long categoryId,
                                                    @RequestParam(defaultValue = "1") int page,
                                                    @RequestParam(defaultValue = "8") int size) {
        Map<String, Object> result = new HashMap<>();
        try {
            int offset = (page - 1) * size;
            
            // 获取分类信息
            String categorySql = "SELECT id, name, description FROM categories WHERE id = ?";
            List<Map<String, Object>> categoryInfo = jdbcTemplate.queryForList(categorySql, categoryId);
            
            // 获取该分类下的产品
            String productsSql = "SELECT p.id, p.name, p.description, p.price, p.stock, p.image_url, p.is_new " +
                                "FROM products p " +
                                "JOIN product_category pc ON p.id = pc.product_id " +
                                "WHERE pc.category_id = ? " +
                                "ORDER BY p.id DESC " +
                                "LIMIT ?, ?";
            
            List<Map<String, Object>> products = jdbcTemplate.queryForList(productsSql, categoryId, offset, size);
            
            // 获取总数
            String countSql = "SELECT COUNT(*) FROM products p " +
                             "JOIN product_category pc ON p.id = pc.product_id " +
                             "WHERE pc.category_id = ?";
            int total = jdbcTemplate.queryForObject(countSql, Integer.class, categoryId);
            
            result.put("success", true);
            result.put("category", categoryInfo.isEmpty() ? null : categoryInfo.get(0));
            result.put("products", products);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
            result.put("totalPages", (int) Math.ceil((double) total / size));
            
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取所有分类及其产品数量
     */
    @GetMapping("/categories")
    public Map<String, Object> getCategoriesWithProductCount() {
        Map<String, Object> result = new HashMap<>();
        try {
            String sql = "SELECT c.id, c.name, c.description, c.sort_order, " +
                        "COUNT(pc.product_id) as product_count " +
                        "FROM categories c " +
                        "LEFT JOIN product_category pc ON c.id = pc.category_id " +
                        "WHERE c.is_active = 1 " +
                        "GROUP BY c.id, c.name, c.description, c.sort_order " +
                        "ORDER BY c.sort_order ASC, c.id ASC";
            
            List<Map<String, Object>> categories = jdbcTemplate.queryForList(sql);
            
            result.put("success", true);
            result.put("data", categories);
            result.put("count", categories.size());
            
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取首页数据（轮播图 + 分类产品）
     */
    @GetMapping("/home-data")
    public Map<String, Object> getHomeData() {
        Map<String, Object> result = new HashMap<>();
        try {
            // 获取轮播图
            String carouselSql = "SELECT ci.id, ci.image_url, ci.title, ci.description, " +
                               "ci.category_id, c.name as category_name " +
                               "FROM carousel_images ci " +
                               "LEFT JOIN categories c ON ci.category_id = c.id " +
                               "WHERE ci.name = '轮播图' AND ci.is_active = 1 " +
                               "ORDER BY ci.sort_order ASC LIMIT 5";
            
            List<Map<String, Object>> carouselImages = jdbcTemplate.queryForList(carouselSql);
            
            // 获取分类及每个分类的热门产品
            String categoriesSql = "SELECT id, name, description FROM categories WHERE is_active = 1 ORDER BY sort_order ASC LIMIT 6";
            List<Map<String, Object>> categories = jdbcTemplate.queryForList(categoriesSql);
            
            // 为每个分类获取热门产品
            for (Map<String, Object> category : categories) {
                Long categoryId = ((Number) category.get("id")).longValue();
                String productsSql = "SELECT p.id, p.name, p.price, p.image_url " +
                                   "FROM products p " +
                                   "JOIN product_category pc ON p.id = pc.product_id " +
                                   "WHERE pc.category_id = ? " +
                                   "ORDER BY p.id DESC LIMIT 4";
                
                List<Map<String, Object>> products = jdbcTemplate.queryForList(productsSql, categoryId);
                category.put("products", products);
            }
            
            result.put("success", true);
            result.put("carouselImages", carouselImages);
            result.put("categories", categories);
            result.put("message", "获取首页数据成功");
            
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("message", "获取首页数据失败");
        }
        return result;
    }
}
