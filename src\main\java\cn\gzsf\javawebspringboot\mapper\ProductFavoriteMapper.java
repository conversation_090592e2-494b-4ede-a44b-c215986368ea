package cn.gzsf.javawebspringboot.mapper;

import cn.gzsf.javawebspringboot.entity.ProductFavorite;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品收藏Mapper接口
 */
@Mapper
public interface ProductFavoriteMapper {
    
    /**
     * 添加收藏
     */
    int addFavorite(ProductFavorite favorite);
    
    /**
     * 取消收藏
     */
    int removeFavorite(@Param("userPhone") String userPhone, @Param("productId") Long productId);
    
    /**
     * 检查是否已收藏
     */
    ProductFavorite checkFavorite(@Param("userPhone") String userPhone, @Param("productId") Long productId);
    
    /**
     * 获取用户收藏列表
     */
    List<ProductFavorite> getUserFavorites(@Param("userPhone") String userPhone, 
                                         @Param("offset") int offset, 
                                         @Param("size") int size);
    
    /**
     * 获取用户收藏总数
     */
    int getUserFavoriteCount(@Param("userPhone") String userPhone);
    
    /**
     * 获取产品收藏总数
     */
    int getProductFavoriteCount(@Param("productId") Long productId);
    
    /**
     * 获取热门收藏产品
     */
    List<ProductFavorite> getPopularFavorites(@Param("limit") int limit);
    
    /**
     * 获取所有收藏记录（管理员用）
     */
    List<ProductFavorite> getAllFavorites(@Param("offset") int offset, @Param("size") int size);
    
    /**
     * 获取收藏总数（管理员用）
     */
    int getTotalFavoriteCount();

    /**
     * 获取今日新增收藏数
     */
    int getTodayFavoriteCount();

    /**
     * 获取最多收藏的商品收藏数
     */
    int getMaxProductFavoriteCount();

    /**
     * 获取活跃收藏用户数
     */
    int getActiveFavoriteUserCount();

    /**
     * 根据ID删除收藏记录
     */
    int deleteFavoriteById(@Param("id") Long id);
}
