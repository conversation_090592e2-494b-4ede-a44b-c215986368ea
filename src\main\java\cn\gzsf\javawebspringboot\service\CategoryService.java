package cn.gzsf.javawebspringboot.service;

import cn.gzsf.javawebspringboot.entity.Category;

import java.util.List;

public interface CategoryService {
    // 获取所有分类
    List<Category> getAllCategories();
    // 根据 ID 获取分类
    Category getCategoryById(Long id);
    // 新增分类
    boolean addCategory(Category category);
    // 更新分类信息
    boolean updateCategory(Category category);
    // 删除分类
    boolean deleteCategory(Long id);

    // 分页获取分类列表
    List<Category> getCategoriesPage(int offset, int size);

    // 获取分类总数
    int getTotalCategoryCount();
}