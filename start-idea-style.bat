@echo off
echo ========================================
echo    启动SpringBoot项目 (IDEA风格)
echo ========================================
echo.

REM 设置编码
chcp 65001 > nul

REM 设置Java环境变量
set JAVA_HOME=C:\Program Files\Java\jdk1.8.0_202
set PATH=%JAVA_HOME%\bin;%PATH%

REM 设置项目路径
set PROJECT_DIR=%~dp0
cd /d "%PROJECT_DIR%"

echo 🔧 项目目录: %PROJECT_DIR%
echo 🔧 Java版本: 
java -version
echo.

REM 检查target/classes目录
if not exist "target\classes" (
    echo ❌ target\classes目录不存在，开始编译...
    echo.
    mvn compile
    if errorlevel 1 (
        echo ❌ 编译失败！请确保已安装Maven并配置环境变量
        pause
        exit /b 1
    )
)

echo ✅ 编译完成，开始启动应用程序...
echo.

REM 设置JVM参数（模拟IDEA的设置）
set JVM_OPTS=-Dfile.encoding=UTF-8
set JVM_OPTS=%JVM_OPTS% -Dspring.profiles.active=dev
set JVM_OPTS=%JVM_OPTS% -Dspring.output.ansi.enabled=always
set JVM_OPTS=%JVM_OPTS% -Dserver.port=8082
set JVM_OPTS=%JVM_OPTS% -Xms256m -Xmx512m

REM 设置类路径（包含编译后的类和依赖）
set CLASSPATH=target\classes
for %%i in (target\dependency\*.jar) do set CLASSPATH=!CLASSPATH!;%%i

echo 🚀 启动参数:
echo    JVM参数: %JVM_OPTS%
echo    主类: cn.gzsf.javawebspringboot.JavaWebSpringBootApplication
echo    端口: 8082
echo.

echo 🌟 应用程序启动中...
echo    访问地址: http://localhost:8082
echo    按 Ctrl+C 停止应用程序
echo.

REM 启动应用程序
java %JVM_OPTS% -cp "%CLASSPATH%" cn.gzsf.javawebspringboot.JavaWebSpringBootApplication

echo.
echo 应用程序已停止
pause
