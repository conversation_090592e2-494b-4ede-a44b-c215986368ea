package cn.gzsf.javawebspringboot.controller;

import cn.gzsf.javawebspringboot.dto.UserDetailDTO;
import cn.gzsf.javawebspringboot.entity.*;
import cn.gzsf.javawebspringboot.service.UserDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户详情控制器
 */
@RestController
@RequestMapping("/api/user-detail")
@CrossOrigin(origins = "*")
public class UserDetailController {
    
    @Autowired
    private UserDetailService userDetailService;
    
    /**
     * 根据手机号获取用户详情
     */
    @GetMapping("/{phone}")
    public Map<String, Object> getUserDetail(@PathVariable String phone) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            UserDetailDTO userDetail = userDetailService.getUserDetailByPhone(phone);
            
            if (userDetail != null) {
                result.put("success", true);
                result.put("data", userDetail);
            } else {
                result.put("success", false);
                result.put("message", "用户不存在");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取用户详情失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 更新用户详情信息
     */
    @PostMapping("/update")
    public Map<String, Object> updateUserDetail(@RequestBody UserDetail userDetail) {
        Map<String, Object> result = new HashMap<>();

        try {
            boolean success = userDetailService.saveUserDetail(userDetail);

            if (success) {
                result.put("success", true);
                result.put("message", "更新成功");
            } else {
                result.put("success", false);
                result.put("message", "更新失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "更新用户详情失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 只更新用户签名
     */
    @PostMapping("/update-signature")
    public Map<String, Object> updateUserSignature(@RequestBody Map<String, String> request) {
        Map<String, Object> result = new HashMap<>();

        try {
            String phone = request.get("phone");
            String signature = request.get("signature");

            if (phone == null || phone.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "用户手机号不能为空");
                return result;
            }

            boolean success = userDetailService.updateUserSignature(phone, signature);

            if (success) {
                result.put("success", true);
                result.put("message", "签名更新成功");
                System.out.println("✅ 用户签名更新成功: " + phone + " -> " + signature);
            } else {
                result.put("success", false);
                result.put("message", "签名更新失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "更新用户签名失败: " + e.getMessage());
        }

        return result;
    }
    
    /**
     * 获取用户地址列表
     */
    @GetMapping("/{phone}/addresses")
    public Map<String, Object> getUserAddresses(@PathVariable String phone) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<UserAddress> addresses = userDetailService.getUserAddresses(phone);
            result.put("success", true);
            result.put("data", addresses);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取地址列表失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 添加用户地址
     */
    @PostMapping("/{phone}/addresses")
    public Map<String, Object> addUserAddress(@PathVariable String phone, @RequestBody UserAddress address) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            address.setUserPhone(phone);
            boolean success = userDetailService.addUserAddress(address);
            
            if (success) {
                result.put("success", true);
                result.put("message", "添加地址成功");
            } else {
                result.put("success", false);
                result.put("message", "添加地址失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "添加地址失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取用户购物车信息
     */
    @GetMapping("/{phone}/cart")
    public Map<String, Object> getUserCart(@PathVariable String phone) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<ShoppingCart> cartItems = userDetailService.getCartItems(phone);
            Integer cartItemCount = userDetailService.getCartItemCount(phone);
            
            result.put("success", true);
            result.put("data", cartItems);
            result.put("count", cartItemCount);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取购物车信息失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取用户订单信息
     */
    @GetMapping("/{phone}/orders")
    public Map<String, Object> getUserOrders(@PathVariable String phone, 
                                           @RequestParam(defaultValue = "10") int limit) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<UserOrder> orders = userDetailService.getRecentOrders(phone, limit);
            Integer totalCount = userDetailService.getTotalOrderCount(phone);
            Integer pendingCount = userDetailService.getPendingOrderCount(phone);
            
            Map<String, Object> data = new HashMap<>();
            data.put("orders", orders);
            data.put("totalCount", totalCount);
            data.put("pendingCount", pendingCount);
            
            result.put("success", true);
            result.put("data", data);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取订单信息失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取用户好友信息
     */
    @GetMapping("/{phone}/friends")
    public Map<String, Object> getUserFriends(@PathVariable String phone) {
        Map<String, Object> result = new HashMap<>();

        try {
            List<UserFriend> friends = userDetailService.getFriends(phone);
            Integer friendCount = userDetailService.getFriendCount(phone);

            result.put("success", true);
            result.put("data", friends);
            result.put("count", friendCount);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取好友信息失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 获取用户统计数据
     */
    @GetMapping("/stats")
    public Map<String, Object> getUserStats() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取用户统计数据
            Map<String, Object> stats = userDetailService.getUserStats();

            result.put("success", true);
            result.put("data", stats);
            result.put("message", "获取用户统计成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取用户统计失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 获取用户余额信息
     */
    @GetMapping("/{phone}/balance")
    public Map<String, Object> getUserBalance(@PathVariable String phone) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 这里可以从数据库获取用户余额信息
            // 目前返回模拟数据
            Map<String, Object> balanceData = new HashMap<>();
            balanceData.put("balance", 0.00);
            balanceData.put("transactions", new ArrayList<>());

            result.put("success", true);
            result.put("data", balanceData);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取余额信息失败: " + e.getMessage());
        }

        return result;
    }
}
