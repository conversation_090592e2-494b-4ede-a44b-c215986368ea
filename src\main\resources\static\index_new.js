// 轮播图管理类
class CarouselManager {
    constructor() {
        this.carouselContainer = document.getElementById('carouselContainer');
        this.carouselDots = document.getElementById('carouselDots');
        this.currentIndex = 0;
        this.carouselData = [];
        this.autoPlayInterval = null;

        // 加载轮播图数据
        this.loadCarouselData();
    }

    // 加载轮播图数据
    loadCarouselData() {
        axios.get('/carousel/active-images')
            .then(response => {
                console.log('🎠 轮播图数据响应:', response.data);
                if (response.data.success && response.data.data.length > 0) {
                    this.carouselData = response.data.data;
                    this.renderCarousel();
                    this.startAutoPlay();
                } else {
                    console.log('📋 使用默认轮播图');
                    this.useDefaultCarousel();
                }
            })
            .catch(error => {
                console.error('❌ 加载轮播图失败:', error);
                this.useDefaultCarousel();
            });
    }

    // 使用默认轮播图
    useDefaultCarousel() {
        this.carouselData = [
            {
                image_url: '/images/074dc74f-ab1e-4241-ad36-40a3dae3234a.jpg',
                title: '息壤天然植萃',
                description: '源自大地的纯净力量，滋养肌肤本真之美'
            },
            {
                image_url: '/images/496ea50b-83b6-438b-8493-2e49911b089e.jpg',
                title: '匠心臻选好物',
                description: '每一款产品都经过严格筛选，只为呈现最佳品质'
            },
            {
                image_url: '/images/acd8ab1a-4e42-41c5-bca6-21d886257b1c.jpg',
                title: '自然美肌之道',
                description: '遵循自然规律，让肌肤回归最初的健康状态'
            }
        ];
        this.renderCarousel();
        this.startAutoPlay();
    }

    // 渲染轮播图
    renderCarousel() {
        if (!this.carouselData || this.carouselData.length === 0) {
            return;
        }

        // 清空容器
        this.carouselContainer.innerHTML = '';
        this.carouselDots.innerHTML = '';

        // 创建轮播图项
        this.carouselData.forEach((item, index) => {
            // 创建轮播图项
            const carouselItem = document.createElement('div');
            carouselItem.className = `carousel-item ${index === 0 ? 'active' : ''}`;
            carouselItem.style.backgroundImage = `url('${item.image_url}')`;

            carouselItem.innerHTML = `
                <div class="carousel-caption">
                    <h2>${item.title}</h2>
                    <p>${item.description}</p>
                </div>
            `;

            this.carouselContainer.appendChild(carouselItem);

            // 创建指示点
            const dot = document.createElement('span');
            dot.className = `carousel-dot ${index === 0 ? 'active' : ''}`;
            dot.addEventListener('click', () => this.goToSlide(index));
            this.carouselDots.appendChild(dot);
        });

        console.log(`🎠 轮播图渲染完成，共 ${this.carouselData.length} 张`);
    }

    // 跳转到指定幻灯片
    goToSlide(index) {
        if (index < 0 || index >= this.carouselData.length) return;

        // 移除当前活动状态
        const currentItem = this.carouselContainer.querySelector('.carousel-item.active');
        const currentDot = this.carouselDots.querySelector('.carousel-dot.active');

        if (currentItem) currentItem.classList.remove('active');
        if (currentDot) currentDot.classList.remove('active');

        // 设置新的活动状态
        const newItem = this.carouselContainer.children[index];
        const newDot = this.carouselDots.children[index];

        if (newItem) newItem.classList.add('active');
        if (newDot) newDot.classList.add('active');

        this.currentIndex = index;
    }

    // 下一张
    nextSlide() {
        const nextIndex = (this.currentIndex + 1) % this.carouselData.length;
        this.goToSlide(nextIndex);
    }

    // 上一张
    prevSlide() {
        const prevIndex = (this.currentIndex - 1 + this.carouselData.length) % this.carouselData.length;
        this.goToSlide(prevIndex);
    }

    // 开始自动播放
    startAutoPlay() {
        this.stopAutoPlay(); // 先停止之前的自动播放
        this.autoPlayInterval = setInterval(() => {
            this.nextSlide();
        }, 5000); // 每5秒切换一次
    }

    // 停止自动播放
    stopAutoPlay() {
        if (this.autoPlayInterval) {
            clearInterval(this.autoPlayInterval);
            this.autoPlayInterval = null;
        }
    }

    // 销毁轮播图
    destroy() {
        this.stopAutoPlay();
    }
}

// 搜索功能类
class SearchManager {
    constructor() {
        console.log('🔧 SearchManager 构造函数开始执行...');

        // 初始化元素
        this.searchInput = document.getElementById('searchInput');
        this.searchBtn = document.getElementById('searchBtn');
        this.suggestions = document.getElementById('suggestions');
        this.productGrid = document.querySelector('.product-grid');
        this.categoryFilter = document.querySelector('.category-filter');
        this.dynamicProducts = document.querySelector('.dynamic-products');

        console.log('🔧 DOM元素检查:');
        console.log('  - productGrid:', this.productGrid ? '✅' : '❌');
        console.log('  - categoryFilter:', this.categoryFilter ? '✅' : '❌');
        console.log('  - dynamicProducts:', this.dynamicProducts ? '✅' : '❌');

        // 当前分类
        this.currentCategory = 'all';

        // 初始化事件
        this.initEvents();

        // 加载分类数据
        this.loadCategories();

        console.log('🔧 SearchManager 构造函数执行完成');
    }

    // 初始化事件监听
    initEvents() {
        // 输入实时搜索
        this.searchInput.addEventListener('input', (e) => {
            const keyword = e.target.value.trim();
            if (keyword) {
                this.showSuggestions(keyword);
            } else {
                this.hideSuggestions();
                this.loadAllProducts();
            }
        });

        // 搜索按钮点击
        this.searchBtn.addEventListener('click', () => this.executeSearch());

        // 回车搜索
        this.searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.executeSearch();
        });

        // 建议项点击
        this.suggestions.addEventListener('click', (e) => {
            if (e.target.classList.contains('suggestion-item')) {
                this.fillInput(e.target.textContent);
                this.executeSearch();
            }
        });

        // 分类筛选点击事件
        this.categoryFilter.addEventListener('click', (e) => {
            if (e.target.closest('.category-item')) {
                this.handleCategoryClick(e.target.closest('.category-item'));
            }
        });

        // 页面DOM加载完成后立即加载新品
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.loadNewProducts(); // 加载新品到息壤臻选区域
            });
        } else {
            // 如果DOM已经加载完成，立即执行
            this.loadNewProducts();
        }
    }

    // 加载分类数据
    loadCategories() {
        console.log('🚀 开始加载分类数据...');

        // 临时：强制使用默认分类进行测试
        if (window.location.search.includes('useDefault=true')) {
            console.log('🔧 强制使用默认分类（测试模式）');
            this.useDefaultCategories();
            return;
        }

        axios.get('/admin/category/frontend')
            .then(response => {
                console.log('📡 分类API响应:', response);
                console.log('📊 分类API数据:', response.data);

                // 检查响应数据的结构
                if (response.data && response.data.success && Array.isArray(response.data.data)) {
                    this.categoriesData = response.data.data;
                    console.log('✅ 分类数据保存成功:', this.categoriesData);

                    // 如果分类数据为空，使用默认分类
                    if (this.categoriesData.length === 0) {
                        console.log('⚠️ 分类数据为空，使用默认分类');
                        this.useDefaultCategories();
                        return;
                    }

                    this.renderCategories(this.categoriesData);
                } else {
                    console.error('❌ 分类数据格式不正确:', response.data);
                    console.log('🔄 回退到默认分类');
                    this.useDefaultCategories();
                }
            })
            .catch(error => {
                console.error('❌ 加载分类失败:', error);
                // 如果分类加载失败，使用默认分类
                this.useDefaultCategories();
            });
    }

    // 渲染分类
    renderCategories(categories) {
        const categoryList = this.categoryFilter.querySelector('.category-list');
        categoryList.innerHTML = `
            <li class="category-item active" data-category="all" data-category-id="0">
                <i class="fas fa-star"></i>
                <span>全部</span>
            </li>
        `;

        console.log('🔍 开始渲染分类，分类数据:', categories);

        categories.forEach((category, index) => {
            console.log(`📝 处理分类 ${index + 1}:`, category);

            const categoryItem = document.createElement('li');
            categoryItem.className = 'category-item';

            // 确保分类名称和ID都存在
            const categoryName = category.name || `分类${index + 1}`;
            const categoryId = category.id || (index + 1);

            categoryItem.setAttribute('data-category', categoryName);
            categoryItem.setAttribute('data-category-id', categoryId);

            categoryItem.innerHTML = `
                <i class="fas fa-spa"></i>
                <span>${categoryName}</span>
            `;

            categoryList.appendChild(categoryItem);

            console.log(`✅ 渲染分类: ${categoryName} (ID: ${categoryId})`);

            // 验证属性是否正确设置
            const verifyId = categoryItem.getAttribute('data-category-id');
            console.log(`🔍 验证分类 "${categoryName}" 的ID属性: ${verifyId}`);
        });

        console.log(`✅ 分类渲染完成，共 ${categories.length} 个分类`);

        // 验证所有渲染的分类元素
        const allCategoryItems = categoryList.querySelectorAll('.category-item');
        console.log('🔍 验证所有分类元素:');
        allCategoryItems.forEach((item, index) => {
            const name = item.getAttribute('data-category');
            const id = item.getAttribute('data-category-id');
            console.log(`  ${index + 1}. ${name} (ID: ${id})`);
        });
    }

    // 使用默认分类（当后端分类接口不可用时）
    useDefaultCategories() {
        console.log('⚠️ 使用默认分类数据');

        // 根据您的实际分类名称创建默认数据
        const defaultCategories = [
            { id: 1, name: '柔雾集', icon: 'fas fa-spa' },
            { id: 2, name: '浅草藏', icon: 'fas fa-paint-brush' },
            { id: 3, name: '凛风匣', icon: 'fas fa-wine-bottle' },
            { id: 4, name: '云栖盒', icon: 'fas fa-toolbox' },
            { id: 5, name: '汲光瓶', icon: 'fas fa-flask' }
        ];

        this.categoriesData = defaultCategories;
        console.log('📝 设置默认分类数据:', this.categoriesData);
        this.renderCategories(defaultCategories);
    }

    // 处理分类点击
    handleCategoryClick(categoryItem) {
        // 移除所有active类
        document.querySelectorAll('.category-item').forEach(item => {
            item.classList.remove('active');
        });

        // 添加active类到当前点击的分类
        categoryItem.classList.add('active');

        // 获取分类名称和ID
        this.currentCategory = categoryItem.getAttribute('data-category');
        let categoryId = categoryItem.getAttribute('data-category-id');

        // 调试：检查元素的所有属性
        console.log('🔍 点击的元素:', categoryItem);
        console.log('🔍 元素的所有属性:', categoryItem.attributes);
        console.log('🔍 data-category:', categoryItem.getAttribute('data-category'));
        console.log('🔍 data-category-id:', categoryItem.getAttribute('data-category-id'));

        // 如果没有获取到categoryId，尝试从分类数据中查找
        if (!categoryId || categoryId === 'null' || categoryId === 'undefined') {
            console.log('⚠️ 没有获取到categoryId，尝试从分类数据中查找');
            if (this.categoriesData && this.currentCategory !== 'all') {
                const foundCategory = this.categoriesData.find(cat => cat.name === this.currentCategory);
                if (foundCategory) {
                    categoryId = foundCategory.id;
                    console.log(`✅ 从分类数据中找到ID: ${categoryId}`);
                } else {
                    console.log(`❌ 在分类数据中未找到 "${this.currentCategory}"`);
                    categoryId = 0;
                }
            } else {
                categoryId = 0;
            }
        }

        // 确保categoryId是数字类型
        if (categoryId) {
            categoryId = parseInt(categoryId);
        } else {
            categoryId = 0;
        }

        console.log(`🎯 最终使用的分类: ${this.currentCategory}, ID: ${categoryId} (类型: ${typeof categoryId})`);

        // 根据分类ID加载产品
        this.loadProductsByCategory(this.currentCategory, categoryId);
    }

    // 根据分类加载产品
    loadProductsByCategory(category, categoryId = null) {
        // 如果是"全部"分类，使用ID 0
        if (category === 'all') {
            categoryId = 0;
        }

        // 如果没有传入categoryId，尝试从分类数据中查找
        if (categoryId === null || categoryId === 0 || categoryId === '0') {
            if (this.categoriesData && category !== 'all') {
                const foundCategory = this.categoriesData.find(cat => cat.name === category);
                if (foundCategory) {
                    categoryId = foundCategory.id;
                    console.log(`✅ 从分类数据中找到ID: ${category} -> ${categoryId}`);
                } else {
                    console.log(`❌ 在分类数据中未找到 "${category}"，使用默认ID: 0`);
                    categoryId = 0;
                }
            } else if (category === 'all') {
                categoryId = 0;
            } else {
                categoryId = 0;
            }
        }

        console.log(`🔍 加载分类产品: ${category} (ID: ${categoryId})`);
        console.log(`📤 发送请求: /products/category/${categoryId}`);

        // 调用分类产品接口
        axios.get(`/products/category/${categoryId}`)
            .then(response => {
                console.log(`📥 分类产品API响应:`, response.data);
                console.log(`📊 响应状态: ${response.data.success}`);
                console.log(`📊 产品数量: ${response.data.data ? response.data.data.length : 'undefined'}`);

                if (response.data.success) {
                    const products = response.data.data || [];
                    console.log(`📂 分类 "${category}" 返回的产品:`, products);

                    this.renderProducts(products);
                    console.log(`✅ 分类 "${category}" 加载了 ${products.length} 个产品`);

                    // 如果没有产品，显示提示信息
                    if (products.length === 0) {
                        console.log(`⚠️ 分类 "${category}" 没有产品，显示提示信息`);
                        this.showNoProductsMessage(`暂无"${category}"分类的产品`);
                    }
                } else {
                    console.error('❌ 获取分类产品失败:', response.data.message);
                    this.showErrorMessage('获取分类产品失败: ' + (response.data.message || ''));
                }
            })
            .catch(error => {
                console.error('❌ 按分类加载产品网络错误:', error);
                console.error('❌ 错误详情:', error.response?.data);
                this.showErrorMessage('网络错误，请重试: ' + (error.response?.data?.message || error.message));
            });
    }

    // 加载新品产品（用于息壤臻选区域）
    loadNewProducts() {
        console.log('🆕 开始加载新品产品...');
        axios.get('/products/new', { params: { limit: 6 } })
            .then(response => {
                console.log('🆕 新品API响应:', response.data);
                if (response.data.success) {
                    this.renderNewProducts(response.data.data);
                } else {
                    console.error('获取新品失败:', response.data.message);
                    this.showNewProductsError('获取新品失败');
                }
            })
            .catch(error => {
                console.error('加载新品失败:', error);
                this.showNewProductsError('网络错误，无法加载新品');
            });
    }

    // 加载所有产品（用于分类区域）
    loadAllProducts() {
        axios.get('/products/category/0') // categoryId=0表示全部
            .then(response => {
                if (response.data.success) {
                    this.renderProducts(response.data.data);
                } else {
                    console.error('获取产品失败:', response.data.message);
                    this.showErrorMessage('加载产品失败，请刷新页面重试');
                }
            })
            .catch(error => {
                console.error('加载产品失败:', error);
                this.showErrorMessage('加载产品失败，请刷新页面重试');
            });
    }

    // 渲染新品到息壤臻选区域
    renderNewProducts(products) {
        const newProductsGrid = document.querySelector('.new-products .product-grid');
        if (!newProductsGrid) {
            console.error('找不到新品展示区域');
            return;
        }

        // 清空现有新品（保留静态的前6个，如果有的话）
        // 这里我们可以选择完全替换或者追加

        if (products.length === 0) {
            console.log('没有新品数据，保持静态展示');
            return;
        }

        // 清空并重新渲染新品
        newProductsGrid.innerHTML = '';

        products.forEach(product => {
            const productCard = document.createElement('article');
            productCard.classList.add('product-card');
            productCard.innerHTML = `
                <div class="card-inner">
                    <div class="card-front" style="background-image: url('${product.imageUrl || '/images/default-product.svg'}')">
                        <div class="product-badge">新品</div>
                    </div>
                    <div class="card-back">
                        <h3>${product.name}</h3>
                        <p>${product.description}</p>
                        <div class="price">¥${product.price}</div>
                        <button class="btn-detail" data-product-id="${product.id || 0}">查看详情</button>
                        <button class="btn-cart" data-product-id="${product.id || 0}" data-product="${product.name || '未知产品'}" data-price="${product.price || 0}">
                            <i class="fas fa-cart-plus"></i> 加入购物车
                        </button>
                    </div>
                </div>
            `;
            newProductsGrid.appendChild(productCard);
        });

        console.log(`🆕 渲染新品成功，共 ${products.length} 个`);

        // 绑定新品查看详情按钮事件
        this.bindProductDetailEvents(newProductsGrid);
    }

    // 显示新品加载错误
    showNewProductsError(message) {
        const newProductsGrid = document.querySelector('.new-products .product-grid');
        if (newProductsGrid) {
            newProductsGrid.innerHTML = `
                <div class="loading-hint">
                    <i class="fas fa-exclamation-triangle" style="font-size: 2rem; color: #ff6b6b; margin-bottom: 1rem;"></i>
                    <p style="color: var(--text-dark);">${message}</p>
                </div>
            `;
        }
    }

    // 渲染产品卡片到分类区域
    renderProducts(products) {
        // 清空现有产品
        this.dynamicProducts.innerHTML = '';

        if (products.length === 0) {
            this.showNoProductsMessage();
            return;
        }

        products.forEach(product => {
            const productCard = document.createElement('article');
            productCard.classList.add('product-card');
            productCard.innerHTML = `
                <div class="card-inner">
                    <div class="card-front" style="background-image: url('${product.imageUrl || '/images/default-product.svg'}')">
                        <div class="product-badge">${product.isNew || product.is_new ? '新品' : ''}</div>
                    </div>
                    <div class="card-back">
                        <h3>${product.name}</h3>
                        <p>${product.description}</p>
                        <div class="price">¥${product.price}</div>
                        <button class="btn-detail" data-product-id="${product.id || 0}">查看详情</button>
                        <button class="btn-cart" data-product-id="${product.id || 0}" data-product="${product.name || '未知产品'}" data-price="${product.price || 0}">
                            <i class="fas fa-cart-plus"></i> 加入购物车
                        </button>
                    </div>
                </div>
            `;
            this.dynamicProducts.appendChild(productCard);
        });

        console.log(`📂 渲染分类产品成功，共 ${products.length} 个`);

        // 绑定分类产品查看详情按钮事件
        this.bindProductDetailEvents(this.dynamicProducts);
    }

    // 绑定产品详情按钮事件
    bindProductDetailEvents(container) {
        if (!container) return;

        // 使用事件委托绑定查看详情按钮
        container.addEventListener('click', (e) => {
            if (e.target.classList.contains('btn-detail') || e.target.closest('.btn-detail')) {
                const button = e.target.classList.contains('btn-detail') ? e.target : e.target.closest('.btn-detail');
                const productId = button.getAttribute('data-product-id');

                console.log('🔍 点击查看详情，产品ID:', productId);

                if (productId && productId !== '0') {
                    // 调用全局的查看产品详情函数
                    if (window.viewProductDetail) {
                        window.viewProductDetail(productId);
                    } else {
                        console.error('viewProductDetail 函数未定义');
                        this.showMessage('产品详情功能暂时不可用', 'warning');
                    }
                } else {
                    console.error('无效的产品ID:', productId);
                    this.showMessage('产品信息错误', 'error');
                }
            }
        });

        console.log('✅ 产品详情事件绑定完成');
    }

    // 显示无产品消息
    showNoProductsMessage(message = '暂无相关产品') {
        this.dynamicProducts.innerHTML = `
            <div class="no-products-message">
                <i class="fas fa-search" style="font-size: 3rem; color: #ccc; margin-bottom: 1rem;"></i>
                <p>${message}</p>
            </div>
        `;
    }

    // 显示错误消息
    showErrorMessage(message) {
        this.dynamicProducts.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-triangle" style="font-size: 3rem; color: #ff6b6b; margin-bottom: 1rem;"></i>
                <p>${message}</p>
            </div>
        `;
    }

    // 显示搜索建议
    showSuggestions(keyword) {
        axios.get('/products/search', { params: { keyword } })
            .then(response => {
                const results = response.data;
                if (results.length === 0) {
                    this.suggestions.innerHTML = '<div class="no-results">无相关产品</div>';
                } else {
                    this.suggestions.innerHTML = results.map(product =>
                        `<div class="suggestion-item">${product.name}</div>`
                    ).join('');
                }
                this.suggestions.style.display = 'block';
            })
            .catch(error => console.error('搜索建议失败:', error));
    }

    // 隐藏搜索建议
    hideSuggestions() {
        this.suggestions.style.display = 'none';
    }

    // 填充搜索框
    fillInput(text) {
        this.searchInput.value = text;
        this.searchInput.focus(); // 保持焦点
    }

    // 执行搜索
    executeSearch() {
        const keyword = this.searchInput.value.trim();
        this.hideSuggestions(); // 搜索时隐藏建议列表

        if (!keyword) {
            this.loadAllProducts();
            return;
        }

        axios.get('/products/search', { params: { keyword } })
            .then(response => {
                const results = response.data;
                if (results.length === 0) {
                    this.showNoResultsToast(); // 显示无结果提示
                    return;
                }
                this.renderProducts(results);

                // 搜索完成后滚动到产品区域
                this.scrollToProducts();
            })
            .catch(error => console.error('搜索产品失败:', error));
    }

    // 滚动到产品区域
    scrollToProducts() {
        console.log('🎯 开始滚动到产品区域...');

        // 查找产品区域元素，优先使用动态产品区域
        const productSection = this.dynamicProducts ||
                              document.querySelector('.dynamic-products') ||
                              this.productGrid ||
                              document.querySelector('.product-grid') ||
                              document.querySelector('#products') ||
                              document.querySelector('#products-section');

        console.log('🔍 找到的产品区域元素:', productSection);

        if (productSection) {
            // 计算滚动位置，留出一些顶部空间
            const rect = productSection.getBoundingClientRect();
            const scrollTop = window.pageYOffset + rect.top - 100; // 留出100px的顶部空间

            // 平滑滚动到产品区域
            window.scrollTo({
                top: scrollTop,
                behavior: 'smooth'
            });

            // 添加高亮效果
            productSection.style.transition = 'all 0.3s ease';
            productSection.style.boxShadow = '0 0 20px rgba(255, 105, 180, 0.3)';
            productSection.style.borderRadius = '10px';

            console.log('✨ 添加高亮效果');

            // 2秒后移除高亮效果
            setTimeout(() => {
                productSection.style.boxShadow = '';
                productSection.style.borderRadius = '';
                console.log('🔄 移除高亮效果');
            }, 2000);
        } else {
            console.warn('⚠️ 未找到产品区域元素');
        }
    }

    // 显示无结果提示（临时Toast）
    showNoResultsToast() {
        const toast = document.createElement('div');
        toast.className = 'search-toast';
        toast.textContent = '没有找到相关产品，请尝试其他关键词';
        toast.style.position = 'fixed';
        toast.style.top = '60px';
        toast.style.right = '20px';
        toast.style.padding = '10px 20px';
        toast.style.borderRadius = '25px';
        toast.style.backgroundColor = 'rgba(255, 77, 77, 0.9)';
        toast.style.color = 'white';
        toast.style.boxShadow = '0 3px 10px rgba(0,0,0,0.2)';
        document.body.appendChild(toast);

        // 3秒后消失
        setTimeout(() => toast.remove(), 3000);
    }
}

// 购物车功能类
class ShoppingCart {
    constructor(authManager) {
        // 初始化购物车数组，用于存储商品信息
        this.cart = [];
        // 用户认证管理器
        this.authManager = authManager;
        // 初始化事件监听
        this.initEvents();
        // 从后端加载购物车数据
        this.loadCartFromServer();

        // 创建提示框容器
        this.createAlertContainer();
    }

    // 创建全局提示框
    createAlertContainer() {
        this.alertContainer = document.createElement('div');
        this.alertContainer.className = 'cart-alert';
        document.body.appendChild(this.alertContainer);
    }

    initEvents() {
        // 为所有"加入购物车"按钮添加点击事件监听器
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('btn-cart')) {
                // 获取商品ID
                const productId = parseInt(e.target.dataset.productId);
                // 获取商品名称
                const product = e.target.dataset.product;
                // 获取商品价格并转换为数字
                const price = parseFloat(e.target.dataset.price);

                // 验证商品信息是否有效
                if (productId && product && product.trim() !== '' && !isNaN(price) && price > 0) {
                    this.addItemToCart(productId, product, price);
                } else {
                    this.showAlert('error', '⚠️ 商品信息不完整');
                }
            }
        });

        // 为购物车图标添加点击事件监听器，用于显示或隐藏购物车模态框
        const cartCounter = document.querySelector('.cart-counter');
        if (cartCounter) {
            cartCounter.addEventListener('click', () => {
                this.toggleCart();
            });
        }

        // 为关闭购物车模态框的按钮添加点击事件监听器
        const closeCartBtn = document.querySelector('.close-cart');
        if (closeCartBtn) {
            closeCartBtn.addEventListener('click', () => {
                this.toggleCart(false);
            });
        }

        // 使用事件委托，为购物车中的数量调整和删除按钮添加点击事件监听器
        const cartInstance = this; // 保存this引用
        document.addEventListener('click', (e) => {
            // 检查是否点击了购物车相关按钮
            const cartModal = e.target.closest('.cart-modal');
            if (!cartModal) return;

            if (e.target.classList.contains('cart-item-increase') || e.target.closest('.cart-item-increase')) {
                // 增加商品数量
                const button = e.target.classList.contains('cart-item-increase') ? e.target : e.target.closest('.cart-item-increase');
                cartInstance.changeQuantity(button.closest('li'), 1);
            } else if (e.target.classList.contains('cart-item-decrease') || e.target.closest('.cart-item-decrease')) {
                // 减少商品数量
                const button = e.target.classList.contains('cart-item-decrease') ? e.target : e.target.closest('.cart-item-decrease');
                cartInstance.changeQuantity(button.closest('li'), -1);
            } else if (e.target.classList.contains('cart-item-remove') || e.target.closest('.cart-item-remove')) {
                // 删除商品
                const button = e.target.classList.contains('cart-item-remove') ? e.target : e.target.closest('.cart-item-remove');
                console.log('🗑️ 点击删除按钮:', button);

                // 弹出确认框，确认是否删除商品
                if (confirm('确定要删除该商品吗？')) {
                    console.log('✅ 用户确认删除');
                    // 删除商品
                    cartInstance.removeItem(button.closest('li'));
                } else {
                    console.log('❌ 用户取消删除');
                }
            }
        });

        // 结算按钮点击事件（动态添加，在updateCartDisplay中处理）
        // 这里不需要添加事件监听器，因为按钮是动态生成的
    }

    // 显示提示框方法
    showAlert(type, message) {
        const alert = document.createElement('div');
        alert.className = `cart-alert-item ${type}`;
        alert.textContent = message;

        // 添加到提示容器
        this.alertContainer.appendChild(alert);

        // 3秒后自动消失
        setTimeout(() => {
            alert.remove();
        }, 3000);
    }

    // 添加商品到购物车（与后端交互）
    addItemToCart(productId, productName, price) {
        // 检查登录状态
        if (!this.authManager.isLoggedIn()) {
            this.showAlert('warning', '⚠️ 请先登录');
            setTimeout(() => {
                window.location.href = 'land.html';
            }, 1500);
            return;
        }

        const currentUser = this.authManager.getCurrentUser();
        const cartData = {
            userPhone: currentUser.phone || currentUser.userId,
            productId: productId,
            quantity: 1
        };

        // 发送请求到后端
        axios.post('/api/cart/add', cartData)
            .then(response => {
                if (response.data.success) {
                    this.showAlert('success', '✔️ 添加成功');
                    this.loadCartFromServer(); // 重新加载购物车
                } else {
                    this.showAlert('error', '❌ ' + (response.data.message || '添加失败'));
                }
            })
            .catch(error => {
                console.error('添加购物车失败:', error);
                this.showAlert('error', '❌ 网络错误，请重试');
            });
    }

    // 从服务器加载购物车数据
    loadCartFromServer() {
        if (!this.authManager.isLoggedIn()) {
            // 未登录状态，清空购物车显示
            this.cart = [];
            this.updateCartDisplay();
            return;
        }

        const currentUser = this.authManager.getCurrentUser();
        const userIdentifier = currentUser.phone || currentUser.userId;

        axios.get(`/api/cart/${userIdentifier}`)
            .then(response => {
                if (response.data.success) {
                    this.cart = response.data.data || [];
                    this.updateCartDisplay();
                } else {
                    console.error('加载购物车失败:', response.data.message);
                    // 如果后端接口不可用，从本地存储加载
                    this.loadCartFromLocalStorage();
                }
            })
            .catch(error => {
                console.error('加载购物车失败:', error);
                // 如果后端接口不可用，从本地存储加载
                this.loadCartFromLocalStorage();
            });
    }

    // 更新购物车显示
    updateCartDisplay() {
        const cartItems = document.querySelector('.cart-items');
        const cartCount = document.querySelector('.cart-count');
        const modalCartCount = document.querySelector('.modal-cart-count');
        const totalPrice = document.querySelector('.total-price');

        if (!cartItems) return;

        cartItems.innerHTML = '';
        let total = 0;
        let itemCount = 0;

        if (this.cart.length === 0) {
            cartItems.innerHTML = '<li class="empty-cart">购物车为空</li>';
        } else {
            this.cart.forEach((item, index) => {
                const li = document.createElement('li');
                li.className = 'cart-item';
                li.innerHTML = `
                    <div class="cart-item-checkbox">
                        <input type="checkbox" class="item-checkbox" data-index="${index}" checked>
                    </div>
                    <div class="cart-item-image">
                        <img src="${item.productImageUrl || 'images/default-product.svg'}" alt="${item.productName || item.name}">
                    </div>
                    <div class="cart-item-info">
                        <div class="cart-item-name">${item.productName || item.name}</div>
                        <div class="cart-item-price">¥${(item.productPrice || item.price).toFixed(2)}</div>
                    </div>
                    <div class="cart-item-controls">
                        <button class="cart-item-decrease" data-product-id="${item.productId}" ${item.quantity <= 1 ? 'disabled' : ''}>-</button>
                        <span class="cart-item-quantity">${item.quantity}</span>
                        <button class="cart-item-increase" data-product-id="${item.productId}">+</button>
                    </div>
                    <div class="cart-item-total">
                        ¥${((item.productPrice || item.price) * item.quantity).toFixed(2)}
                    </div>
                    <div class="cart-item-actions">
                        <button class="cart-item-remove" data-product-id="${item.productId}" title="删除商品">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                `;
                cartItems.appendChild(li);

                const itemPrice = item.productPrice || item.price;
                total += itemPrice * item.quantity;
                itemCount += item.quantity;
            });

            // 添加全选和批量操作
            const cartFooter = document.querySelector('.cart-footer');
            if (cartFooter) {
                cartFooter.innerHTML = `
                    <div class="cart-batch-operations">
                        <label class="select-all">
                            <input type="checkbox" id="selectAllItems" checked> 全选
                        </label>
                        <button class="btn-clear-selected">删除选中</button>
                    </div>
                    <div class="cart-summary">
                        <div class="total-info">
                            <span>共 ${itemCount} 件商品，总计：</span>
                            <span class="total-price">¥${total.toFixed(2)}</span>
                        </div>
                        <button class="btn-checkout">结算</button>
                    </div>
                `;

                // 添加全选功能
                const selectAllCheckbox = document.getElementById('selectAllItems');
                if (selectAllCheckbox) {
                    selectAllCheckbox.addEventListener('change', (e) => {
                        const itemCheckboxes = document.querySelectorAll('.item-checkbox');
                        itemCheckboxes.forEach(checkbox => {
                            checkbox.checked = e.target.checked;
                        });
                        this.updateCartTotal();
                    });
                }

                // 添加删除选中功能
                const clearSelectedBtn = document.querySelector('.btn-clear-selected');
                if (clearSelectedBtn) {
                    clearSelectedBtn.addEventListener('click', () => {
                        this.clearSelectedItems();
                    });
                }

                // 添加结算按钮功能
                const checkoutBtn = document.querySelector('.btn-checkout');
                if (checkoutBtn) {
                    checkoutBtn.addEventListener('click', () => {
                        this.checkout();
                    });
                }
            }
        }

        if (cartCount) cartCount.textContent = itemCount;
        if (modalCartCount) modalCartCount.textContent = itemCount;
        if (totalPrice) totalPrice.textContent = total.toFixed(2);

        // 添加复选框变化监听
        document.querySelectorAll('.item-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.updateCartTotal();
            });
        });
    }

    // 改变商品数量
    changeQuantity(itemElement, amount) {
        if (!this.authManager.isLoggedIn()) {
            this.showAlert('warning', '⚠️ 请先登录');
            return;
        }

        const productId = parseInt(itemElement.querySelector('.cart-item-increase, .cart-item-decrease').dataset.productId);
        const currentQuantity = parseInt(itemElement.querySelector('.cart-item-quantity').textContent);
        const newQuantity = Math.max(1, currentQuantity + amount);

        const currentUser = this.authManager.getCurrentUser();
        const updateData = {
            userPhone: currentUser.phone || currentUser.userId,
            productId: productId,
            quantity: newQuantity
        };

        axios.post('/api/cart/update', updateData)
            .then(response => {
                if (response.data.success) {
                    this.loadCartFromServer(); // 重新加载购物车
                    // 更新购物车计数
                    updateCartCount();
                } else {
                    this.showAlert('error', '❌ 更新失败');
                }
            })
            .catch(error => {
                console.error('更新购物车失败:', error);
                this.showAlert('error', '❌ 网络错误');
            });
    }

    // 删除商品
    removeItem(itemElement) {
        if (!this.authManager.isLoggedIn()) {
            this.showAlert('warning', '⚠️ 请先登录');
            return;
        }

        const productId = parseInt(itemElement.querySelector('.cart-item-remove').dataset.productId);
        const currentUser = this.authManager.getCurrentUser();
        const userIdentifier = currentUser.phone || currentUser.userId;

        axios.delete(`/api/cart/remove/${userIdentifier}/${productId}`)
            .then(response => {
                if (response.data.success) {
                    this.showAlert('success', '✔️ 删除成功');
                    this.loadCartFromServer(); // 重新加载购物车
                    // 更新购物车计数
                    updateCartCount();
                    // 不要立即强制更新，让loadCartFromServer自然更新
                } else {
                    this.showAlert('error', '❌ 删除失败');
                }
            })
            .catch(error => {
                console.error('删除购物车商品失败:', error);
                this.showAlert('error', '❌ 网络错误');
            });
    }

    // 显示或隐藏购物车模态框
    toggleCart(show) {
        const cartModal = document.querySelector('.cart-modal');
        if (show === undefined) {
            cartModal.style.display = cartModal.style.display === 'block' ? 'none' : 'block';
        } else {
            cartModal.style.display = show ? 'block' : 'none';
        }
    }

    // 结算功能
    checkout() {
        if (!this.authManager.isLoggedIn()) {
            this.showAlert('warning', '⚠️ 请先登录');
            setTimeout(() => {
                window.location.href = 'land.html';
            }, 1500);
            return;
        }

        if (this.cart.length === 0) {
            this.showAlert('warning', '⚠️ 购物车为空');
            return;
        }

        // 计算总金额
        let totalAmount = 0;
        this.cart.forEach(item => {
            const itemPrice = item.productPrice || item.price;
            totalAmount += itemPrice * item.quantity;
        });

        const currentUser = this.authManager.getCurrentUser();
        // 创建订单数据
        const orderData = {
            userPhone: currentUser.phone || currentUser.userId,
            totalAmount: totalAmount,
            items: this.cart.map(item => ({
                productId: item.productId,
                productName: item.productName || item.name,
                productPrice: item.productPrice || item.price,
                quantity: item.quantity
            }))
        };

        // 发送订单到后端
        axios.post('/api/order/create', orderData)
            .then(response => {
                if (response.data.success) {
                    this.showAlert('success', '✔️ 订单创建成功！');
                    this.cart = []; // 清空购物车
                    this.updateCartDisplay();
                    this.toggleCart(false); // 关闭购物车

                    // 更新订单统计
                    updateOrderStats();
                } else {
                    this.showAlert('error', '❌ ' + (response.data.message || '订单创建失败'));
                }
            })
            .catch(error => {
                console.error('创建订单失败:', error);
                this.showAlert('error', '❌ 网络错误，请重试');
            });
    }

    // 从本地存储加载购物车数据（备用方案）
    loadCartFromLocalStorage() {
        const cartData = localStorage.getItem('shoppingCart');
        if (cartData) {
            this.cart = JSON.parse(cartData);
            this.updateCartDisplay();
        }
    }

    // 保存购物车数据到本地存储（备用方案）
    saveCartToLocalStorage() {
        localStorage.setItem('shoppingCart', JSON.stringify(this.cart));
    }

    // 更新购物车总计（只计算选中的商品）
    updateCartTotal() {
        const selectedCheckboxes = document.querySelectorAll('.item-checkbox:checked');
        let total = 0;
        let selectedCount = 0;

        selectedCheckboxes.forEach(checkbox => {
            const index = parseInt(checkbox.dataset.index);
            const item = this.cart[index];
            if (item) {
                const itemPrice = item.productPrice || item.price;
                total += itemPrice * item.quantity;
                selectedCount += item.quantity;
            }
        });

        const totalPriceEl = document.querySelector('.total-price');
        const totalInfoEl = document.querySelector('.total-info span');

        if (totalPriceEl) totalPriceEl.textContent = `¥${total.toFixed(2)}`;
        if (totalInfoEl) totalInfoEl.textContent = `共 ${selectedCount} 件商品，总计：`;

        // 更新全选状态
        const selectAllCheckbox = document.getElementById('selectAllItems');
        const itemCheckboxes = document.querySelectorAll('.item-checkbox');
        if (selectAllCheckbox && itemCheckboxes.length > 0) {
            const checkedCount = document.querySelectorAll('.item-checkbox:checked').length;
            selectAllCheckbox.checked = checkedCount === itemCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < itemCheckboxes.length;
        }
    }

    // 删除选中的商品
    clearSelectedItems() {
        const selectedCheckboxes = document.querySelectorAll('.item-checkbox:checked');

        if (selectedCheckboxes.length === 0) {
            this.showAlert('warning', '⚠️ 请选择要删除的商品');
            return;
        }

        if (!confirm(`确定要删除选中的 ${selectedCheckboxes.length} 件商品吗？`)) {
            return;
        }

        const selectedIndices = Array.from(selectedCheckboxes).map(checkbox =>
            parseInt(checkbox.dataset.index)
        ).sort((a, b) => b - a); // 从后往前删除，避免索引变化

        const currentUser = this.authManager.getCurrentUser();
        const userIdentifier = currentUser.phone || currentUser.userId;

        // 批量删除请求
        const deletePromises = selectedIndices.map(index => {
            const item = this.cart[index];
            return axios.delete(`/api/cart/remove/${userIdentifier}/${item.productId}`);
        });

        Promise.all(deletePromises)
            .then(responses => {
                const successCount = responses.filter(res => res.data.success).length;
                if (successCount === selectedIndices.length) {
                    this.showAlert('success', `✔️ 成功删除 ${successCount} 件商品`);
                    this.loadCartFromServer(); // 重新加载购物车
                } else {
                    this.showAlert('warning', `⚠️ 删除了 ${successCount}/${selectedIndices.length} 件商品`);
                    this.loadCartFromServer(); // 重新加载购物车
                }
            })
            .catch(error => {
                console.error('批量删除失败:', error);
                this.showAlert('error', '❌ 删除失败，请重试');
            });
    }

    // 清空购物车
    clearCart() {
        if (!this.authManager.isLoggedIn()) {
            this.showAlert('warning', '⚠️ 请先登录');
            return;
        }

        if (this.cart.length === 0) {
            this.showAlert('info', 'ℹ️ 购物车已经是空的');
            return;
        }

        if (!confirm('确定要清空购物车吗？')) {
            return;
        }

        const currentUser = this.authManager.getCurrentUser();
        const userIdentifier = currentUser.phone || currentUser.userId;

        axios.delete(`/api/cart/clear/${userIdentifier}`)
            .then(response => {
                if (response.data.success) {
                    this.showAlert('success', '✔️ 购物车已清空');
                    this.cart = [];
                    this.updateCartDisplay();
                } else {
                    this.showAlert('error', '❌ 清空失败');
                }
            })
            .catch(error => {
                console.error('清空购物车失败:', error);
                this.showAlert('error', '❌ 网络错误');
            });
    }
}

// 用户登录状态管理类
class UserAuthManager {
    constructor() {
        this.currentUser = null;
        this.storageKey = 'sky_current_user';
        this.loadUserFromStorage();
    }

    // 从本地存储加载用户信息
    loadUserFromStorage() {
        const userData = localStorage.getItem(this.storageKey);
        if (userData) {
            try {
                this.currentUser = JSON.parse(userData);
                return true;
            } catch (error) {
                console.error('解析用户数据失败:', error);
                this.clearUserData();
            }
        }
        return false;
    }

    // 保存用户信息到本地存储
    saveUserToStorage(userData) {
        this.currentUser = userData;
        localStorage.setItem(this.storageKey, JSON.stringify(userData));
    }

    // 清除用户数据
    clearUserData() {
        this.currentUser = null;
        localStorage.removeItem(this.storageKey);
    }

    // 检查是否已登录
    isLoggedIn() {
        return this.currentUser !== null;
    }

    // 获取当前用户信息
    getCurrentUser() {
        return this.currentUser;
    }

    // 登出
    logout() {
        // 关闭所有模态框
        if (window.closeAllModals) {
            window.closeAllModals();
        }
        this.clearUserData();
        window.location.href = 'land.html';
    }

    // 关闭所有模态框
    closeAllModals() {
        // 隐藏用户中心模态框
        const userCenterModal = document.querySelector('.user-center-modal');
        if (userCenterModal) {
            userCenterModal.classList.remove('show');
            userCenterModal.style.display = 'none';
        }

        // 移除所有动态创建的模态框（包括新增的类型）
        const modalSelectors = [
            '.profile-modal', '.settings-modal', '.balance-modal', '.friends-modal',
            '.coupons-modal', '.user-comments-modal', '.user-shares-modal',
            '.order-management-modal', '.address-management-modal', '.favorites-modal',
            '.modal-overlay' // 直接移除所有遮罩层
        ];

        modalSelectors.forEach(selector => {
            const modals = document.querySelectorAll(selector);
            modals.forEach(modal => {
                if (modal && modal.parentNode) {
                    modal.parentNode.removeChild(modal);
                }
            });
        });

        // 恢复页面滚动和样式
        document.body.style.overflow = '';
        document.body.style.filter = '';
        document.body.style.pointerEvents = '';

        // 移除页面模糊效果
        document.body.classList.remove('modal-open');

        // 恢复主内容区域
        const mainElements = document.querySelectorAll('main, .main-content, header, nav, section');
        mainElements.forEach(element => {
            if (element) {
                element.style.filter = '';
                element.style.pointerEvents = '';
            }
        });

        // 确保移除所有可能的遮罩效果
        const allElements = document.querySelectorAll('*');
        allElements.forEach(element => {
            if (element.style.filter && element.style.filter.includes('blur')) {
                element.style.filter = '';
            }
        });
    }

    // 强制清理所有用户相关数据（调试用）
    forceCleanup() {
        console.log('🧹 强制清理所有用户数据...');
        localStorage.removeItem('sky_current_user');
        localStorage.removeItem('user_data');
        localStorage.removeItem('current_user');
        localStorage.removeItem('userInfo');
        this.currentUser = null;
        console.log('✅ 清理完成');
    }
}

// 用户中心功能类
class UserCenter {
    constructor() {
        this.authManager = new UserAuthManager();
        this.userDetailData = null; // 用户详细信息
        this.initEvents();
        this.loadUserInfo();
    }

    initEvents() {
        // 用户头像点击事件
        const userAvatarBtn = document.getElementById('userAvatarBtn');
        if (userAvatarBtn) {
            userAvatarBtn.addEventListener('click', (e) => {
                e.preventDefault(); // 阻止默认行为
                e.stopPropagation(); // 阻止事件冒泡
                this.handleUserCenterClick();
            });
        }

        // 关闭用户中心
        const closeBtn = document.querySelector('.close-user-center');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.toggleUserCenter(false);
            });
        }

        // 切换账户按钮
        const switchBtn = document.querySelector('.switch-account');
        if (switchBtn) {
            switchBtn.addEventListener('click', () => {
                this.switchAccount();
            });
        }

        // 切换账户确认模态框事件
        const confirmCancel = document.querySelector('.confirm-cancel');
        if (confirmCancel) {
            confirmCancel.addEventListener('click', () => {
                this.hideSwitchAccountModal();
            });
        }

        const confirmOk = document.querySelector('.confirm-ok');
        if (confirmOk) {
            confirmOk.addEventListener('click', () => {
                this.confirmSwitchAccount();
            });
        }

        // 个性签名编辑模态框事件
        const signatureInput = document.getElementById('signatureInput');
        if (signatureInput) {
            signatureInput.addEventListener('input', () => {
                this.updateSignatureCounter();
            });
        }

        const closeSignature = document.querySelector('.close-signature');
        if (closeSignature) {
            closeSignature.addEventListener('click', () => {
                this.closeSignatureModal();
            });
        }

        const signatureCancel = document.querySelector('.signature-cancel');
        if (signatureCancel) {
            signatureCancel.addEventListener('click', () => {
                this.closeSignatureModal();
            });
        }

        const signatureSave = document.querySelector('.signature-save');
        if (signatureSave) {
            signatureSave.addEventListener('click', () => {
                this.saveSignature();
            });
        }

        // 头像上传模态框事件
        const avatarFileInput = document.getElementById('avatarFileInput');
        if (avatarFileInput) {
            avatarFileInput.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                    this.handleAvatarUpload(file);
                }
            });
        }

        const closeAvatar = document.querySelector('.close-avatar');
        if (closeAvatar) {
            closeAvatar.addEventListener('click', () => {
                this.closeAvatarModal();
            });
        }

        const avatarCancel = document.querySelector('.avatar-cancel');
        if (avatarCancel) {
            avatarCancel.addEventListener('click', () => {
                this.closeAvatarModal();
            });
        }

        const avatarSave = document.querySelector('.avatar-save');
        if (avatarSave) {
            avatarSave.addEventListener('click', () => {
                this.saveAvatar();
            });
        }

        // 预设头像点击事件
        const presetAvatars = document.querySelectorAll('.preset-avatar');
        presetAvatars.forEach(avatar => {
            avatar.addEventListener('click', () => {
                const avatarPath = avatar.dataset.avatar;
                this.selectPresetAvatar(avatarPath);
            });
        });

        // 点击模态框背景关闭
        const signatureModal = document.querySelector('.signature-modal');
        if (signatureModal) {
            signatureModal.addEventListener('click', (e) => {
                if (e.target === signatureModal) {
                    this.closeSignatureModal();
                }
            });
        }

        const avatarModal = document.querySelector('.avatar-modal');
        if (avatarModal) {
            avatarModal.addEventListener('click', (e) => {
                if (e.target === avatarModal) {
                    this.closeAvatarModal();
                }
            });
        }
    }

    // 加载用户信息
    loadUserInfo() {
        if (this.authManager.isLoggedIn()) {
            const user = this.authManager.getCurrentUser();
            // 先显示基本用户信息（用户名、ID），但不设置头像
            this.updateUserDisplayWithoutAvatar(user);

            // 从后端获取最新用户信息，包括正确的头像
            this.fetchUserInfoFromServer(user.phone || user.userId);
        } else {
            // 未登录状态，显示默认信息
            this.showGuestMode();
        }
    }

    // 从服务器获取用户详细信息
    fetchUserInfoFromServer(identifier) {
        // 获取基本用户信息
        axios.get(`/api/user/info/${identifier}`)
            .then(response => {
                if (response.data.success) {
                    const userData = response.data.data;
                    this.authManager.saveUserToStorage(userData);
                    // 只更新用户名和ID，不更新头像
                    this.updateUserDisplayWithoutAvatar(userData);
                }
            })
            .catch(error => {
                console.error('获取用户信息失败:', error);
            });

        // 获取用户详细信息
        axios.get(`/api/user-detail/${identifier}`)
            .then(response => {
                if (response.data.success) {
                    this.userDetailData = response.data.data;
                    console.log('📋 获取用户详情成功:', this.userDetailData);

                    // 直接更新头像显示，不进行额外的检查
                    if (this.userDetailData.avatarUrl) {
                        console.log('🖼️ 用户详情中的头像:', this.userDetailData.avatarUrl);
                        this.updateAvatarFromUserDetail(this.userDetailData.avatarUrl);
                    }

                    this.updateUserCenterDisplay();
                }
            })
            .catch(error => {
                console.error('获取用户详情失败:', error);
            });
    }

    // 从用户详情直接更新头像显示
    updateAvatarFromUserDetail(avatarUrl) {
        console.log('🔄 从用户详情更新头像:', avatarUrl);

        let processedAvatarUrl = avatarUrl;

        // 处理头像URL
        if (avatarUrl && avatarUrl !== 'null' && avatarUrl.trim() !== '') {
            // 如果已经是完整路径（包含images/），直接使用
            if (avatarUrl.startsWith('images/')) {
                processedAvatarUrl = avatarUrl;
            } else if (!avatarUrl.startsWith('http') && !avatarUrl.startsWith('/') && !avatarUrl.startsWith('data:')) {
                // 判断是预设头像还是用户上传的头像
                if (avatarUrl.startsWith('avatar_')) {
                    // 用户上传的头像，保存在avatars文件夹
                    processedAvatarUrl = `images/avatars/${avatarUrl}`;
                } else {
                    // 预设头像，保存在avatar文件夹
                    processedAvatarUrl = `images/avatar/${avatarUrl}`;
                }
            }

            // 添加时间戳防止浏览器缓存旧头像
            if (processedAvatarUrl.startsWith('images/avatars/')) {
                processedAvatarUrl += `?t=${Date.now()}`;
            }
        } else {
            // 如果头像URL为空或无效，使用默认头像
            processedAvatarUrl = 'images/avatar/default-avatar.jpg';
        }

        console.log('✅ 处理后的头像URL:', processedAvatarUrl);

        // 更新页面中的头像元素
        const profileAvatarEl = document.querySelector('.profile-avatar');
        const avatarIconEl = document.querySelector('.avatar-icon');

        if (profileAvatarEl) {
            profileAvatarEl.src = processedAvatarUrl;
            console.log('🖼️ 已更新profile-avatar');
        }
        if (avatarIconEl) {
            avatarIconEl.src = processedAvatarUrl;
            console.log('🖼️ 已更新avatar-icon');
        }
    }

    // 更新用户显示（不包括头像）
    updateUserDisplayWithoutAvatar(user) {
        if (user) {
            const usernameEl = document.querySelector('.username');
            const accountIdEl = document.querySelector('.account-id');

            if (usernameEl) usernameEl.textContent = user.username || user.name || '用户';
            if (accountIdEl) accountIdEl.textContent = `ID: ${user.userId || user.id || 'N/A'}`;
        }
    }

    // 更新用户显示
    updateUserDisplay(user) {
        if (user) {
            const usernameEl = document.querySelector('.username');
            const accountIdEl = document.querySelector('.account-id');
            const profileAvatarEl = document.querySelector('.profile-avatar');
            const avatarIconEl = document.querySelector('.avatar-icon');

            if (usernameEl) usernameEl.textContent = user.username || user.name || '用户';
            if (accountIdEl) accountIdEl.textContent = `ID: ${user.userId || user.id || 'N/A'}`;

            // 处理头像URL
            let avatarUrl = user.avatar || user.avatarUrl || 'default-avatar.jpg';

            // 如果已经是完整路径（包含images/），直接使用
            if (avatarUrl.startsWith('images/')) {
                // 已经是完整路径，直接使用
            } else if (!avatarUrl.startsWith('http') && !avatarUrl.startsWith('/') && !avatarUrl.startsWith('data:')) {
                // 判断是预设头像还是用户上传的头像
                if (avatarUrl.startsWith('avatar_')) {
                    // 用户上传的头像，保存在avatars文件夹
                    avatarUrl = `images/avatars/${avatarUrl}`;
                } else {
                    // 预设头像，保存在avatar文件夹
                    avatarUrl = `images/avatar/${avatarUrl}`;
                }
            }

            // 添加时间戳防止浏览器缓存旧头像
            if (avatarUrl.startsWith('images/avatars/')) {
                avatarUrl += `?t=${Date.now()}`;
            }

            if (profileAvatarEl) profileAvatarEl.src = avatarUrl;
            if (avatarIconEl) avatarIconEl.src = avatarUrl;
        }
    }

    // 显示访客模式
    showGuestMode() {
        const usernameEl = document.querySelector('.username');
        const accountIdEl = document.querySelector('.account-id');
        const profileAvatarEl = document.querySelector('.profile-avatar');
        const avatarIconEl = document.querySelector('.avatar-icon');

        if (usernameEl) usernameEl.textContent = '点击登录';
        if (accountIdEl) accountIdEl.textContent = '未登录状态';

        const defaultAvatar = '/images/avatar/default-avatar.jpg';
        if (profileAvatarEl) profileAvatarEl.src = defaultAvatar;
        if (avatarIconEl) avatarIconEl.src = defaultAvatar;

        // 更新登录状态相关的UI
        this.updateLoginStatusUI();
    }

    // 处理用户中心点击事件
    handleUserCenterClick() {
        console.log('🔍 用户中心点击，检查登录状态...');

        // 调试：显示当前localStorage内容
        const userData = localStorage.getItem('sky_current_user');
        console.log('📱 localStorage内容:', userData);

        // 调试：显示当前用户对象
        const currentUser = this.authManager.getCurrentUser();
        console.log('👤 当前用户对象:', currentUser);

        if (this.authManager.isLoggedIn()) {
            console.log('✅ 用户已登录，显示用户中心');
            this.toggleUserCenter();
        } else {
            console.log('❌ 用户未登录，跳转到登录页面');
            // 显示提示信息
            this.showMessage('请先登录后再访问个人中心', 'warning');
            // 延迟跳转，让用户看到提示
            setTimeout(() => {
                window.location.href = 'land.html';
            }, 1500);
        }
    }

    // 显示/隐藏用户中心
    toggleUserCenter(show) {
        const modal = document.querySelector('.user-center-modal');
        if (modal) {
            if (show === undefined) {
                // 切换显示状态
                const isVisible = modal.classList.contains('show');
                if (isVisible) {
                    modal.classList.remove('show');
                    modal.style.display = 'none';
                } else {
                    modal.classList.add('show');
                    modal.style.display = 'flex';
                    // 加载用户数据
                    this.loadUserInfo();
                }
            } else {
                if (show) {
                    modal.classList.add('show');
                    modal.style.display = 'flex';
                    // 加载用户数据
                    this.loadUserInfo();
                } else {
                    modal.classList.remove('show');
                    modal.style.display = 'none';
                }
            }
        }
    }

    // 显示切换账户确认模态框
    showSwitchAccountModal() {
        const modal = document.querySelector('.confirm-modal');
        if (modal) {
            modal.style.display = 'block';
        }
    }

    // 隐藏切换账户确认模态框
    hideSwitchAccountModal() {
        const modal = document.querySelector('.confirm-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    // 切换账户
    switchAccount() {
        // 直接跳转到登录页面
        window.location.href = 'land.html';
    }

    // 确认切换账户
    confirmSwitchAccount() {
        this.authManager.logout();
    }

    // 更新用户中心详细信息显示
    updateUserCenterDisplay() {
        // 更新登录状态相关的UI
        this.updateLoginStatusUI();

        if (!this.userDetailData) return;

        // 更新用户名
        const usernameEl = document.querySelector('.user-center-modal .username');
        if (usernameEl) {
            usernameEl.textContent = this.userDetailData.username || this.userDetailData.userName || '未知用户';
        }

        // 更新用户ID
        const userIdEl = document.querySelector('.user-center-modal .account-id');
        if (userIdEl) {
            userIdEl.textContent = `ID: ${this.userDetailData.userId || this.userDetailData.userAccount || 'N/A'}`;
        }

        // 更新个性签名
        const signatureTextEl = document.querySelector('.signature-text');
        if (signatureTextEl) {
            signatureTextEl.textContent = this.userDetailData.signature || '这个人很懒，什么都没留下~';
        }

        // 更新统计信息
        this.updateUserStats();

        // 更新收货地址
        this.updateAddressList();

        // 更新订单信息
        this.updateOrderInfo();

        // 更新好友信息
        this.updateFriendInfo();
    }

    // 更新登录状态相关的UI
    updateLoginStatusUI() {
        const isLoggedIn = this.authManager.isLoggedIn();

        // 更新头像覆盖层文本
        const avatarOverlayText = document.querySelector('.avatar-overlay-text');
        if (avatarOverlayText) {
            avatarOverlayText.textContent = isLoggedIn ? '更换头像' : '请先登录';
        }

        // 更新签名区域提示
        const signatureText = document.querySelector('.signature-text');
        if (signatureText && !isLoggedIn) {
            signatureText.textContent = '请先登录查看个性签名';
        }

        // 更新用户名显示
        const usernameEl = document.querySelector('.username');
        if (usernameEl) {
            if (!isLoggedIn) {
                usernameEl.textContent = '点击登录';
            } else if (this.userDetailData) {
                usernameEl.textContent = this.userDetailData.username || this.userDetailData.userName || '未知用户';
            }
        }

        // 更新账户ID显示
        const accountIdEl = document.querySelector('.account-id');
        if (accountIdEl) {
            if (!isLoggedIn) {
                accountIdEl.textContent = 'ID: 未登录';
            } else if (this.userDetailData) {
                accountIdEl.textContent = `ID: ${this.userDetailData.userId || this.userDetailData.userAccount || 'N/A'}`;
            }
        }
    }

    // 更新用户统计信息
    updateUserStats() {
        const cartCountEl = document.querySelector('.stat-cart-count');
        const orderCountEl = document.querySelector('.stat-order-count');
        const friendCountEl = document.querySelector('.stat-friend-count');

        if (cartCountEl) cartCountEl.textContent = this.userDetailData.cartItemCount || 0;
        if (orderCountEl) orderCountEl.textContent = this.userDetailData.totalOrderCount || 0;
        if (friendCountEl) friendCountEl.textContent = this.userDetailData.friendCount || 0;
    }

    // 更新收货地址列表
    updateAddressList() {
        const addressListEl = document.querySelector('.address-list');
        if (!addressListEl) return;

        if (!this.userDetailData.addresses || this.userDetailData.addresses.length === 0) {
            addressListEl.innerHTML = '<div class="no-address">暂无收货地址</div>';
            return;
        }

        const addressHTML = this.userDetailData.addresses.map(address => `
            <div class="address-item ${address.isDefault ? 'default' : ''}">
                <div class="address-header">
                    <span class="receiver-name">${address.receiverName}</span>
                    <span class="receiver-phone">${address.receiverPhone}</span>
                    ${address.isDefault ? '<span class="default-tag">默认</span>' : ''}
                </div>
                <div class="address-detail">
                    ${address.province} ${address.city} ${address.district} ${address.detailAddress}
                </div>
            </div>
        `).join('');

        addressListEl.innerHTML = addressHTML;
    }

    // 更新订单信息
    updateOrderInfo() {
        const orderInfoEl = document.querySelector('.order-summary');
        if (!orderInfoEl) return;

        const pendingCount = this.userDetailData.pendingOrderCount || 0;
        const totalCount = this.userDetailData.totalOrderCount || 0;

        orderInfoEl.innerHTML = `
            <div class="order-stat">
                <span class="order-label">待处理订单:</span>
                <span class="order-count pending">${pendingCount}</span>
            </div>
            <div class="order-stat">
                <span class="order-label">总订单数:</span>
                <span class="order-count total">${totalCount}</span>
            </div>
        `;
    }

    // 更新好友信息
    updateFriendInfo() {
        const friendInfoEl = document.querySelector('.friend-summary');
        if (!friendInfoEl) return;

        const friendCount = this.userDetailData.friendCount || 0;
        friendInfoEl.innerHTML = `
            <div class="friend-stat">
                <i class="fas fa-users"></i>
                <span class="friend-count">${friendCount}</span>
                <span class="friend-label">位好友</span>
            </div>
        `;
    }

    // 编辑个性签名
    editSignature() {
        // 检查登录状态
        if (!this.authManager.isLoggedIn()) {
            this.showMessage('请先登录后再编辑个性签名', 'warning');
            setTimeout(() => {
                window.location.href = 'land.html';
            }, 2000);
            return;
        }

        const modal = document.querySelector('.signature-modal');
        const input = document.getElementById('signatureInput');
        const counter = document.getElementById('signatureCount');

        if (modal && input) {
            // 设置当前签名
            const currentSignature = this.userDetailData?.signature || '这个人很懒，什么都没留下~';
            input.value = currentSignature;
            this.updateSignatureCounter();

            // 显示模态框
            modal.style.display = 'block';
            input.focus();
        }
    }

    // 更新签名字符计数
    updateSignatureCounter() {
        const input = document.getElementById('signatureInput');
        const counter = document.getElementById('signatureCount');
        if (input && counter) {
            counter.textContent = input.value.length;
        }
    }

    // 显示头像上传模态框
    showAvatarModal() {
        // 检查登录状态
        if (!this.authManager.isLoggedIn()) {
            this.showMessage('请先登录后再更换头像', 'warning');
            setTimeout(() => {
                window.location.href = 'land.html';
            }, 2000);
            return;
        }

        const modal = document.querySelector('.avatar-modal');
        const preview = document.getElementById('avatarPreview');

        if (modal && preview) {
            // 设置当前头像
            let currentAvatar = this.userDetailData?.avatarUrl || 'default-avatar.jpg';

            // 处理头像路径
            if (currentAvatar.startsWith('images/')) {
                // 已经是完整路径，直接使用
            } else if (!currentAvatar.startsWith('http') && !currentAvatar.startsWith('/') && !currentAvatar.startsWith('data:')) {
                // 判断是预设头像还是用户上传的头像
                if (currentAvatar.startsWith('avatar_')) {
                    // 用户上传的头像，保存在avatars文件夹
                    currentAvatar = `images/avatars/${currentAvatar}`;
                } else {
                    // 预设头像，保存在avatar文件夹
                    currentAvatar = `images/avatar/${currentAvatar}`;
                }
            }

            // 添加时间戳防止浏览器缓存
            if (currentAvatar.startsWith('images/avatars/')) {
                currentAvatar += `?t=${Date.now()}`;
            }

            preview.src = currentAvatar;

            // 显示模态框
            modal.style.display = 'block';
        }
    }

    // 上传头像（直接文件选择）
    uploadAvatar() {
        // 检查登录状态
        if (!this.authManager.isLoggedIn()) {
            this.showMessage('请先登录后再更换头像', 'warning');
            setTimeout(() => {
                window.location.href = 'land.html';
            }, 2000);
            return;
        }

        // 创建文件输入元素
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                this.uploadAvatarFile(file);  // 调用真正的文件上传方法
            }
        };
        input.click();
    }

    // 处理头像文件上传
    handleAvatarUpload(file) {
        if (!file) return;

        // 验证文件类型
        if (!file.type.startsWith('image/')) {
            this.showMessage('请选择图片文件', 'error');
            return;
        }

        // 验证文件大小 (2MB)
        if (file.size > 2 * 1024 * 1024) {
            this.showMessage('图片大小不能超过2MB', 'error');
            return;
        }

        // 预览图片
        const reader = new FileReader();
        reader.onload = (e) => {
            const preview = document.getElementById('avatarPreview');
            if (preview) {
                preview.src = e.target.result;
            }
        };
        reader.readAsDataURL(file);
    }

    // 选择预设头像
    selectPresetAvatar(avatarPath) {
        const preview = document.getElementById('avatarPreview');
        const presetAvatars = document.querySelectorAll('.preset-avatar');

        if (preview) {
            preview.src = `images/avatar/${avatarPath}`;
        }

        // 更新选中状态
        presetAvatars.forEach(avatar => {
            avatar.classList.remove('selected');
            if (avatar.dataset.avatar === avatarPath) {
                avatar.classList.add('selected');
            }
        });
    }

    // 保存头像
    saveAvatar() {
        const preview = document.getElementById('avatarPreview');
        if (!preview) return;

        const newAvatarSrc = preview.src;

        // 检查是否是base64数据或文件路径
        if (newAvatarSrc.startsWith('data:')) {
            // 如果是base64数据，需要上传到服务器
            this.uploadBase64Avatar(newAvatarSrc);
        } else {
            // 如果是预设头像路径，直接更新
            this.updateUserAvatar(newAvatarSrc);
        }

        // 关闭模态框
        this.closeAvatarModal();
    }

    // 保存签名
    saveSignature() {
        const input = document.getElementById('signatureInput');
        if (!input) return;

        const newSignature = input.value.trim();

        // 更新用户签名
        this.updateUserSignature(newSignature);

        // 关闭模态框
        this.closeSignatureModal();
    }

    // 关闭签名模态框
    closeSignatureModal() {
        const modal = document.querySelector('.signature-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    // 关闭头像模态框
    closeAvatarModal() {
        const modal = document.querySelector('.avatar-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    // 更新用户签名
    updateUserSignature(signature) {
        const currentUser = this.authManager.getCurrentUser();
        if (!currentUser) return;

        // 使用专门的签名更新接口，不会影响头像
        const updateData = {
            phone: currentUser.phone,
            signature: signature
        };

        console.log('🔄 使用专门的签名更新接口:', updateData);

        axios.post('/api/user-detail/update-signature', updateData)
            .then(response => {
                if (response.data.success) {
                    // 更新本地数据
                    if (this.userDetailData) {
                        this.userDetailData.signature = signature;
                        console.log('✅ 签名更新成功，头像完全不受影响');
                    }

                    // 只更新签名显示，不调用完整的updateUserCenterDisplay
                    // 避免触发头像相关的更新逻辑
                    const signatureTextEl = document.querySelector('.signature-text');
                    if (signatureTextEl) {
                        signatureTextEl.textContent = signature || '这个人很懒，什么都没留下~';
                    }

                    this.showMessage('个性签名更新成功', 'success');
                } else {
                    this.showMessage('更新失败: ' + response.data.message, 'error');
                }
            })
            .catch(error => {
                console.error('更新签名失败:', error);
                this.showMessage('网络错误，请重试', 'error');
            });
    }

    // 上传base64头像到服务器
    uploadBase64Avatar(base64Data) {
        const currentUser = this.authManager.getCurrentUser();
        if (!currentUser) return;

        // 将base64转换为Blob
        const byteCharacters = atob(base64Data.split(',')[1]);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: 'image/jpeg' });

        // 创建FormData
        const formData = new FormData();
        formData.append('file', blob, `avatar_${currentUser.phone}_${Date.now()}.jpg`);
        formData.append('phone', currentUser.phone);

        // 上传到服务器
        axios.post('/api/avatar/upload', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
        .then(response => {
            if (response.data.success) {
                // 更新本地数据 - 只存储文件名
                if (this.userDetailData) {
                    this.userDetailData.avatarUrl = response.data.avatar;
                }

                // 更新localStorage中的用户数据
                const currentUser = this.authManager.getCurrentUser();
                if (currentUser) {
                    currentUser.avatarUrl = response.data.avatar;
                    this.authManager.saveUserToStorage(currentUser);
                }

                // 更新页面显示 - 使用完整路径，添加时间戳防止缓存
                this.updateAvatarDisplay(response.data.avatarUrl + `?t=${Date.now()}`);
                this.updateUserCenterDisplay();
                this.showMessage('头像上传成功', 'success');
            } else {
                this.showMessage('上传失败: ' + response.data.message, 'error');
            }
        })
        .catch(error => {
            console.error('头像上传失败:', error);
            this.showMessage('网络错误，请重试', 'error');
        });
    }

    // 更新用户头像
    updateUserAvatar(avatarSrc) {
        const currentUser = this.authManager.getCurrentUser();
        if (!currentUser) return;

        // 提取头像文件名
        let avatarPath = avatarSrc;
        if (avatarSrc.includes('/')) {
            avatarPath = avatarSrc.substring(avatarSrc.lastIndexOf('/') + 1);
        }

        const updateData = {
            phone: currentUser.phone,
            avatarUrl: avatarPath
        };

        axios.post('/api/user-detail/update', updateData)
            .then(response => {
                if (response.data.success) {
                    // 更新本地数据
                    if (this.userDetailData) {
                        this.userDetailData.avatarUrl = avatarPath;
                    }

                    // 更新localStorage中的用户数据
                    const currentUser = this.authManager.getCurrentUser();
                    if (currentUser) {
                        currentUser.avatarUrl = avatarPath;
                        this.authManager.saveUserToStorage(currentUser);
                    }

                    // 更新页面显示的头像，添加时间戳防止缓存
                    this.updateAvatarDisplay(avatarSrc + (avatarSrc.includes('images/avatar/') ? '' : `?t=${Date.now()}`));
                    this.updateUserCenterDisplay();
                    this.showMessage('头像更新成功', 'success');
                } else {
                    this.showMessage('更新失败: ' + response.data.message, 'error');
                }
            })
            .catch(error => {
                console.error('更新头像失败:', error);
                this.showMessage('网络错误，请重试', 'error');
            });
    }

    // 检查头像文件是否存在并更新显示
    checkAndUpdateAvatar(identifier, avatarUrl) {
        console.log('🔍 检查头像:', identifier, avatarUrl);

        // 如果是用户上传的头像，检查文件是否存在
        if (avatarUrl && avatarUrl.startsWith('avatar_')) {
            axios.get(`/api/avatar/check/${identifier}`)
                .then(response => {
                    if (response.data.success) {
                        if (response.data.exists) {
                            // 文件存在，正常显示
                            console.log('✅ 头像文件存在，正常显示:', response.data.avatarUrl);
                            this.updateAvatarDisplay(response.data.avatarUrl);
                        } else {
                            // 文件不存在，已重置为默认头像
                            console.log('⚠️ 头像文件不存在，重置为默认头像');
                            this.userDetailData.avatarUrl = 'default-avatar.jpg';
                            this.updateAvatarDisplay(response.data.avatarUrl);
                            this.showMessage(response.data.message || '头像文件丢失，已重置为默认头像', 'warning');
                        }
                    } else {
                        // 检查失败，保持当前头像，不要重置
                        console.warn('⚠️ 头像检查API失败，保持当前头像:', response.data.message);
                        this.updateAvatarDisplay(avatarUrl); // 使用原始的avatarUrl
                    }
                })
                .catch(error => {
                    console.error('❌ 检查头像文件网络错误:', error);
                    // 网络错误时，保持当前头像，不要重置为默认头像
                    console.log('🔄 网络错误，保持当前头像显示');
                    this.updateAvatarDisplay(avatarUrl); // 使用原始的avatarUrl
                });
        } else {
            // 预设头像或默认头像，直接显示
            console.log('📷 预设头像或默认头像，直接显示:', avatarUrl);
            this.updateAvatarDisplay(avatarUrl);
        }
    }

    // 更新页面中的头像显示
    updateAvatarDisplay(avatarSrc) {
        // 处理头像路径
        let displaySrc = avatarSrc;

        // 如果已经是完整路径（包含images/），直接使用
        if (displaySrc.startsWith('images/')) {
            // 已经是完整路径，直接使用
        } else if (!displaySrc.startsWith('http') && !displaySrc.startsWith('/') && !displaySrc.startsWith('data:')) {
            // 判断是预设头像还是用户上传的头像
            if (displaySrc.startsWith('avatar_')) {
                // 用户上传的头像，保存在avatars文件夹
                displaySrc = `images/avatars/${displaySrc}`;
            } else {
                // 预设头像，保存在avatar文件夹
                displaySrc = `images/avatar/${displaySrc}`;
            }
        }

        // 更新所有可能的头像元素
        const avatarSelectors = [
            '.profile-avatar',
            '.avatar-icon',
            '.user-avatar',
            '.current-avatar',
            '.header-avatar',
            '.nav-avatar'
        ];

        avatarSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                if (element) {
                    element.src = displaySrc;
                }
            });
        });

        console.log('✅ 头像显示已更新:', displaySrc);
    }

    // 显示消息提示
    showMessage(message, type = 'info') {
        const messageEl = document.createElement('div');
        messageEl.className = `user-message ${type}`;
        messageEl.textContent = message;
        messageEl.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 4px;
            color: white;
            z-index: 10001;
            animation: slideInRight 0.3s ease;
        `;

        if (type === 'success') {
            messageEl.style.background = '#4CAF50';
        } else if (type === 'error') {
            messageEl.style.background = '#f44336';
        } else if (type === 'warning') {
            messageEl.style.background = '#ff9800';
        } else {
            messageEl.style.background = '#2196F3';
        }

        document.body.appendChild(messageEl);
        setTimeout(() => messageEl.remove(), 3000);
    }

    // 管理收货地址
    manageAddresses() {
        if (!this.authManager.isLoggedIn()) {
            this.showMessage('请先登录后再管理收货地址', 'warning');
            setTimeout(() => window.location.href = 'land.html', 2000);
            return;
        }

        const currentUser = this.authManager.getCurrentUser();
        const userIdentifier = currentUser.phone || currentUser.userId;

        // 获取用户地址列表
        axios.get(`/api/user-detail/${userIdentifier}/addresses`)
            .then(response => {
                if (response.data.success) {
                    this.showAddressManagementModal(response.data.data);
                } else {
                    this.showMessage('获取地址列表失败', 'error');
                }
            })
            .catch(error => {
                console.error('获取地址列表失败:', error);
                this.showMessage('网络错误，请重试', 'error');
            });
    }

    // 查看所有订单
    viewAllOrders() {
        if (!this.authManager.isLoggedIn()) {
            this.showMessage('请先登录后再查看订单', 'warning');
            setTimeout(() => window.location.href = 'land.html', 2000);
            return;
        }

        const currentUser = this.authManager.getCurrentUser();
        const userIdentifier = currentUser.phone || currentUser.userId;

        // 获取用户订单列表
        axios.get(`/api/order/list/${userIdentifier}`)
            .then(response => {
                console.log('订单API响应:', response.data);
                if (response.data.success) {
                    this.showOrderManagementModal(response.data);
                } else {
                    this.showMessage('获取订单列表失败: ' + (response.data.message || ''), 'error');
                }
            })
            .catch(error => {
                console.error('获取订单列表失败:', error);
                this.showMessage('网络错误，请重试: ' + error.message, 'error');
            });
    }

    // 管理好友
    manageFriends() {
        if (!this.authManager.isLoggedIn()) {
            this.showMessage('请先登录后再管理好友', 'warning');
            setTimeout(() => window.location.href = 'land.html', 2000);
            return;
        }
        this.showMessage('好友管理功能开发中...', 'info');
    }

    // 查看个人资料
    viewProfile() {
        if (!this.authManager.isLoggedIn()) {
            this.showMessage('请先登录后再查看个人资料', 'warning');
            setTimeout(() => window.location.href = 'land.html', 2000);
            return;
        }
        this.showProfileModal();
    }

    // 查看订单
    viewOrders() {
        if (!this.authManager.isLoggedIn()) {
            this.showMessage('请先登录后再查看订单', 'warning');
            setTimeout(() => window.location.href = 'land.html', 2000);
            return;
        }

        const currentUser = this.authManager.getCurrentUser();
        const userIdentifier = currentUser.phone || currentUser.userId;

        // 获取用户订单列表
        axios.get(`/api/order/list/${userIdentifier}`)
            .then(response => {
                console.log('订单API响应:', response.data);
                if (response.data.success) {
                    this.showOrderManagementModal(response.data);
                } else {
                    this.showMessage('获取订单列表失败: ' + (response.data.message || ''), 'error');
                }
            })
            .catch(error => {
                console.error('获取订单列表失败:', error);
                this.showMessage('网络错误，请重试: ' + error.message, 'error');
            });
    }

    // 查看收藏夹
    viewFavorites() {
        if (!this.authManager.isLoggedIn()) {
            this.showMessage('请先登录后再查看收藏', 'warning');
            setTimeout(() => window.location.href = 'land.html', 2000);
            return;
        }

        const modal = document.querySelector('.favorites-modal');
        if (modal) {
            modal.style.display = 'flex';
            if (typeof this.loadUserFavorites === 'function') {
                this.loadUserFavorites();
            } else {
                // 如果方法不存在，直接调用原型上的方法
                window.UserCenter.prototype.loadUserFavorites.call(this);
            }
        }
    }

    // 查看我的评论
    viewComments() {
        if (!this.authManager.isLoggedIn()) {
            this.showMessage('请先登录后再查看评论', 'warning');
            setTimeout(() => window.location.href = 'land.html', 2000);
            return;
        }

        const currentUser = this.authManager.getCurrentUser();
        const userIdentifier = currentUser.phone || currentUser.userId;

        // 获取用户评论列表
        axios.get(`/api/comments/user/${userIdentifier}?page=1&size=10`)
            .then(response => {
                if (response.data.success) {
                    this.showUserCommentsModal(response.data.data || []);
                } else {
                    this.showMessage('获取评论列表失败: ' + (response.data.message || ''), 'error');
                }
            })
            .catch(error => {
                console.error('获取评论列表失败:', error);
                this.showMessage('网络错误，请重试', 'error');
            });
    }

    // 查看我的分享
    viewShares() {
        if (!this.authManager.isLoggedIn()) {
            this.showMessage('请先登录后再查看分享', 'warning');
            setTimeout(() => window.location.href = 'land.html', 2000);
            return;
        }

        const currentUser = this.authManager.getCurrentUser();
        const userIdentifier = currentUser.phone || currentUser.userId;

        // 获取用户分享列表
        axios.get(`/api/shares/user/${userIdentifier}?page=1&size=10`)
            .then(response => {
                if (response.data.success) {
                    this.showUserSharesModal(response.data.data || []);
                } else {
                    this.showMessage('获取分享列表失败: ' + (response.data.message || ''), 'error');
                }
            })
            .catch(error => {
                console.error('获取分享列表失败:', error);
                this.showMessage('网络错误，请重试', 'error');
            });
    }

    // 查看余额
    viewBalance() {
        if (!this.authManager.isLoggedIn()) {
            this.showMessage('请先登录后再查看余额', 'warning');
            setTimeout(() => window.location.href = 'land.html', 2000);
            return;
        }
        this.showBalanceModal();
    }

    // 查看好友
    viewFriends() {
        if (!this.authManager.isLoggedIn()) {
            this.showMessage('请先登录后再查看好友', 'warning');
            setTimeout(() => window.location.href = 'land.html', 2000);
            return;
        }

        const currentUser = this.authManager.getCurrentUser();
        const userIdentifier = currentUser.phone || currentUser.userId;

        // 获取用户好友列表
        axios.get(`/api/user-detail/${userIdentifier}/friends`)
            .then(response => {
                if (response.data.success) {
                    console.log('好友数据:', response.data);
                    this.showFriendsManagementModal(response.data.data || []);
                } else {
                    this.showMessage('获取好友列表失败: ' + (response.data.message || ''), 'error');
                }
            })
            .catch(error => {
                console.error('获取好友列表失败:', error);
                this.showMessage('网络错误，请重试', 'error');
            });
    }

    // 查看优惠券
    viewCoupons() {
        if (!this.authManager.isLoggedIn()) {
            this.showMessage('请先登录后再查看优惠券', 'warning');
            setTimeout(() => window.location.href = 'land.html', 2000);
            return;
        }

        // 模拟优惠券数据（实际项目中应该从后端获取）
        const coupons = [
            {
                id: 1,
                name: '新用户专享',
                discount: 20,
                type: 'percentage',
                minAmount: 100,
                expireDate: '2024-12-31',
                status: 'available'
            },
            {
                id: 2,
                name: '满减优惠',
                discount: 50,
                type: 'amount',
                minAmount: 300,
                expireDate: '2024-11-30',
                status: 'available'
            },
            {
                id: 3,
                name: '生日特惠',
                discount: 15,
                type: 'percentage',
                minAmount: 200,
                expireDate: '2024-10-15',
                status: 'expired'
            }
        ];

        this.showCouponsModal(coupons);
    }

    // 设置
    settings() {
        if (!this.authManager.isLoggedIn()) {
            this.showMessage('请先登录后再进入设置', 'warning');
            setTimeout(() => window.location.href = 'land.html', 2000);
            return;
        }
        this.showSettingsModal();
    }

    // 查看设置（别名方法）
    viewSettings() {
        this.settings();
    }

    // 查看评论
    viewComments() {
        if (!this.authManager.isLoggedIn()) {
            this.showMessage('请先登录后再查看评论', 'warning');
            setTimeout(() => window.location.href = 'land.html', 2000);
            return;
        }
        this.showCommentsModal();
    }

    // 查看分享
    viewShares() {
        if (!this.authManager.isLoggedIn()) {
            this.showMessage('请先登录后再查看分享', 'warning');
            setTimeout(() => window.location.href = 'land.html', 2000);
            return;
        }
        this.showSharesModal();
    }

    // 查看优惠券
    viewCoupons() {
        if (!this.authManager.isLoggedIn()) {
            this.showMessage('请先登录后再查看优惠券', 'warning');
            setTimeout(() => window.location.href = 'land.html', 2000);
            return;
        }
        this.showCouponsModal();
    }

    // 查看所有订单
    viewAllOrders() {
        this.viewOrders();
    }

    // 帮助中心
    helpCenter() {
        this.showMessage('帮助中心功能开发中...', 'info');
    }

    // 意见反馈
    feedback() {
        this.showMessage('意见反馈功能开发中...', 'info');
    }

    // 显示评论模态框
    showCommentsModal() {
        const currentUser = this.authManager.getCurrentUser();
        const userIdentifier = currentUser.phone || currentUser.userId;

        // 获取用户评论列表
        axios.get(`/api/comments/user/${userIdentifier}`)
            .then(response => {
                if (response.data.success) {
                    this.displayCommentsModal(response.data.data || []);
                } else {
                    this.displayCommentsModal([]);
                }
            })
            .catch(error => {
                console.error('获取评论列表失败:', error);
                this.displayCommentsModal([]);
            });
    }

    // 显示分享模态框
    showSharesModal() {
        const currentUser = this.authManager.getCurrentUser();
        const userIdentifier = currentUser.phone || currentUser.userId;

        // 获取用户分享列表
        axios.get(`/api/shares/user/${userIdentifier}`)
            .then(response => {
                if (response.data.success) {
                    this.displaySharesModal(response.data.data || []);
                } else {
                    this.displaySharesModal([]);
                }
            })
            .catch(error => {
                console.error('获取分享列表失败:', error);
                this.displaySharesModal([]);
            });
    }



    // 显示评论列表
    displayCommentsModal(comments) {
        const modal = document.querySelector('.comments-modal');
        if (modal) {
            modal.style.display = 'flex';
            const container = document.getElementById('commentsList');
            if (container) {
                if (comments.length === 0) {
                    container.innerHTML = `
                        <div class="no-comments">
                            <i class="fas fa-comment-dots" style="font-size: 3rem; color: #ddd; margin-bottom: 1rem;"></i>
                            <p>暂无评论</p>
                            <p style="font-size: 14px; color: #999; margin-top: 0.5rem;">快去评论商品吧！</p>
                        </div>
                    `;
                } else {
                    let html = '<div class="comments-grid">';
                    comments.forEach(comment => {
                        html += `
                            <div class="comment-item">
                                <div class="comment-product">
                                    <img src="${comment.productImageUrl || '/images/default-product.svg'}"
                                         alt="${comment.productName}"
                                         onerror="this.src='/images/default-product.svg'">
                                    <div class="comment-product-info">
                                        <h4>${comment.productName}</h4>
                                        <div class="comment-rating">
                                            ${'★'.repeat(comment.rating || 5)}${'☆'.repeat(5 - (comment.rating || 5))}
                                        </div>
                                    </div>
                                </div>
                                <div class="comment-content">
                                    <p>${comment.content}</p>
                                    <div class="comment-time">${new Date(comment.createdTime).toLocaleString()}</div>
                                </div>
                            </div>
                        `;
                    });
                    html += '</div>';
                    container.innerHTML = html;
                }
            }
        }
    }

    // 显示分享列表
    displaySharesModal(shares) {
        const modal = document.querySelector('.shares-modal');
        if (modal) {
            modal.style.display = 'flex';
            const container = document.getElementById('sharesList');
            if (container) {
                if (shares.length === 0) {
                    container.innerHTML = `
                        <div class="no-shares">
                            <i class="fas fa-share-alt" style="font-size: 3rem; color: #ddd; margin-bottom: 1rem;"></i>
                            <p>暂无分享</p>
                            <p style="font-size: 14px; color: #999; margin-top: 0.5rem;">快去分享商品吧！</p>
                        </div>
                    `;
                } else {
                    let html = '<div class="shares-grid">';
                    shares.forEach(share => {
                        html += `
                            <div class="share-item">
                                <div class="share-product">
                                    <img src="${share.productImageUrl || '/images/default-product.svg'}"
                                         alt="${share.productName}"
                                         onerror="this.src='/images/default-product.svg'">
                                    <div class="share-product-info">
                                        <h4>${share.productName}</h4>
                                        <div class="share-platform">分享到: ${share.shareType}</div>
                                    </div>
                                </div>
                                <div class="share-time">${new Date(share.createdTime).toLocaleString()}</div>
                            </div>
                        `;
                    });
                    html += '</div>';
                    container.innerHTML = html;
                }
            }
        }
    }

    // 添加优惠券模态框样式
    addCouponsModalStyles() {
        if (document.getElementById('coupons-modal-styles')) return;

        const style = document.createElement('style');
        style.id = 'coupons-modal-styles';
        style.textContent = `
            .coupons-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                backdrop-filter: blur(5px);
            }
            .coupons-modal .modal-content {
                background: white;
                border-radius: 12px;
                width: 90%;
                max-width: 600px;
                max-height: 80vh;
                overflow: hidden;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            }
            .coupons-modal .modal-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .coupons-modal .modal-body {
                padding: 20px;
                max-height: 60vh;
                overflow-y: auto;
            }
            .no-coupons {
                text-align: center;
                padding: 40px 20px;
                color: #666;
            }
        `;
        document.head.appendChild(style);
    }

    // 显示个人资料模态框
    showProfileModal() {
        const currentUser = this.authManager.getCurrentUser();
        const userIdentifier = currentUser.phone || currentUser.userId;

        // 获取用户详细信息
        axios.get(`/api/user-detail/${userIdentifier}`)
            .then(response => {
                if (response.data.success) {
                    this.displayProfileModal(response.data.data);
                } else {
                    this.showMessage('获取用户信息失败', 'error');
                }
            })
            .catch(error => {
                console.error('获取用户信息失败:', error);
                this.showMessage('网络错误，请重试', 'error');
            });
    }

    // 显示设置模态框
    showSettingsModal() {
        const modal = document.createElement('div');
        modal.className = 'settings-modal';
        modal.innerHTML = `
            <div class="modal-overlay" onclick="window.closeAllModals()"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-cog"></i> 设置</h3>
                    <button class="close-modal" onclick="window.closeAllModals()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="settings-section">
                        <h4>账户设置</h4>
                        <div class="setting-item">
                            <div class="setting-info">
                                <i class="fas fa-key"></i>
                                <span>修改密码</span>
                            </div>
                            <button class="btn-setting" onclick="window.userCenter?.changePassword()">修改</button>
                        </div>
                        <div class="setting-item">
                            <div class="setting-info">
                                <i class="fas fa-bell"></i>
                                <span>消息通知</span>
                            </div>
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                    <div class="settings-section">
                        <h4>隐私设置</h4>
                        <div class="setting-item">
                            <div class="setting-info">
                                <i class="fas fa-eye"></i>
                                <span>个人资料可见性</span>
                            </div>
                            <select class="setting-select">
                                <option value="public">公开</option>
                                <option value="friends">仅好友</option>
                                <option value="private">私密</option>
                            </select>
                        </div>
                    </div>
                    <div class="settings-section">
                        <h4>其他</h4>
                        <div class="setting-item">
                            <div class="setting-info">
                                <i class="fas fa-trash"></i>
                                <span>清除缓存</span>
                            </div>
                            <button class="btn-setting" onclick="window.userCenter?.clearCache()">清除</button>
                        </div>
                        <div class="setting-item">
                            <div class="setting-info">
                                <i class="fas fa-sign-out-alt"></i>
                                <span>退出登录</span>
                            </div>
                            <button class="btn-setting btn-danger" onclick="window.userCenter?.logout()">退出</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加样式
        this.addSettingsModalStyles();
        document.body.appendChild(modal);
    }

    // 显示个人资料详情
    displayProfileModal(userDetail) {
        const modal = document.createElement('div');
        modal.className = 'profile-modal';
        modal.innerHTML = `
            <div class="modal-overlay" onclick="window.closeAllModals()"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-user"></i> 个人资料</h3>
                    <button class="close-modal" onclick="window.closeAllModals()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="profile-section">
                        <div class="avatar-section">
                            <div class="avatar-container">
                                <img src="${userDetail.avatar || '/images/avatars/default-avatar.svg'}"
                                     alt="头像" class="user-avatar">
                                <button class="btn-change-avatar" onclick="window.userCenter?.changeAvatar()">
                                    <i class="fas fa-camera"></i>
                                </button>
                            </div>
                        </div>
                        <div class="info-section">
                            <div class="info-item">
                                <label>用户ID</label>
                                <span>${userDetail.userId || '未设置'}</span>
                            </div>
                            <div class="info-item">
                                <label>手机号</label>
                                <span>${userDetail.phone || '未绑定'}</span>
                            </div>
                            <div class="info-item">
                                <label>个性签名</label>
                                <div class="editable-field">
                                    <input type="text" value="${userDetail.signature || ''}"
                                           placeholder="写点什么介绍自己吧..." id="profileSignatureInput">
                                    <button class="btn-edit" onclick="window.userCenter?.updateSignature()">
                                        <i class="fas fa-save"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="info-item">
                                <label>注册时间</label>
                                <span>${userDetail.createdTime ? new Date(userDetail.createdTime).toLocaleDateString() : '未知'}</span>
                            </div>
                        </div>
                    </div>
                    <div class="stats-section">
                        <div class="stat-item">
                            <div class="stat-value">${userDetail.totalOrderCount || 0}</div>
                            <div class="stat-label">订单数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${userDetail.favoriteCount || 0}</div>
                            <div class="stat-label">收藏数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${userDetail.commentCount || 0}</div>
                            <div class="stat-label">评论数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${userDetail.shareCount || 0}</div>
                            <div class="stat-label">分享数</div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加样式
        this.addProfileModalStyles();
        document.body.appendChild(modal);
    }

    // 更新个性签名
    updateSignature() {
        const input = document.getElementById('profileSignatureInput');
        if (!input) return;

        const newSignature = input.value.trim();
        const currentUser = this.authManager.getCurrentUser();
        const userIdentifier = currentUser.phone || currentUser.userId;

        axios.post('/api/user-detail/update-signature', {
            userId: userIdentifier,
            signature: newSignature
        })
        .then(response => {
            if (response.data.success) {
                this.showMessage('个性签名更新成功', 'success');
            } else {
                this.showMessage('更新失败: ' + response.data.message, 'error');
            }
        })
        .catch(error => {
            console.error('更新个性签名失败:', error);
            this.showMessage('网络错误，请重试', 'error');
        });
    }

    // 修改头像
    changeAvatar() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                this.uploadAvatarFile(file);  // 调用真正的文件上传方法
            }
        };
        input.click();
    }

    // 上传头像文件 - 这个方法用于直接文件上传
    uploadAvatarFile(file) {
        if (!file) {
            this.showMessage('请选择要上传的文件', 'warning');
            return;
        }

        // 检查登录状态
        if (!this.authManager.isLoggedIn()) {
            this.showMessage('请先登录后再更换头像', 'warning');
            setTimeout(() => {
                window.location.href = 'land.html';
            }, 2000);
            return;
        }

        // 验证文件类型
        if (!file.type.startsWith('image/')) {
            this.showMessage('请选择图片文件', 'error');
            return;
        }

        // 验证文件大小 (2MB)
        if (file.size > 2 * 1024 * 1024) {
            this.showMessage('图片大小不能超过2MB', 'error');
            return;
        }

        const formData = new FormData();
        formData.append('file', file);

        const currentUser = this.authManager.getCurrentUser();
        formData.append('phone', currentUser.phone);

        axios.post('/api/avatar/upload', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
        .then(response => {
            if (response.data.success) {
                // 更新本地数据
                if (this.userDetailData) {
                    this.userDetailData.avatarUrl = response.data.avatar;
                }

                // 更新localStorage中的用户数据
                const currentUser = this.authManager.getCurrentUser();
                if (currentUser) {
                    currentUser.avatarUrl = response.data.avatar;
                    this.authManager.saveUserToStorage(currentUser);
                }

                // 更新页面显示
                this.updateAvatarDisplay(response.data.avatarUrl + `?t=${Date.now()}`);
                this.updateUserCenterDisplay();
                this.showMessage('头像上传成功', 'success');
            } else {
                this.showMessage('头像上传失败: ' + response.data.message, 'error');
            }
        })
        .catch(error => {
            console.error('头像上传失败:', error);
            this.showMessage('头像上传失败: ' + (error.response?.data?.message || '网络错误'), 'error');
        });
    }

    // 修改密码
    changePassword() {
        this.showMessage('修改密码功能开发中...', 'info');
    }

    // 清除缓存
    clearCache() {
        if (confirm('确定要清除所有缓存数据吗？')) {
            localStorage.clear();
            sessionStorage.clear();
            this.showMessage('缓存已清除', 'success');
        }
    }

    // 显示余额模态框
    showBalanceModal() {
        const currentUser = this.authManager.getCurrentUser();
        const userIdentifier = currentUser.phone || currentUser.userId;

        // 获取用户余额信息
        axios.get(`/api/user-detail/${userIdentifier}/balance`)
            .then(response => {
                if (response.data.success) {
                    this.displayBalanceModal(response.data.data);
                } else {
                    // 如果没有余额记录，显示默认余额
                    this.displayBalanceModal({ balance: 0, transactions: [] });
                }
            })
            .catch(error => {
                console.error('获取余额信息失败:', error);
                this.displayBalanceModal({ balance: 0, transactions: [] });
            });
    }

    // 显示余额详情模态框
    displayBalanceModal(balanceData) {
        const modal = document.createElement('div');
        modal.className = 'balance-modal';
        modal.innerHTML = `
            <div class="modal-overlay" onclick="window.closeAllModals()"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-wallet"></i> 我的余额</h3>
                    <button class="close-modal" onclick="window.closeAllModals()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="balance-overview">
                        <div class="balance-card">
                            <div class="balance-amount">
                                <span class="currency">¥</span>
                                <span class="amount">${(balanceData.balance || 0).toFixed(2)}</span>
                            </div>
                            <div class="balance-label">当前余额</div>
                        </div>
                        <div class="balance-actions">
                            <button class="btn-recharge" onclick="window.userCenter?.rechargeBalance()">
                                <i class="fas fa-plus"></i> 充值
                            </button>
                            <button class="btn-withdraw" onclick="window.userCenter?.withdrawBalance()">
                                <i class="fas fa-minus"></i> 提现
                            </button>
                        </div>
                    </div>
                    <div class="transaction-history">
                        <h4>交易记录</h4>
                        <div class="transaction-list">
                            ${balanceData.transactions && balanceData.transactions.length > 0 ?
                                balanceData.transactions.map(transaction => `
                                    <div class="transaction-item">
                                        <div class="transaction-info">
                                            <div class="transaction-type">${transaction.type}</div>
                                            <div class="transaction-time">${new Date(transaction.createdTime).toLocaleString()}</div>
                                        </div>
                                        <div class="transaction-amount ${transaction.amount > 0 ? 'positive' : 'negative'}">
                                            ${transaction.amount > 0 ? '+' : ''}¥${Math.abs(transaction.amount).toFixed(2)}
                                        </div>
                                    </div>
                                `).join('') :
                                '<div class="no-transactions">暂无交易记录</div>'
                            }
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加样式
        this.addBalanceModalStyles();
        document.body.appendChild(modal);
    }

    // 显示好友管理模态框
    showFriendsManagementModal(friends) {
        const modal = document.createElement('div');
        modal.className = 'friends-modal';
        modal.innerHTML = `
            <div class="modal-overlay" onclick="window.closeAllModals()"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-users"></i> 我的好友</h3>
                    <button class="close-modal" onclick="window.closeAllModals()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="friends-stats">
                        <div class="stat-card">
                            <div class="stat-value">${friends.length}</div>
                            <div class="stat-label">好友总数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${friends.filter(f => f.isOnline).length || 0}</div>
                            <div class="stat-label">在线好友</div>
                        </div>
                    </div>
                    <div class="friends-actions">
                        <button class="btn-add-friend" onclick="window.userCenter?.addFriend()">
                            <i class="fas fa-user-plus"></i> 添加好友
                        </button>
                    </div>
                    <div class="friends-list">
                        ${friends.length > 0 ?
                            friends.map(friend => `
                                <div class="friend-item">
                                    <div class="friend-avatar">
                                        <img src="${friend.avatar || '/images/avatars/default-avatar.svg'}" alt="好友头像">
                                        <span class="online-status ${friend.isOnline ? 'online' : 'offline'}"></span>
                                    </div>
                                    <div class="friend-info">
                                        <div class="friend-name">${friend.name || '未知用户'}</div>
                                        <div class="friend-status">${friend.isOnline ? '在线' : '离线'}</div>
                                    </div>
                                    <div class="friend-actions">
                                        <button class="btn-chat" onclick="window.userCenter?.chatWithFriend('${friend.id}')">
                                            <i class="fas fa-comment"></i>
                                        </button>
                                        <button class="btn-remove" onclick="window.userCenter?.removeFriend('${friend.id}')">
                                            <i class="fas fa-user-minus"></i>
                                        </button>
                                    </div>
                                </div>
                            `).join('') :
                            '<div class="no-friends">暂无好友，快去添加好友吧！</div>'
                        }
                    </div>
                </div>
            </div>
        `;

        // 添加样式
        this.addFriendsModalStyles();
        document.body.appendChild(modal);
    }

    // 充值余额
    rechargeBalance() {
        this.showMessage('充值功能开发中...', 'info');
    }

    // 提现余额
    withdrawBalance() {
        this.showMessage('提现功能开发中...', 'info');
    }

    // 添加好友
    addFriend() {
        this.showMessage('添加好友功能开发中...', 'info');
    }

    // 与好友聊天
    chatWithFriend(friendId) {
        this.showMessage('聊天功能开发中...', 'info');
    }

    // 删除好友
    removeFriend(friendId) {
        if (confirm('确定要删除这个好友吗？')) {
            this.showMessage('删除好友功能开发中...', 'info');
        }
    }

    // 显示优惠券模态框
    showCouponsModal(coupons = []) {
        const availableCoupons = coupons.filter(c => c.status === 'available');
        const expiredCoupons = coupons.filter(c => c.status === 'expired');

        const modal = document.createElement('div');
        modal.className = 'coupons-modal';
        modal.innerHTML = `
            <div class="modal-overlay" onclick="window.closeAllModals()"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-ticket-alt"></i> 我的优惠券</h3>
                    <button class="close-modal" onclick="window.closeAllModals()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="coupons-stats">
                        <div class="stat-card">
                            <div class="stat-value">${availableCoupons.length}</div>
                            <div class="stat-label">可用优惠券</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${expiredCoupons.length}</div>
                            <div class="stat-label">已过期</div>
                        </div>
                    </div>

                    <div class="coupons-tabs">
                        <button class="tab-btn active" onclick="showCouponTab('available')">可用优惠券</button>
                        <button class="tab-btn" onclick="showCouponTab('expired')">已过期</button>
                    </div>

                    <div class="coupons-list" id="available-coupons">
                        ${availableCoupons.length > 0 ?
                            availableCoupons.map(coupon => `
                                <div class="coupon-item available">
                                    <div class="coupon-left">
                                        <div class="coupon-discount">
                                            ${coupon.type === 'percentage' ?
                                                `${coupon.discount}%` :
                                                `¥${coupon.discount}`
                                            }
                                        </div>
                                        <div class="coupon-type">
                                            ${coupon.type === 'percentage' ? '折扣' : '立减'}
                                        </div>
                                    </div>
                                    <div class="coupon-info">
                                        <div class="coupon-name">${coupon.name}</div>
                                        <div class="coupon-condition">满¥${coupon.minAmount}可用</div>
                                        <div class="coupon-expire">有效期至：${coupon.expireDate}</div>
                                    </div>
                                    <div class="coupon-action">
                                        <button class="btn-use-coupon" onclick="useCoupon(${coupon.id})">立即使用</button>
                                    </div>
                                </div>
                            `).join('') :
                            '<div class="no-coupons">暂无可用优惠券</div>'
                        }
                    </div>

                    <div class="coupons-list" id="expired-coupons" style="display: none;">
                        ${expiredCoupons.length > 0 ?
                            expiredCoupons.map(coupon => `
                                <div class="coupon-item expired">
                                    <div class="coupon-left">
                                        <div class="coupon-discount">
                                            ${coupon.type === 'percentage' ?
                                                `${coupon.discount}%` :
                                                `¥${coupon.discount}`
                                            }
                                        </div>
                                        <div class="coupon-type">
                                            ${coupon.type === 'percentage' ? '折扣' : '立减'}
                                        </div>
                                    </div>
                                    <div class="coupon-info">
                                        <div class="coupon-name">${coupon.name}</div>
                                        <div class="coupon-condition">满¥${coupon.minAmount}可用</div>
                                        <div class="coupon-expire">已于${coupon.expireDate}过期</div>
                                    </div>
                                    <div class="coupon-action">
                                        <span class="expired-label">已过期</span>
                                    </div>
                                </div>
                            `).join('') :
                            '<div class="no-coupons">暂无过期优惠券</div>'
                        }
                    </div>
                </div>
            </div>
        `;

        // 添加样式
        addCouponsModalStyles();
        document.body.appendChild(modal);

        // 添加全局函数
        window.showCouponTab = function(tab) {
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.coupons-list').forEach(list => list.style.display = 'none');

            event.target.classList.add('active');
            document.getElementById(tab + '-coupons').style.display = 'block';
        };

        window.useCoupon = function(couponId) {
            alert('优惠券使用功能开发中...');
        };
    }

    // 显示用户评论模态框
    showUserCommentsModal(comments) {
        const modal = document.createElement('div');
        modal.className = 'user-comments-modal';
        modal.innerHTML = `
            <div class="modal-overlay" onclick="window.closeAllModals()"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-comment"></i> 我的评论</h3>
                    <button class="close-modal" onclick="window.closeAllModals()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="comments-stats">
                        <div class="stat-card">
                            <div class="stat-value">${comments.length}</div>
                            <div class="stat-label">评论总数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${comments.filter(c => c.rating >= 4).length}</div>
                            <div class="stat-label">好评数</div>
                        </div>
                    </div>
                    <div class="comments-list">
                        ${comments.length > 0 ?
                            comments.map(comment => `
                                <div class="comment-item">
                                    <div class="comment-header">
                                        <div class="product-info">
                                            <img src="${comment.productImageUrl || '/images/default-product.svg'}"
                                                 alt="商品图片" class="product-image">
                                            <div class="product-details">
                                                <div class="product-name">${comment.productName || '未知商品'}</div>
                                                <div class="comment-rating">
                                                    ${'★'.repeat(comment.rating)}${'☆'.repeat(5 - comment.rating)}
                                                </div>
                                            </div>
                                        </div>
                                        <span class="comment-time">${new Date(comment.createdTime).toLocaleDateString()}</span>
                                    </div>
                                    <div class="comment-content">
                                        <p>${comment.content}</p>
                                        ${comment.images ? `
                                            <div class="comment-images">
                                                ${comment.images.split(',').map(img => `
                                                    <img src="${img}" alt="评论图片" class="comment-image">
                                                `).join('')}
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>
                            `).join('') :
                            '<div class="no-comments">暂无评论记录</div>'
                        }
                    </div>
                </div>
            </div>
        `;

        // 添加样式
        addUserCommentsModalStyles();
        document.body.appendChild(modal);
    }

    // 显示用户分享模态框
    showUserSharesModal(shares) {
        const modal = document.createElement('div');
        modal.className = 'user-shares-modal';
        modal.innerHTML = `
            <div class="modal-overlay" onclick="window.closeAllModals()"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-share"></i> 我的分享</h3>
                    <button class="close-modal" onclick="window.closeAllModals()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="shares-stats">
                        <div class="stat-card">
                            <div class="stat-value">${shares.length}</div>
                            <div class="stat-label">分享总数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${new Set(shares.map(s => s.shareType)).size}</div>
                            <div class="stat-label">分享平台</div>
                        </div>
                    </div>
                    <div class="shares-list">
                        ${shares.length > 0 ?
                            shares.map(share => `
                                <div class="share-item">
                                    <div class="share-header">
                                        <div class="product-info">
                                            <img src="${share.productImageUrl || '/images/default-product.svg'}"
                                                 alt="商品图片" class="product-image">
                                            <div class="product-details">
                                                <div class="product-name">${share.productName || '未知商品'}</div>
                                                <div class="share-type">
                                                    <i class="fas fa-share-alt"></i>
                                                    ${this.getShareTypeText(share.shareType)}
                                                </div>
                                            </div>
                                        </div>
                                        <span class="share-time">${new Date(share.createdTime).toLocaleDateString()}</span>
                                    </div>
                                    <div class="share-content">
                                        <p>${share.content || '分享了这个商品'}</p>
                                    </div>
                                </div>
                            `).join('') :
                            '<div class="no-shares">暂无分享记录</div>'
                        }
                    </div>
                </div>
            </div>
        `;

        // 添加样式
        addUserSharesModalStyles();
        document.body.appendChild(modal);
    }

    // 获取分享类型文本
    getShareTypeText(shareType) {
        const types = {
            'wechat': '微信',
            'weibo': '微博',
            'qq': 'QQ',
            'link': '复制链接'
        };
        return types[shareType] || shareType;
    }

    // 添加个人资料模态框样式
    addProfileModalStyles() {
        if (document.getElementById('profile-modal-styles')) return;

        const style = document.createElement('style');
        style.id = 'profile-modal-styles';
        style.textContent = `
            .profile-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.6);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 15000;
                backdrop-filter: blur(5px);
            }
            .profile-modal .modal-content {
                background: white;
                border-radius: 20px;
                width: 90%;
                max-width: 600px;
                max-height: 80vh;
                overflow: hidden;
                box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            }
            .profile-modal .modal-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .profile-modal .modal-body {
                padding: 30px;
                max-height: 60vh;
                overflow-y: auto;
            }
            .profile-section {
                display: flex;
                gap: 30px;
                margin-bottom: 30px;
            }
            .avatar-section {
                flex-shrink: 0;
            }
            .avatar-container {
                position: relative;
                width: 120px;
                height: 120px;
            }
            .user-avatar {
                width: 100%;
                height: 100%;
                border-radius: 50%;
                object-fit: cover;
                border: 4px solid #f0f0f0;
            }
            .btn-change-avatar {
                position: absolute;
                bottom: 0;
                right: 0;
                width: 36px;
                height: 36px;
                border-radius: 50%;
                background: #667eea;
                color: white;
                border: none;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            }
            .info-section {
                flex: 1;
            }
            .info-item {
                margin-bottom: 20px;
            }
            .info-item label {
                display: block;
                font-weight: 600;
                color: #333;
                margin-bottom: 8px;
            }
            .info-item span {
                color: #666;
                font-size: 14px;
            }
            .editable-field {
                display: flex;
                gap: 10px;
                align-items: center;
            }
            .editable-field input {
                flex: 1;
                padding: 8px 12px;
                border: 1px solid #ddd;
                border-radius: 8px;
                font-size: 14px;
            }
            .btn-edit {
                padding: 8px 12px;
                background: #667eea;
                color: white;
                border: none;
                border-radius: 8px;
                cursor: pointer;
            }
            .stats-section {
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 20px;
                padding: 20px;
                background: #f8f9fa;
                border-radius: 12px;
            }
            .stat-item {
                text-align: center;
            }
            .stat-value {
                font-size: 24px;
                font-weight: 600;
                color: #667eea;
                margin-bottom: 5px;
            }
            .stat-label {
                font-size: 12px;
                color: #666;
            }
        `;
        document.head.appendChild(style);
    }

    // 添加设置模态框样式
    addSettingsModalStyles() {
        if (document.getElementById('settings-modal-styles')) return;

        const style = document.createElement('style');
        style.id = 'settings-modal-styles';
        style.textContent = `
            .settings-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.6);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 15000;
                backdrop-filter: blur(5px);
            }
            .settings-modal .modal-content {
                background: white;
                border-radius: 20px;
                width: 90%;
                max-width: 500px;
                max-height: 80vh;
                overflow: hidden;
                box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            }
            .settings-modal .modal-header {
                background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                color: white;
                padding: 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .settings-modal .modal-body {
                padding: 20px;
                max-height: 60vh;
                overflow-y: auto;
            }
            .settings-section {
                margin-bottom: 30px;
            }
            .settings-section h4 {
                color: #333;
                margin-bottom: 15px;
                font-size: 16px;
                border-bottom: 2px solid #f0f0f0;
                padding-bottom: 8px;
            }
            .setting-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 15px 0;
                border-bottom: 1px solid #f0f0f0;
            }
            .setting-info {
                display: flex;
                align-items: center;
                gap: 12px;
            }
            .setting-info i {
                width: 20px;
                color: #666;
            }
            .btn-setting {
                padding: 6px 12px;
                border: 1px solid #ddd;
                border-radius: 6px;
                background: white;
                cursor: pointer;
                font-size: 12px;
            }
            .btn-setting.btn-danger {
                background: #ff4757;
                color: white;
                border-color: #ff4757;
            }
            .switch {
                position: relative;
                display: inline-block;
                width: 50px;
                height: 24px;
            }
            .switch input {
                opacity: 0;
                width: 0;
                height: 0;
            }
            .slider {
                position: absolute;
                cursor: pointer;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: #ccc;
                transition: .4s;
                border-radius: 24px;
            }
            .slider:before {
                position: absolute;
                content: "";
                height: 18px;
                width: 18px;
                left: 3px;
                bottom: 3px;
                background-color: white;
                transition: .4s;
                border-radius: 50%;
            }
            input:checked + .slider {
                background-color: #667eea;
            }
            input:checked + .slider:before {
                transform: translateX(26px);
            }
            .setting-select {
                padding: 6px 12px;
                border: 1px solid #ddd;
                border-radius: 6px;
                background: white;
                font-size: 12px;
            }
        `;
        document.head.appendChild(style);
    }

    // 添加余额模态框样式
    addBalanceModalStyles() {
        if (document.getElementById('balance-modal-styles')) return;

        const style = document.createElement('style');
        style.id = 'balance-modal-styles';
        style.textContent = `
            .balance-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.6);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 15000;
                backdrop-filter: blur(5px);
            }
            .balance-modal .modal-content {
                background: white;
                border-radius: 20px;
                width: 90%;
                max-width: 500px;
                max-height: 80vh;
                overflow: hidden;
                box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            }
            .balance-modal .modal-header {
                background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
                color: white;
                padding: 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .balance-overview {
                text-align: center;
                padding: 30px 20px;
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            }
            .balance-card {
                margin-bottom: 20px;
            }
            .balance-amount {
                font-size: 2.5rem;
                font-weight: 600;
                color: #4CAF50;
                margin-bottom: 10px;
            }
            .currency {
                font-size: 1.5rem;
                margin-right: 5px;
            }
            .balance-label {
                color: #666;
                font-size: 14px;
            }
            .balance-actions {
                display: flex;
                gap: 15px;
                justify-content: center;
            }
            .btn-recharge, .btn-withdraw {
                padding: 10px 20px;
                border: none;
                border-radius: 25px;
                cursor: pointer;
                font-weight: 500;
                transition: all 0.3s ease;
            }
            .btn-recharge {
                background: #4CAF50;
                color: white;
            }
            .btn-withdraw {
                background: #ff9800;
                color: white;
            }
            .transaction-history {
                padding: 20px;
            }
            .transaction-history h4 {
                margin-bottom: 15px;
                color: #333;
            }
            .transaction-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px 0;
                border-bottom: 1px solid #f0f0f0;
            }
            .transaction-amount.positive {
                color: #4CAF50;
            }
            .transaction-amount.negative {
                color: #f44336;
            }
            .no-transactions {
                text-align: center;
                color: #999;
                padding: 40px 0;
            }
        `;
        document.head.appendChild(style);
    }

    // 添加好友模态框样式
    addFriendsModalStyles() {
        if (document.getElementById('friends-modal-styles')) return;

        const style = document.createElement('style');
        style.id = 'friends-modal-styles';
        style.textContent = `
            .friends-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.6);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 15000;
                backdrop-filter: blur(5px);
            }
            .friends-modal .modal-content {
                background: white;
                border-radius: 20px;
                width: 90%;
                max-width: 600px;
                max-height: 80vh;
                overflow: hidden;
                box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            }
            .friends-modal .modal-header {
                background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
                color: white;
                padding: 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .friends-stats {
                display: flex;
                gap: 20px;
                padding: 20px;
                background: #f8f9fa;
            }
            .friends-stats .stat-card {
                flex: 1;
                text-align: center;
                padding: 15px;
                background: white;
                border-radius: 10px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .friends-stats .stat-value {
                font-size: 24px;
                font-weight: 600;
                color: #2196F3;
                margin-bottom: 5px;
            }
            .friends-stats .stat-label {
                font-size: 12px;
                color: #666;
            }
            .friends-actions {
                padding: 20px;
                text-align: center;
                border-bottom: 1px solid #f0f0f0;
            }
            .btn-add-friend {
                padding: 10px 20px;
                background: #2196F3;
                color: white;
                border: none;
                border-radius: 25px;
                cursor: pointer;
                font-weight: 500;
            }
            .friends-list {
                max-height: 300px;
                overflow-y: auto;
                padding: 20px;
            }
            .friend-item {
                display: flex;
                align-items: center;
                gap: 15px;
                padding: 15px 0;
                border-bottom: 1px solid #f0f0f0;
            }
            .friend-avatar {
                position: relative;
                width: 50px;
                height: 50px;
            }
            .friend-avatar img {
                width: 100%;
                height: 100%;
                border-radius: 50%;
                object-fit: cover;
            }
            .online-status {
                position: absolute;
                bottom: 2px;
                right: 2px;
                width: 12px;
                height: 12px;
                border-radius: 50%;
                border: 2px solid white;
            }
            .online-status.online {
                background: #4CAF50;
            }
            .online-status.offline {
                background: #999;
            }
            .friend-info {
                flex: 1;
            }
            .friend-name {
                font-weight: 500;
                margin-bottom: 5px;
            }
            .friend-status {
                font-size: 12px;
                color: #666;
            }
            .friend-actions {
                display: flex;
                gap: 10px;
            }
            .btn-chat, .btn-remove {
                width: 32px;
                height: 32px;
                border: none;
                border-radius: 50%;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .btn-chat {
                background: #4CAF50;
                color: white;
            }
            .btn-remove {
                background: #f44336;
                color: white;
            }
            .no-friends {
                text-align: center;
                color: #999;
                padding: 40px 0;
            }
        `;
        document.head.appendChild(style);
    }

    // 查看购物车
    viewCart() {
        if (!this.authManager.isLoggedIn()) {
            this.showMessage('请先登录后再查看购物车', 'warning');
            setTimeout(() => window.location.href = 'land.html', 2000);
            return;
        }

        // 关闭用户中心模态框
        const modal = document.querySelector('.user-center-modal');
        if (modal) {
            modal.classList.remove('show');
            modal.style.display = 'none';
        }

        // 打开购物车模态框
        if (window.shoppingCart) {
            window.shoppingCart.toggleCart(true);
        } else {
            this.showMessage('购物车功能暂时不可用', 'error');
        }
    }

    // 查看好友
    viewFriends() {
        if (!this.authManager.isLoggedIn()) {
            this.showMessage('请先登录后再查看好友', 'warning');
            setTimeout(() => window.location.href = 'land.html', 2000);
            return;
        }

        const currentUser = this.authManager.getCurrentUser();
        const userIdentifier = currentUser.phone || currentUser.userId;

        // 获取用户好友列表
        axios.get(`/api/user-detail/${userIdentifier}/friends`)
            .then(response => {
                if (response.data.success) {
                    console.log('好友数据:', response.data);
                    this.showFriendsManagementModal(response.data.data || []);
                } else {
                    this.showMessage('获取好友列表失败: ' + (response.data.message || ''), 'error');
                }
            })
            .catch(error => {
                console.error('获取好友列表失败:', error);
                this.showMessage('网络错误，请重试', 'error');
            });
    }

    // 显示地址管理模态框
    showAddressManagementModal(addresses) {
        const modal = document.createElement('div');
        modal.className = 'address-management-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>收货地址管理</h3>
                    <button class="close-modal">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="address-list">
                        ${addresses.map(addr => `
                            <div class="address-item ${addr.isDefault ? 'default' : ''}">
                                <div class="address-info">
                                    <div class="receiver-info">
                                        <span class="receiver-name">${addr.receiverName}</span>
                                        <span class="receiver-phone">${addr.receiverPhone}</span>
                                        ${addr.isDefault ? '<span class="default-tag">默认</span>' : ''}
                                    </div>
                                    <div class="address-detail">
                                        ${addr.province} ${addr.city} ${addr.district} ${addr.detailAddress}
                                    </div>
                                </div>
                                <div class="address-actions">
                                    <button class="btn-edit" onclick="editAddress(${addr.id})">编辑</button>
                                    <button class="btn-delete" onclick="deleteAddress(${addr.id})">删除</button>
                                    ${!addr.isDefault ? `<button class="btn-default" onclick="setDefaultAddress(${addr.id})">设为默认</button>` : ''}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                    <button class="btn-add-address" onclick="addNewAddress()">+ 添加新地址</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 添加关闭事件
        modal.querySelector('.close-modal').addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    // 显示订单管理模态框
    showOrderManagementModal(orderData) {
        const modal = document.createElement('div');
        modal.className = 'order-management-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <div class="header-content">
                        <div class="header-left">
                            <div class="header-icon">
                                <i class="fas fa-shopping-bag"></i>
                            </div>
                            <div class="header-text">
                                <h3>我的订单</h3>
                                <p>管理您的购买记录</p>
                            </div>
                        </div>
                        <button class="close-modal">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="modal-body">
                    <div class="order-stats">
                        <div class="stat-card total">
                            <div class="stat-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-value">${orderData.pagination ? orderData.pagination.total : (orderData.data ? orderData.data.length : 0)}</div>
                                <div class="stat-label">总订单数</div>
                            </div>
                        </div>
                        <div class="stat-card pending">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-value">${orderData.data ? orderData.data.filter(o => o.status === 1 || o.status === '1').length : 0}</div>
                                <div class="stat-label">待付款</div>
                            </div>
                        </div>
                        <div class="stat-card processing">
                            <div class="stat-icon">
                                <i class="fas fa-truck"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-value">${orderData.data ? orderData.data.filter(o => o.status === 2 || o.status === '2').length : 0}</div>
                                <div class="stat-label">待发货</div>
                            </div>
                        </div>
                        <div class="stat-card completed">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-value">${orderData.data ? orderData.data.filter(o => o.status === 4 || o.status === '4').length : 0}</div>
                                <div class="stat-label">已完成</div>
                            </div>
                        </div>
                    </div>
                    <div class="order-list">
                        ${orderData.data && orderData.data.length > 0 ?
                            orderData.data.map(order => `
                                <div class="order-item">
                                    <div class="order-header">
                                        <div class="order-info-left">
                                            <div class="order-no">
                                                <i class="fas fa-receipt"></i>
                                                <span class="order-label">订单号:</span>
                                                <span class="order-value">${order.order_no || order.orderNo || '未知'}</span>
                                            </div>
                                            <div class="order-time">
                                                <i class="fas fa-calendar-alt"></i>
                                                <span class="time-value">${order.created_time ? new Date(order.created_time).toLocaleString() : (order.createdTime ? new Date(order.createdTime).toLocaleString() : '未知时间')}</span>
                                            </div>
                                        </div>
                                        <div class="order-status-badge">
                                            <span class="order-status status-${order.status || 0}">${this.getOrderStatusText(order.status || 0)}</span>
                                        </div>
                                    </div>
                                    <div class="order-content">
                                        <div class="order-details">
                                            <div class="order-amount-section">
                                                <div class="amount-label">订单金额</div>
                                                <div class="amount-value">¥${order.total_amount || order.totalAmount || '0.00'}</div>
                                            </div>
                                            <div class="order-receiver-section">
                                                <div class="receiver-label">收货信息</div>
                                                <div class="receiver-info">
                                                    <div class="receiver-name">${order.receiver_name || order.receiverName || '未知'}</div>
                                                    <div class="receiver-phone">${order.receiver_phone || order.receiverPhone || '未知'}</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="order-actions">
                                            <button class="btn-action btn-view" onclick="viewOrderDetail('${order.order_no || order.orderNo}')">
                                                <i class="fas fa-eye"></i>
                                                <span>查看详情</span>
                                            </button>
                                            ${(order.status === 1 || order.status === '1') ?
                                                `<button class="btn-action btn-pay" onclick="payOrder('${order.order_no || order.orderNo}')">
                                                    <i class="fas fa-credit-card"></i>
                                                    <span>立即支付</span>
                                                </button>` : ''}
                                            ${(order.status === 1 || order.status === 2 || order.status === '1' || order.status === '2') ?
                                                `<button class="btn-action btn-cancel" onclick="cancelOrder('${order.order_no || order.orderNo}')">
                                                    <i class="fas fa-times"></i>
                                                    <span>取消订单</span>
                                                </button>` : ''}
                                            ${(order.status === 3 || order.status === '3') ?
                                                `<button class="btn-action btn-confirm" onclick="confirmReceived('${order.order_no || order.orderNo}')">
                                                    <i class="fas fa-check"></i>
                                                    <span>确认收货</span>
                                                </button>` : ''}
                                            ${(order.status === 4 || order.status === 5 || order.status === '4' || order.status === '5') ?
                                                `<button class="btn-action btn-delete" onclick="deleteOrder('${order.order_no || order.orderNo}')">
                                                    <i class="fas fa-trash"></i>
                                                    <span>删除订单</span>
                                                </button>` : ''}
                                        </div>
                                    </div>
                                </div>
                            `).join('') :
                            `<div class="no-orders">
                                <div class="no-orders-illustration">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <div class="no-orders-text">暂无订单记录</div>
                                <div class="no-orders-tip">快去购买心仪的商品吧！</div>
                                <button class="btn-shop-now" onclick="closeOrderModal()">
                                    <i class="fas fa-shopping-bag"></i>
                                    <span>立即购物</span>
                                </button>
                            </div>`
                        }
                    </div>
                </div>
            </div>
        `;

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .order-management-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.4);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                backdrop-filter: blur(3px);
            }
            .modal-content {
                background: linear-gradient(135deg, #f0fdfa 0%, #ecfeff 100%);
                border-radius: 24px;
                width: 95%;
                max-width: 950px;
                max-height: 88vh;
                overflow: hidden;
                box-shadow: 0 25px 80px rgba(6, 182, 212, 0.15);
                animation: modalSlideIn 0.4s ease-out;
                border: 1px solid rgba(6, 182, 212, 0.1);
            }
            @keyframes modalSlideIn {
                from {
                    opacity: 0;
                    transform: translateY(-30px) scale(0.95);
                }
                to {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
            }
            .modal-header {
                background: linear-gradient(135deg, #67e8f9 0%, #06b6d4 100%);
                color: white;
                padding: 25px 30px;
                position: relative;
            }
            .header-content {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .header-left {
                display: flex;
                align-items: center;
                gap: 20px;
            }
            .header-icon {
                width: 55px;
                height: 55px;
                background: rgba(255, 255, 255, 0.15);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.6rem;
                border: 2px solid rgba(255, 255, 255, 0.2);
            }
            .header-text h3 {
                margin: 0;
                font-size: 1.6rem;
                font-weight: 500;
                letter-spacing: 0.5px;
            }
            .header-text p {
                margin: 5px 0 0 0;
                opacity: 0.85;
                font-size: 0.9rem;
                font-weight: 300;
            }
            .close-modal {
                background: rgba(255, 255, 255, 0.15);
                border: none;
                color: white;
                font-size: 1.1rem;
                cursor: pointer;
                padding: 12px;
                border-radius: 50%;
                width: 44px;
                height: 44px;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;
                border: 2px solid rgba(255, 255, 255, 0.2);
                position: absolute;
                top: 20px;
                right: 20px;
            }
            .close-modal:hover {
                background: rgba(255, 255, 255, 0.25);
                transform: scale(1.1);
            }
            .modal-body {
                padding: 30px;
                max-height: 65vh;
                overflow-y: auto;
                background: linear-gradient(135deg, #f8fdff 0%, #f0f9ff 100%);
            }
            .order-stats {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 18px;
                margin-bottom: 30px;
            }
            .stat-card {
                padding: 22px;
                border-radius: 16px;
                display: flex;
                align-items: center;
                gap: 18px;
                transition: all 0.3s ease;
                cursor: pointer;
                border: 1px solid rgba(59, 130, 246, 0.1);
            }
            .stat-card:hover {
                transform: translateY(-4px);
                box-shadow: 0 12px 35px rgba(59, 130, 246, 0.12);
            }
            .stat-card.total {
                background: linear-gradient(135deg, #cffafe 0%, #a7f3d0 100%);
                color: #0f766e;
            }
            .stat-card.pending {
                background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
                color: #d97706;
            }
            .stat-card.processing {
                background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
                color: #0369a1;
            }
            .stat-card.completed {
                background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
                color: #166534;
            }
            .stat-icon {
                width: 48px;
                height: 48px;
                background: rgba(255, 255, 255, 0.6);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.4rem;
                border: 2px solid rgba(255, 255, 255, 0.8);
            }
            .stat-value {
                font-size: 1.8rem;
                font-weight: 600;
                margin-bottom: 4px;
            }
            .stat-label {
                font-size: 0.85rem;
                opacity: 0.8;
                font-weight: 500;
            }
            .order-list {
                display: flex;
                flex-direction: column;
                gap: 18px;
            }
            .order-item {
                border: 1px solid rgba(6, 182, 212, 0.15);
                border-radius: 20px;
                overflow: hidden;
                transition: all 0.3s ease;
                background: white;
                box-shadow: 0 4px 20px rgba(6, 182, 212, 0.08);
                margin-bottom: 20px;
            }
            .order-item:hover {
                border-color: rgba(6, 182, 212, 0.3);
                box-shadow: 0 8px 30px rgba(6, 182, 212, 0.15);
                transform: translateY(-3px);
            }
            .order-header {
                background: linear-gradient(135deg, #f0fdfa 0%, #e6fffa 100%);
                padding: 20px 26px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: 1px solid rgba(6, 182, 212, 0.1);
            }
            .order-info-left {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }
            .order-no, .order-time {
                display: flex;
                align-items: center;
                gap: 8px;
            }
            .order-no i, .order-time i {
                color: #06b6d4;
                width: 14px;
                font-size: 0.9rem;
            }
            .order-label {
                font-weight: 500;
                color: #64748b;
                font-size: 0.85rem;
            }
            .order-value {
                font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
                font-weight: 600;
                color: #1e293b;
                font-size: 0.9rem;
            }
            .time-value {
                color: #64748b;
                font-size: 0.8rem;
                font-weight: 400;
            }
            .order-status-badge {
                display: flex;
                align-items: center;
            }
            .order-status {
                padding: 6px 14px;
                border-radius: 20px;
                font-size: 0.8rem;
                font-weight: 500;
                letter-spacing: 0.3px;
                border: 1px solid rgba(255, 255, 255, 0.3);
            }
            .status-1 {
                background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
                color: #d97706;
                border-color: #f59e0b;
            }
            .status-2 {
                background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
                color: #1d4ed8;
                border-color: #3b82f6;
            }
            .status-3 {
                background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
                color: #047857;
                border-color: #10b981;
            }
            .status-4 {
                background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
                color: #4338ca;
                border-color: #6366f1;
            }
            .status-5 {
                background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%);
                color: #dc2626;
                border-color: #ef4444;
            }
            .order-content {
                padding: 22px;
            }
            .order-details {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
                margin-bottom: 22px;
            }
            .order-amount-section, .order-receiver-section {
                padding: 20px;
                background: linear-gradient(135deg, #f0fdfa 0%, #ecfeff 100%);
                border-radius: 14px;
                border-left: 4px solid #06b6d4;
                border: 1px solid rgba(6, 182, 212, 0.1);
            }
            .amount-label, .receiver-label {
                font-size: 0.8rem;
                color: #64748b;
                margin-bottom: 8px;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                font-weight: 600;
            }
            .amount-value {
                font-size: 1.6rem;
                font-weight: 700;
                color: #dc2626;
                font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
            }
            .receiver-info {
                display: flex;
                flex-direction: column;
                gap: 4px;
            }
            .receiver-name {
                font-weight: 600;
                color: #1e293b;
                font-size: 1rem;
            }
            .receiver-phone {
                color: #64748b;
                font-size: 0.85rem;
                font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
            }
            .order-actions {
                display: flex;
                gap: 10px;
                flex-wrap: wrap;
            }
            .btn-action {
                padding: 10px 18px;
                border: none;
                border-radius: 10px;
                cursor: pointer;
                font-size: 0.85rem;
                font-weight: 500;
                display: flex;
                align-items: center;
                gap: 6px;
                transition: all 0.3s ease;
                text-decoration: none;
                border: 1px solid transparent;
            }
            .btn-action:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
            }
            .btn-view {
                background: linear-gradient(135deg, #64748b 0%, #475569 100%);
                color: white;
            }
            .btn-pay {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                color: white;
            }
            .btn-cancel {
                background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                color: white;
            }
            .no-orders {
                text-align: center;
                padding: 60px 20px;
                color: #64748b;
            }
            .no-orders-illustration {
                font-size: 4rem;
                margin-bottom: 24px;
                opacity: 0.4;
                color: #3b82f6;
            }
            .no-orders-text {
                font-size: 1.3rem;
                font-weight: 500;
                margin-bottom: 12px;
                color: #1e293b;
            }
            .no-orders-tip {
                color: #64748b;
                font-size: 0.95rem;
                margin-bottom: 24px;
            }
            .btn-shop-now {
                background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 20px;
                font-size: 0.9rem;
                font-weight: 500;
                cursor: pointer;
                display: inline-flex;
                align-items: center;
                gap: 8px;
                transition: all 0.3s ease;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
            .btn-shop-now:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(6, 182, 212, 0.3);
            }
            @media (max-width: 768px) {
                .modal-content {
                    width: 98%;
                    margin: 10px;
                    border-radius: 20px;
                }
                .modal-body {
                    padding: 20px;
                }
                .order-stats {
                    grid-template-columns: 1fr 1fr;
                    gap: 12px;
                }
                .order-details {
                    grid-template-columns: 1fr;
                    gap: 15px;
                }
                .order-header {
                    flex-direction: column;
                    gap: 12px;
                    align-items: flex-start;
                    padding: 15px 18px;
                }
                .close-modal {
                    top: 15px;
                    right: 15px;
                    width: 40px;
                    height: 40px;
                }
            }
        `;
        document.head.appendChild(style);

        document.body.appendChild(modal);

        // 添加关闭事件
        modal.querySelector('.close-modal').addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });

        // 添加全局关闭函数
        window.closeOrderModal = () => {
            if (modal && modal.parentNode) {
                document.body.removeChild(modal);
            }
        };
    }

    // 获取订单状态文本
    getOrderStatusText(status) {
        const statusMap = {
            1: '待付款',
            2: '待发货',
            3: '待收货',
            4: '已完成',
            5: '已取消'
        };
        return statusMap[status] || '未知状态';
    }

    // 显示好友管理模态框
    showFriendsManagementModal(friends) {
        const modal = document.createElement('div');
        modal.className = 'friends-management-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>我的好友</h3>
                    <button class="close-modal">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="friends-stats">
                        <div class="stat-item">
                            <span class="stat-label">好友总数:</span>
                            <span class="stat-value">${friends.length || 0}</span>
                        </div>
                    </div>
                    <div class="friends-list">
                        ${friends && friends.length > 0 ?
                            friends.map(friend => `
                                <div class="friend-item">
                                    <div class="friend-avatar">
                                        <img src="${friend.friendAvatar || 'images/avatar/default-avatar.jpg'}" alt="${friend.friendName || friend.friendUsername}">
                                    </div>
                                    <div class="friend-info">
                                        <div class="friend-name">${friend.friendName || friend.friendUsername || '未知好友'}</div>
                                        <div class="friend-phone">${friend.friendPhone || ''}</div>
                                        <div class="friend-status">在线</div>
                                    </div>
                                    <div class="friend-actions">
                                        <button class="btn-chat" onclick="chatWithFriend('${friend.friendPhone}')">聊天</button>
                                        <button class="btn-remove" onclick="removeFriend('${friend.id}')">删除</button>
                                    </div>
                                </div>
                            `).join('') :
                            '<div class="no-friends">暂无好友，快去添加好友吧！</div>'
                        }
                    </div>
                    <div class="friends-actions">
                        <button class="btn-add-friend" onclick="addNewFriend()">+ 添加好友</button>
                        <button class="btn-find-friends" onclick="findFriends()">发现好友</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 添加关闭事件
        modal.querySelector('.close-modal').addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    // 加载用户收藏
    loadUserFavorites(page = 1) {
        const currentUser = this.authManager.getCurrentUser();
        const userPhone = currentUser.phone || currentUser.userId;

        axios.get(`/api/favorites/user/${userPhone}?page=${page}&size=10`)
            .then(response => {
                if (response.data.success) {
                    this.displayFavorites(response.data.data, response.data.total, page);
                } else {
                    this.showMessage('加载收藏失败', 'error');
                }
            })
            .catch(error => {
                console.error('加载收藏失败:', error);
                this.showMessage('网络错误，请重试', 'error');
            });
    }

    // 显示收藏列表
    displayFavorites(favorites, total, page) {
        const container = document.querySelector('.favorites-list');
        if (!container) return;

        let html = '<div class="favorites-grid">';
        favorites.forEach(favorite => {
            html += `
                <div class="favorite-item">
                    <div class="favorite-image">
                        <img src="${favorite.productImageUrl || '/images/default-product.svg'}"
                             alt="${favorite.productName}"
                             onerror="this.src='/images/default-product.svg'">
                    </div>
                    <div class="favorite-info">
                        <h4>${favorite.productName}</h4>
                        <p class="favorite-price">¥${favorite.productPrice}</p>
                        <div class="favorite-actions">
                            <button class="btn-view" onclick="window.viewProductDetail(${favorite.productId})">查看</button>
                            <button class="btn-remove" onclick="window.userCenter?.removeFavorite(${favorite.productId})">移除</button>
                        </div>
                    </div>
                </div>
            `;
        });
        html += '</div>';

        container.innerHTML = html;
        this.updatePagination('favoritesPagination', total, page, 10, (newPage) => this.loadUserFavorites(newPage));
    }

    // 移除收藏
    removeFavorite(productId) {
        const currentUser = this.authManager.getCurrentUser();
        const userPhone = currentUser.phone || currentUser.userId;

        axios.post('/api/favorites/remove', {
            userPhone: userPhone,
            productId: productId
        })
        .then(response => {
            if (response.data.success) {
                this.showMessage('取消收藏成功', 'success');
                this.loadUserFavorites(); // 重新加载收藏列表
            } else {
                this.showMessage('取消收藏失败', 'error');
            }
        })
        .catch(error => {
            console.error('取消收藏失败:', error);
            this.showMessage('取消收藏失败', 'error');
        });
    }

    // 更新分页
    updatePagination(containerId, total, currentPage, pageSize, callback) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const totalPages = Math.ceil(total / pageSize);
        if (totalPages <= 1) {
            container.innerHTML = '';
            return;
        }

        let html = '<div class="pagination">';

        // 上一页
        if (currentPage > 1) {
            html += `<button class="page-btn" onclick="(${callback})(${currentPage - 1})">上一页</button>`;
        }

        // 页码
        for (let i = 1; i <= totalPages; i++) {
            if (i === currentPage) {
                html += `<button class="page-btn active">${i}</button>`;
            } else {
                html += `<button class="page-btn" onclick="(${callback})(${i})">${i}</button>`;
            }
        }

        // 下一页
        if (currentPage < totalPages) {
            html += `<button class="page-btn" onclick="(${callback})(${currentPage + 1})">下一页</button>`;
        }

        html += '</div>';
        container.innerHTML = html;
    }

    // 关闭收藏夹模态框
    closeFavoritesModal() {
        const modal = document.querySelector('.favorites-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    // 帮助中心
    helpCenter() {
        this.showMessage('帮助中心功能开发中...', 'info');
    }

    // 意见反馈
    feedback() {
        this.showMessage('意见反馈功能开发中...', 'info');
    }
}

// 设置模态框事件监听器
function setupModalEventListeners(userCenter) {
    // 用户中心模态框关闭按钮
    const closeUserCenter = document.querySelector('.close-user-center');
    if (closeUserCenter) {
        closeUserCenter.addEventListener('click', () => {
            userCenter.toggleUserCenter(false);
        });
    }

    // 点击模态框背景关闭
    const userCenterModal = document.querySelector('.user-center-modal');
    if (userCenterModal) {
        userCenterModal.addEventListener('click', (e) => {
            if (e.target === userCenterModal) {
                userCenter.toggleUserCenter(false);
            }
        });
    }

    // ESC键关闭模态框
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            const modal = document.querySelector('.user-center-modal.show');
            if (modal) {
                userCenter.toggleUserCenter(false);
            }
        }
    });

    // 签名模态框事件
    const closeSignature = document.querySelector('.close-signature');
    const signatureCancel = document.querySelector('.signature-cancel');
    const signatureSave = document.querySelector('.signature-save');
    const signatureInput = document.getElementById('signatureInput');

    if (closeSignature) {
        closeSignature.addEventListener('click', () => userCenter.closeSignatureModal());
    }
    if (signatureCancel) {
        signatureCancel.addEventListener('click', () => userCenter.closeSignatureModal());
    }
    if (signatureSave) {
        signatureSave.addEventListener('click', () => userCenter.saveSignature());
    }
    if (signatureInput) {
        signatureInput.addEventListener('input', () => userCenter.updateSignatureCounter());
    }

    // 头像模态框事件
    const closeAvatar = document.querySelector('.close-avatar');
    const avatarCancel = document.querySelector('.avatar-cancel');
    const avatarSave = document.querySelector('.avatar-save');
    const avatarFileInput = document.getElementById('avatarFileInput');

    if (closeAvatar) {
        closeAvatar.addEventListener('click', () => userCenter.closeAvatarModal());
    }
    if (avatarCancel) {
        avatarCancel.addEventListener('click', () => userCenter.closeAvatarModal());
    }
    if (avatarSave) {
        avatarSave.addEventListener('click', () => userCenter.saveAvatar());
    }
    if (avatarFileInput) {
        avatarFileInput.addEventListener('change', (e) => {
            if (e.target.files[0]) {
                userCenter.handleAvatarUpload(e.target.files[0]);
            }
        });
    }

    // 预设头像选择
    const presetAvatars = document.querySelectorAll('.preset-avatar');
    presetAvatars.forEach(avatar => {
        avatar.addEventListener('click', () => {
            userCenter.selectPresetAvatar(avatar.dataset.avatar);
        });
    });
}

// 初始化所有功能
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 页面DOM加载完成，开始初始化...');

    // 检查URL参数，如果有清理参数则清理localStorage
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('clear') === 'true') {
        console.log('🧹 检测到清理参数，清理localStorage...');
        localStorage.clear();
        // 移除URL参数
        window.history.replaceState({}, document.title, window.location.pathname);
    }

    // 创建用户中心实例（包含认证管理器）
    const userCenter = new UserCenter();

    // 创建轮播图管理器实例
    const carouselManager = new CarouselManager();

    // 创建搜索管理器
    const searchManager = new SearchManager();

    // 创建购物车实例，传入认证管理器
    const shoppingCart = new ShoppingCart(userCenter.authManager);

    // 将实例挂载到全局，方便调试和其他模块使用
    window.carouselManager = carouselManager;
    window.userCenter = userCenter;
    window.searchManager = searchManager;
    window.shoppingCart = shoppingCart;

    // 创建全局关闭模态框函数
    window.closeAllModals = function() {
        // 关闭所有静态模态框
        const staticModals = [
            '.favorites-modal',
            '.comments-modal',
            '.shares-modal',
            '.signature-modal',
            '.avatar-modal',
            '.confirm-modal'
        ];

        staticModals.forEach(selector => {
            const modal = document.querySelector(selector);
            if (modal) {
                modal.style.display = 'none';
            }
        });

        // 关闭所有动态创建的模态框
        const dynamicModals = document.querySelectorAll('.coupons-modal, .balance-modal, .profile-modal, .settings-modal, .friends-modal, .order-management-modal, .address-management-modal');
        dynamicModals.forEach(modal => {
            if (modal && modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
        });

        // 调用UserCenter的closeAllModals方法
        if (window.userCenter && typeof window.userCenter.closeAllModals === 'function') {
            window.userCenter.closeAllModals();
        }
    };

    // 添加模态框事件监听器
    setupModalEventListeners(userCenter);

    // 暴露清理方法到全局（调试用）
    window.clearUserData = function() {
        console.log('🧹 手动清理用户数据...');
        if (window.userCenter && window.userCenter.authManager) {
            window.userCenter.authManager.forceCleanup();
        }
        localStorage.clear();
        location.reload();
    };

    console.log('✅ 所有组件初始化完成');
    console.log('💡 调试提示：如果需要清理用户数据，请在控制台执行 clearUserData() 或访问 ?clear=true');

    // 延迟更新购物车计数，确保用户中心初始化完成
    setTimeout(() => {
        updateCartCount();
    }, 1000);
});

// 添加页面加载完成的额外检查
window.addEventListener('load', () => {
    console.log('🎯 页面完全加载完成');
});

// 全局地址管理函数
window.editAddress = function(addressId) {
    console.log('编辑地址:', addressId);
    // 这里可以实现地址编辑功能
    alert('地址编辑功能开发中...');
};

window.deleteAddress = function(addressId) {
    if (confirm('确定要删除这个地址吗？')) {
        console.log('删除地址:', addressId);
        // 这里可以实现地址删除功能
        alert('地址删除功能开发中...');
    }
};

window.setDefaultAddress = function(addressId) {
    console.log('设为默认地址:', addressId);
    // 这里可以实现设为默认地址功能
    alert('设为默认地址功能开发中...');
};

window.addNewAddress = function() {
    console.log('添加新地址');

    // 创建添加地址模态框
    const modal = document.createElement('div');
    modal.className = 'address-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>添加收货地址</h3>
                <button class="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="addressForm">
                    <div class="form-group">
                        <label>收货人姓名 *</label>
                        <input type="text" id="receiverName" required placeholder="请输入收货人姓名">
                    </div>
                    <div class="form-group">
                        <label>手机号码 *</label>
                        <input type="tel" id="receiverPhone" required placeholder="请输入手机号码">
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>省份 *</label>
                            <input type="text" id="province" required placeholder="省份">
                        </div>
                        <div class="form-group">
                            <label>城市 *</label>
                            <input type="text" id="city" required placeholder="城市">
                        </div>
                        <div class="form-group">
                            <label>区县 *</label>
                            <input type="text" id="district" required placeholder="区县">
                        </div>
                    </div>
                    <div class="form-group">
                        <label>详细地址 *</label>
                        <textarea id="detailAddress" required placeholder="请输入详细地址"></textarea>
                    </div>
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="isDefault"> 设为默认地址
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn-cancel" onclick="document.body.removeChild(this.closest('.address-modal'))">取消</button>
                <button class="btn-save" onclick="saveNewAddress()">保存</button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 添加关闭事件
    modal.querySelector('.close-modal').addEventListener('click', () => {
        document.body.removeChild(modal);
    });

    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });
};

window.saveNewAddress = function() {
    const userPhone = localStorage.getItem('userPhone');
    if (!userPhone) {
        alert('请先登录');
        return;
    }

    const addressData = {
        userPhone: userPhone,
        receiverName: document.getElementById('receiverName').value,
        receiverPhone: document.getElementById('receiverPhone').value,
        province: document.getElementById('province').value,
        city: document.getElementById('city').value,
        district: document.getElementById('district').value,
        detailAddress: document.getElementById('detailAddress').value,
        isDefault: document.getElementById('isDefault').checked
    };

    // 验证必填字段
    if (!addressData.receiverName || !addressData.receiverPhone || !addressData.province ||
        !addressData.city || !addressData.district || !addressData.detailAddress) {
        alert('请填写完整的地址信息');
        return;
    }

    axios.post('/api/address/add', addressData)
        .then(response => {
            if (response.data.success) {
                alert('地址添加成功');
                document.body.removeChild(document.querySelector('.address-modal'));
                // 刷新地址列表
                if (window.userCenter) {
                    window.userCenter.loadUserDetail();
                }
            } else {
                alert('添加失败: ' + (response.data.message || ''));
            }
        })
        .catch(error => {
            console.error('添加地址失败:', error);
            alert('网络错误，请重试');
        });
};

// 全局订单管理函数
// 刷新当前订单列表
window.refreshCurrentOrderList = function() {
    console.log('🔄 开始刷新当前订单列表');

    const currentUser = window.userCenter?.authManager?.getCurrentUser();
    if (!currentUser) {
        console.warn('⚠️ 用户未登录，无法刷新订单列表');
        return;
    }

    const userIdentifier = currentUser.phone || currentUser.userId;
    console.log('👤 用户标识:', userIdentifier);

    // 重新获取订单数据
    console.log('📤 发送订单列表请求:', `/api/order/list/${userIdentifier}`);
    axios.get(`/api/order/list/${userIdentifier}`)
        .then(response => {
            console.log('📥 订单列表响应:', response.data);

            if (response.data.success) {
                console.log('✅ 订单列表获取成功，开始更新界面');
                // 更新现有的订单模态框内容
                updateOrderModalContent(response.data);
                console.log('✅ 订单列表界面更新完成');
            } else {
                console.error('❌ 刷新订单列表失败:', response.data.message);
            }
        })
        .catch(error => {
            console.error('❌ 刷新订单列表网络错误:', error);
        });
};

// 更新订单模态框内容
function updateOrderModalContent(orderData) {
    console.log('🔄 更新订单模态框内容，数据:', orderData);

    // 查找订单管理模态框
    const existingModal = document.querySelector('.order-management-modal');

    if (existingModal) {
        console.log('✅ 找到订单管理模态框');

        // 查找订单列表容器
        const orderListContainer = existingModal.querySelector('.order-list');

        if (orderListContainer && orderData && orderData.data) {
            console.log('✅ 找到订单列表容器，开始重新渲染');
            console.log('📦 订单数据:', orderData.data);
            console.log('📦 订单数量:', orderData.data.length);

            // 重新渲染订单列表内容
            if (orderData.data.length > 0) {
                orderListContainer.innerHTML = orderData.data.map(order => `
                    <div class="order-item">
                        <div class="order-header">
                            <div class="order-info-left">
                                <div class="order-no">
                                    <i class="fas fa-receipt"></i>
                                    <span class="order-label">订单号:</span>
                                    <span class="order-value">${order.order_no || order.orderNo || '未知'}</span>
                                </div>
                                <div class="order-time">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span class="time-value">${order.created_time ? new Date(order.created_time).toLocaleString() : (order.createdTime ? new Date(order.createdTime).toLocaleString() : '未知时间')}</span>
                                </div>
                            </div>
                            <div class="order-status-badge">
                                <span class="order-status status-${order.status || 0}">${getOrderStatusText(order.status || 0)}</span>
                            </div>
                        </div>
                        <div class="order-content">
                            <div class="order-details">
                                <div class="order-amount-section">
                                    <div class="amount-label">订单金额</div>
                                    <div class="amount-value">¥${order.total_amount || order.totalAmount || '0.00'}</div>
                                </div>
                                <div class="order-receiver-section">
                                    <div class="receiver-label">收货信息</div>
                                    <div class="receiver-info">
                                        <div class="receiver-name">${order.receiver_name || order.receiverName || '未知'}</div>
                                        <div class="receiver-phone">${order.receiver_phone || order.receiverPhone || '未知'}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="order-actions">
                                <button class="btn-action btn-view" onclick="viewOrderDetail('${order.order_no || order.orderNo}')">
                                    <i class="fas fa-eye"></i>
                                    <span>查看详情</span>
                                </button>
                                ${(order.status === 1 || order.status === '1') ?
                                    `<button class="btn-action btn-pay" onclick="payOrder('${order.order_no || order.orderNo}')">
                                        <i class="fas fa-credit-card"></i>
                                        <span>立即支付</span>
                                    </button>` : ''}
                                ${(order.status === 1 || order.status === 2 || order.status === '1' || order.status === '2') ?
                                    `<button class="btn-action btn-cancel" onclick="cancelOrder('${order.order_no || order.orderNo}')">
                                        <i class="fas fa-times"></i>
                                        <span>取消订单</span>
                                    </button>` : ''}
                                ${(order.status === 3 || order.status === '3') ?
                                    `<button class="btn-action btn-confirm" onclick="confirmReceived('${order.order_no || order.orderNo}')">
                                        <i class="fas fa-check"></i>
                                        <span>确认收货</span>
                                    </button>` : ''}
                                ${(order.status === 4 || order.status === 5 || order.status === '4' || order.status === '5') ?
                                    `<button class="btn-action btn-delete" onclick="deleteOrder('${order.order_no || order.orderNo}')">
                                        <i class="fas fa-trash"></i>
                                        <span>删除订单</span>
                                    </button>` : ''}
                            </div>
                        </div>
                    </div>
                `).join('');
            } else {
                orderListContainer.innerHTML = `
                    <div class="no-orders">
                        <div class="no-orders-illustration">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="no-orders-text">暂无订单记录</div>
                        <div class="no-orders-tip">快去购买心仪的商品吧！</div>
                        <button class="btn-shop-now" onclick="closeOrderModal()">
                            <i class="fas fa-shopping-bag"></i>
                            <span>立即购物</span>
                        </button>
                    </div>
                `;
            }

            // 更新订单统计
            const statCards = existingModal.querySelectorAll('.stat-value');
            if (statCards.length >= 4) {
                statCards[0].textContent = orderData.data.length; // 总订单数
                statCards[1].textContent = orderData.data.filter(o => o.status === 1 || o.status === '1').length; // 待付款
                statCards[2].textContent = orderData.data.filter(o => o.status === 2 || o.status === '2').length; // 待发货
                statCards[3].textContent = orderData.data.filter(o => o.status === 4 || o.status === '4').length; // 已完成
            }

            console.log('✅ 订单列表重新渲染完成');
        } else {
            console.warn('⚠️ 未找到订单列表容器或数据无效');
            console.log('🔍 orderListContainer:', orderListContainer);
            console.log('🔍 orderData:', orderData);
        }
    } else {
        console.warn('⚠️ 未找到订单管理模态框');
        console.log('🔍 当前页面中的所有模态框:', document.querySelectorAll('[class*="modal"]'));
    }
}

// 渲染订单列表
function renderOrderList(container, orders) {
    if (!orders || orders.length === 0) {
        container.innerHTML = `
            <div class="no-orders">
                <i class="fas fa-inbox"></i>
                <p>暂无订单</p>
            </div>
        `;
        return;
    }

    container.innerHTML = orders.map(order => `
        <div class="order-item">
            <div class="order-header">
                <div class="order-no">订单号: ${order.order_no || order.orderNo}</div>
                <div class="order-status status-${order.status || 0}">${getOrderStatusText(order.status || 0)}</div>
            </div>
            <div class="order-info">
                <div class="order-amount">¥${order.total_amount || order.totalAmount || '0.00'}</div>
                <div class="order-time">${formatTimestamp(order.created_time || order.createdTime || 0)}</div>
                <div class="order-receiver">
                    <div class="receiver-info">
                        <div class="receiver-name">${order.receiver_name || order.receiverName || '未知'}</div>
                        <div class="receiver-phone">${order.receiver_phone || order.receiverPhone || '未知'}</div>
                    </div>
                </div>
            </div>
            <div class="order-actions">
                <button class="btn-action btn-view" onclick="viewOrderDetail('${order.order_no || order.orderNo}')">
                    <i class="fas fa-eye"></i>
                    <span>查看详情</span>
                </button>
                ${(order.status === 1 || order.status === '1') ?
                    `<button class="btn-action btn-pay" onclick="payOrder('${order.order_no || order.orderNo}')">
                        <i class="fas fa-credit-card"></i>
                        <span>立即支付</span>
                    </button>` : ''}
                ${(order.status === 1 || order.status === 2 || order.status === '1' || order.status === '2') ?
                    `<button class="btn-action btn-cancel" onclick="cancelOrder('${order.order_no || order.orderNo}')">
                        <i class="fas fa-times"></i>
                        <span>取消订单</span>
                    </button>` : ''}
                ${(order.status === 3 || order.status === '3') ?
                    `<button class="btn-action btn-confirm" onclick="confirmReceived('${order.order_no || order.orderNo}')">
                        <i class="fas fa-check"></i>
                        <span>确认收货</span>
                    </button>` : ''}
                ${(order.status === 4 || order.status === 5 || order.status === '4' || order.status === '5') ?
                    `<button class="btn-action btn-delete" onclick="deleteOrder('${order.order_no || order.orderNo}')">
                        <i class="fas fa-trash"></i>
                        <span>删除订单</span>
                    </button>` : ''}
            </div>
        </div>
    `).join('');
}

window.viewOrderDetail = function(orderNo) {
    console.log('🔍 查看订单详情:', orderNo);

    if (!orderNo || orderNo === 'undefined') {
        alert('订单号无效');
        return;
    }

    // 显示加载提示
    const loadingModal = document.createElement('div');
    loadingModal.className = 'loading-modal';
    loadingModal.innerHTML = `
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p>正在加载订单详情...</p>
        </div>
    `;
    loadingModal.style.cssText = `
        position: fixed; top: 0; left: 0; width: 100%; height: 100%;
        background: rgba(0,0,0,0.5); display: flex; align-items: center;
        justify-content: center; z-index: 10001;
    `;
    document.body.appendChild(loadingModal);

    // 获取订单详情
    console.log('📤 发送订单详情请求:', `/api/order/${orderNo}`);
    axios.get(`/api/order/${orderNo}`)
        .then(response => {
            console.log('📦 订单详情API完整响应:', response);
            console.log('📦 订单详情API数据:', response.data);

            // 移除加载提示
            document.body.removeChild(loadingModal);

            if (response.data.success) {
                const orderData = response.data.data;
                console.log('📦 订单数据:', orderData);
                console.log('📦 商品列表:', orderData.items);
                console.log('📦 商品列表类型:', typeof orderData.items);
                console.log('📦 商品列表长度:', orderData.items ? orderData.items.length : 'undefined');

                // 如果没有商品信息，尝试从其他字段获取
                if (!orderData.items || orderData.items.length === 0) {
                    console.log('⚠️ 订单没有商品信息，尝试从其他字段获取');

                    // 检查所有可能的字段
                    const possibleFields = ['orderItems', 'products', 'details', 'orderDetails'];
                    for (const field of possibleFields) {
                        if (orderData[field] && Array.isArray(orderData[field]) && orderData[field].length > 0) {
                            console.log(`✅ 在字段 ${field} 中找到商品数据:`, orderData[field]);
                            orderData.items = orderData[field];
                            break;
                        }
                    }

                    // 如果还是没有，尝试重新请求
                    if (!orderData.items || orderData.items.length === 0) {
                        console.log('⚠️ 仍然没有商品信息，尝试重新请求');
                        setTimeout(() => {
                            axios.get(`/api/order/${orderNo}`)
                                .then(retryResponse => {
                                    console.log('🔄 重试请求响应:', retryResponse.data);
                                    if (retryResponse.data.success && retryResponse.data.data) {
                                        showOrderDetailModal(retryResponse.data.data);
                                    } else {
                                        showOrderDetailModal(orderData); // 使用原数据
                                    }
                                })
                                .catch(retryError => {
                                    console.error('🔄 重试请求失败:', retryError);
                                    showOrderDetailModal(orderData); // 使用原数据
                                });
                        }, 500);
                        return;
                    }
                }

                showOrderDetailModal(orderData);
            } else {
                alert('获取订单详情失败: ' + (response.data.message || ''));
            }
        })
        .catch(error => {
            console.error('❌ 获取订单详情失败:', error);
            console.error('❌ 错误详情:', error.response?.data);

            // 移除加载提示
            if (document.body.contains(loadingModal)) {
                document.body.removeChild(loadingModal);
            }

            alert('网络错误，请重试: ' + (error.response?.data?.message || error.message));
        });
};

window.payOrder = function(orderNo) {
    console.log('支付订单:', orderNo);

    if (!orderNo || orderNo === 'undefined') {
        alert('订单号无效');
        return;
    }

    if (confirm('确定要支付这个订单吗？')) {
        // 模拟支付成功（实际项目中应该调用支付接口）
        axios.post(`/api/order/${orderNo}/status`, { status: 2 })
            .then(response => {
                if (response.data.success) {
                    alert('支付成功！');
                    // 刷新当前订单列表，不重新打开页面
                    window.refreshCurrentOrderList();
                    // 更新订单统计
                    updateOrderStats();
                } else {
                    alert('支付失败: ' + (response.data.message || ''));
                }
            })
            .catch(error => {
                console.error('支付失败:', error);
                alert('网络错误，请重试');
            });
    }
};

window.cancelOrder = function(orderNo) {
    console.log('🔄 取消订单:', orderNo);

    if (!orderNo || orderNo === 'undefined') {
        alert('订单号无效');
        return;
    }

    if (confirm('确定要取消这个订单吗？')) {
        console.log('📤 发送取消订单请求:', `/api/order/cancel/${orderNo}`);

        axios.post(`/api/order/cancel/${orderNo}`)
            .then(response => {
                console.log('📥 取消订单响应:', response.data);

                if (response.data.success) {
                    alert('订单已取消');
                    // 刷新当前订单列表，不重新打开页面
                    window.refreshCurrentOrderList();
                    // 更新订单统计
                    updateOrderStats();
                } else {
                    console.error('❌ 取消订单失败:', response.data.message);
                    alert('取消订单失败: ' + (response.data.message || '未知错误'));
                }
            })
            .catch(error => {
                console.error('❌ 取消订单网络错误:', error);
                console.error('❌ 错误详情:', error.response?.data);
                alert('网络错误，请重试: ' + (error.response?.data?.message || error.message));
            });
    }
};

// 确认收货
window.confirmReceived = function(orderNo) {
    console.log('确认收货:', orderNo);

    if (!orderNo || orderNo === 'undefined') {
        alert('订单号无效');
        return;
    }

    if (confirm('确定已收到商品吗？确认后订单将完成。')) {
        axios.post(`/api/order/confirm/${orderNo}`)
            .then(response => {
                if (response.data.success) {
                    alert('确认收货成功，订单已完成');
                    // 刷新当前订单列表
                    window.refreshCurrentOrderList();
                    // 更新订单统计
                    updateOrderStats();
                } else {
                    alert('确认收货失败: ' + (response.data.message || ''));
                }
            })
            .catch(error => {
                console.error('确认收货失败:', error);
                alert('网络错误，请重试');
            });
    }
};

// 删除订单
window.deleteOrder = function(orderNo) {
    console.log('删除订单:', orderNo);

    if (!orderNo || orderNo === 'undefined') {
        alert('订单号无效');
        return;
    }

    if (confirm('确定要删除这个订单吗？删除后无法恢复。')) {
        axios.delete(`/api/order/${orderNo}`)
            .then(response => {
                if (response.data.success) {
                    alert('订单已删除');
                    // 刷新当前订单列表
                    window.refreshCurrentOrderList();
                    // 更新订单统计
                    updateOrderStats();
                } else {
                    alert('删除订单失败: ' + (response.data.message || ''));
                }
            })
            .catch(error => {
                console.error('删除订单失败:', error);
                alert('网络错误，请重试');
            });
    }
};

// 显示订单详情模态框
function showOrderDetailModal(order) {
    console.log('🔍 显示订单详情模态框，订单数据:', order);
    console.log('🔍 商品列表数据:', order.items);
    console.log('🔍 商品列表长度:', order.items ? order.items.length : 'undefined');

    // 如果没有商品信息，尝试重新获取订单详情
    if (!order.items || order.items.length === 0) {
        console.log('⚠️ 订单没有商品信息，尝试重新获取订单详情');
        const orderNo = order.order_no || order.orderNo;
        if (orderNo) {
            console.log('📤 发送订单详情请求:', `/api/order/${orderNo}`);
            axios.get(`/api/order/${orderNo}`)
                .then(response => {
                    console.log('🔍 重新获取的订单详情完整响应:', response);
                    console.log('🔍 重新获取的订单详情数据:', response.data);

                    if (response.data.success && response.data.data) {
                        console.log('✅ 重新获取成功，检查新数据的商品信息');
                        const newOrderData = response.data.data;
                        console.log('🔍 新订单数据:', newOrderData);
                        console.log('🔍 新订单商品信息:', newOrderData.items);

                        // 如果新数据有商品信息，使用新数据；否则继续使用原数据但显示调试信息
                        if (newOrderData.items && newOrderData.items.length > 0) {
                            console.log('✅ 新数据包含商品信息，递归调用showOrderDetailModal');
                            showOrderDetailModal(newOrderData);
                            return;
                        } else {
                            console.log('⚠️ 重新获取的数据仍然没有商品信息，继续显示原订单');
                            console.log('🔍 可能的商品数据字段:', Object.keys(newOrderData));
                        }
                    } else {
                        console.error('❌ 重新获取订单详情失败:', response.data.message);
                    }
                })
                .catch(error => {
                    console.error('❌ 重新获取订单详情网络错误:', error);
                });
        } else {
            console.error('❌ 订单号无效:', orderNo);
        }
    }

    const modal = document.createElement('div');
    modal.className = 'order-detail-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>订单详情</h3>
                <button class="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                <!-- 订单基本信息 -->
                <div class="order-basic-info">
                    <h4><i class="fas fa-info-circle"></i> 订单信息</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="label">订单号:</span>
                            <span class="value">${order.order_no || order.orderNo || '未知'}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">订单状态:</span>
                            <span class="value status-${order.status || 0}">${getOrderStatusText(order.status || 0)}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">订单金额:</span>
                            <span class="value price">¥${order.total_amount || order.totalAmount || '0.00'}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">下单时间:</span>
                            <span class="value">${new Date(order.createdTime || order.created_time).toLocaleString()}</span>
                        </div>
                    </div>
                </div>

                <!-- 收货信息 -->
                <div class="delivery-info">
                    <h4><i class="fas fa-shipping-fast"></i> 收货信息</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="label">收货人:</span>
                            <span class="value">${order.receiver_name || order.receiverName || '未知'}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">联系电话:</span>
                            <span class="value">${order.receiver_phone || order.receiverPhone || '未知'}</span>
                        </div>
                        <div class="info-item full-width">
                            <span class="label">收货地址:</span>
                            <span class="value">${order.receiver_address || order.receiverAddress || '未知'}</span>
                        </div>
                    </div>
                </div>

                <!-- 商品详情 -->
                <div class="order-items-section">
                    <h4><i class="fas fa-box"></i> 商品详情</h4>
                    <div class="order-items-list">
                        ${(() => {
                            console.log('🔍 渲染商品列表，完整订单数据:', order);
                            console.log('🔍 商品列表数据:', order.items);
                            console.log('🔍 商品列表类型:', typeof order.items);
                            console.log('🔍 商品列表长度:', order.items ? order.items.length : 'undefined');

                            // 检查所有可能的商品数据字段
                            const possibleItemsFields = ['items', 'orderItems', 'products', 'details', 'orderDetails', 'productList'];
                            let itemsData = null;

                            console.log('🔍 检查订单对象的所有字段:', Object.keys(order));

                            for (const field of possibleItemsFields) {
                                console.log(`🔍 检查字段 ${field}:`, order[field]);
                                if (order[field] && Array.isArray(order[field]) && order[field].length > 0) {
                                    console.log(`✅ 在字段 ${field} 中找到商品数据:`, order[field]);
                                    itemsData = order[field];
                                    break;
                                }
                            }

                            if (!itemsData) {
                                console.log('⚠️ 在预定义字段中未找到商品数据，使用原始 items 字段');
                                itemsData = order.items;

                                // 尝试从订单对象中查找任何包含商品信息的字段
                                console.log('🔍 尝试查找其他可能包含商品信息的字段');
                                for (const [key, value] of Object.entries(order)) {
                                    if (Array.isArray(value) && value.length > 0) {
                                        console.log(`🔍 发现数组字段 ${key}:`, value);
                                        // 检查数组中的第一个元素是否像商品数据
                                        const firstItem = value[0];
                                        if (firstItem && typeof firstItem === 'object' &&
                                            (firstItem.product_name || firstItem.productName || firstItem.name)) {
                                            console.log(`✅ 字段 ${key} 可能包含商品数据:`, value);
                                            itemsData = value;
                                            break;
                                        }
                                    }
                                }
                            }

                            if (itemsData && Array.isArray(itemsData) && itemsData.length > 0) {
                                console.log('✅ 有商品数据，开始渲染，商品数量:', itemsData.length);
                                return itemsData.map((item, index) => {
                                    console.log(`🔍 渲染商品 ${index + 1}:`, item);
                                    console.log(`🔍 商品字段检查:`, {
                                        product_name: item.product_name,
                                        productName: item.productName,
                                        name: item.name,
                                        product_price: item.product_price,
                                        productPrice: item.productPrice,
                                        price: item.price,
                                        quantity: item.quantity,
                                        subtotal: item.subtotal,
                                        product_image_url: item.product_image_url,
                                        productImageUrl: item.productImageUrl,
                                        image_url: item.image_url,
                                        imageUrl: item.imageUrl
                                    });

                                    const itemName = item.product_name || item.productName || item.name || '未知商品';
                                    const itemPrice = item.product_price || item.productPrice || item.price || '0.00';
                                    const itemQuantity = item.quantity || 1;
                                    const itemSubtotal = item.subtotal || (parseFloat(itemPrice) * itemQuantity).toFixed(2);
                                    const itemImage = item.product_image_url || item.productImageUrl || item.image_url || item.imageUrl || '/images/default-product.svg';
                                    const itemDescription = item.product_description || item.productDescription || item.description || '';

                                    console.log(`✅ 商品 ${index + 1} 渲染数据:`, {
                                        name: itemName,
                                        price: itemPrice,
                                        quantity: itemQuantity,
                                        subtotal: itemSubtotal,
                                        image: itemImage,
                                        description: itemDescription
                                    });

                                    return `
                                        <div class="order-item-detail">
                                            <div class="item-image">
                                                <img src="${itemImage}"
                                                     alt="${itemName}"
                                                     onerror="this.src='/images/default-product.svg'">
                                            </div>
                                            <div class="item-info">
                                                <div class="item-name">${itemName}</div>
                                                <div class="item-description">${itemDescription}</div>
                                                <div class="item-price-info">
                                                    <span class="order-price">下单价格: ¥${itemPrice}</span>
                                                    ${item.current_price && item.current_price !== itemPrice ?
                                                        `<span class="current-price">当前价格: ¥${item.current_price}</span>` : ''}
                                                </div>
                                            </div>
                                            <div class="item-quantity">
                                                <span class="quantity">×${itemQuantity}</span>
                                                <span class="subtotal">¥${itemSubtotal}</span>
                                            </div>
                                        </div>
                                    `;
                                }).join('');
                            } else {
                                console.log('❌ 无商品数据或数据格式错误');
                                console.log('🔍 order 完整对象:', order);
                                console.log('🔍 order.items 详细信息:', {
                                    exists: !!order.items,
                                    isArray: Array.isArray(order.items),
                                    length: order.items ? order.items.length : 'N/A',
                                    content: order.items
                                });

                                // 显示调试信息
                                return `
                                    <div class="no-items">
                                        <p>暂无商品信息</p>
                                        <details style="margin-top: 10px; font-size: 12px; color: #666;">
                                            <summary>调试信息</summary>
                                            <pre style="background: #f5f5f5; padding: 10px; margin-top: 5px; border-radius: 4px; overflow: auto; max-height: 200px;">
订单对象: ${JSON.stringify(order, null, 2)}
                                            </pre>
                                        </details>
                                    </div>
                                `;
                            }
                        })()}
                    </div>
                </div>

                ${order.remark ? `
                <!-- 订单备注 -->
                <div class="order-remark">
                    <h4><i class="fas fa-comment"></i> 订单备注</h4>
                    <div class="remark-content">${order.remark}</div>
                </div>
                ` : ''}
                <!-- 操作按钮 -->
                <div class="order-actions">
                    ${(order.status === 1 || order.status === '1') ? `
                        <button class="btn-pay" onclick="payOrder('${order.order_no || order.orderNo}'); document.body.removeChild(this.closest('.order-detail-modal'));">
                            <i class="fas fa-credit-card"></i> 立即支付
                        </button>
                        <button class="btn-cancel" onclick="cancelOrder('${order.order_no || order.orderNo}'); document.body.removeChild(this.closest('.order-detail-modal'));">
                            <i class="fas fa-times"></i> 取消订单
                        </button>
                    ` : ''}
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 添加关闭事件
    modal.querySelector('.close-modal').addEventListener('click', () => {
        document.body.removeChild(modal);
    });

    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });
}

// 获取订单状态文本
function getOrderStatusText(status) {
    const statusMap = {
        1: '待付款',
        2: '待发货',
        3: '待收货',
        4: '已完成',
        5: '已取消'
    };
    return statusMap[status] || '未知状态';
}

// 全局好友管理函数
window.chatWithFriend = function(friendId) {
    console.log('与好友聊天:', friendId);
    alert('聊天功能开发中...');
};

window.removeFriend = function(friendId) {
    if (confirm('确定要删除这个好友吗？')) {
        console.log('删除好友:', friendId);
        alert('删除好友功能开发中...');
    }
};

window.addNewFriend = function() {
    console.log('添加好友');
    alert('添加好友功能开发中...');
};

window.findFriends = function() {
    console.log('发现好友');
    alert('发现好友功能开发中...');
};

// 添加订单页面样式
const orderStyles = `
<style>
/* 订单列表样式美化 */
.order-item {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
    border-radius: 12px;
    margin-bottom: 16px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.order-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1);
}

.order-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.12);
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
}

.order-no {
    font-weight: 600;
    color: #2c3e50;
    font-size: 16px;
}

.order-status {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.order-status.status-1 {
    background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
    color: #e17055;
}

.order-status.status-2 {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
}

.order-status.status-3 {
    background: linear-gradient(135deg, #a29bfe, #6c5ce7);
    color: white;
}

.order-status.status-4 {
    background: linear-gradient(135deg, #00b894, #00cec9);
    color: white;
}

.order-status.status-5 {
    background: linear-gradient(135deg, #fd79a8, #e84393);
    color: white;
}

.order-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 15px;
}

.order-amount {
    font-size: 18px;
    font-weight: 700;
    color: #e74c3c;
}

.order-time {
    color: #7f8c8d;
    font-size: 14px;
}

.order-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.btn-view-detail, .btn-pay, .btn-cancel {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.btn-view-detail {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
}

.btn-view-detail:hover {
    background: linear-gradient(135deg, #0984e3, #74b9ff);
    transform: translateY(-1px);
}

.btn-pay {
    background: linear-gradient(135deg, #00b894, #00cec9);
    color: white;
}

.btn-pay:hover {
    background: linear-gradient(135deg, #00cec9, #00b894);
    transform: translateY(-1px);
}

.btn-cancel {
    background: linear-gradient(135deg, #fd79a8, #e84393);
    color: white;
}

.btn-cancel:hover {
    background: linear-gradient(135deg, #e84393, #fd79a8);
    transform: translateY(-1px);
}

.btn-confirm {
    background: linear-gradient(135deg, #00b894, #00cec9);
    color: white;
}

.btn-confirm:hover {
    background: linear-gradient(135deg, #00cec9, #00b894);
    transform: translateY(-1px);
}

.btn-delete {
    background: linear-gradient(135deg, #e17055, #d63031);
    color: white;
}

.btn-delete:hover {
    background: linear-gradient(135deg, #d63031, #e17055);
    transform: translateY(-1px);
}

/* 订单详情模态框样式 */
.order-detail-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
}

.order-detail-modal .modal-content {
    background: white;
    border-radius: 16px;
    padding: 0;
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.order-detail-modal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 16px 16px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.order-detail-modal .modal-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.order-detail-modal .close-modal {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.order-detail-modal .close-modal:hover {
    background: rgba(255, 255, 255, 0.2);
}

.order-detail-modal .modal-body {
    padding: 25px;
}

.order-detail-modal .order-info {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
    margin-bottom: 20px;
}

.order-detail-modal .info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.order-detail-modal .info-row:last-child {
    border-bottom: none;
}

.order-detail-modal .label {
    font-weight: 600;
    color: #2c3e50;
    min-width: 100px;
}

.order-detail-modal .value {
    color: #34495e;
    text-align: right;
    flex: 1;
}

.order-detail-modal .order-actions {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    gap: 12px;
    justify-content: center;
}

/* 订单详情新增样式 */
.order-basic-info, .delivery-info, .order-items-section, .order-remark {
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    border-left: 4px solid #667eea;
}

.order-basic-info h4, .delivery-info h4, .order-items-section h4, .order-remark h4 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

@media (max-width: 600px) {
    .info-grid {
        grid-template-columns: 1fr;
    }
}

.info-item {
    display: flex;
    flex-direction: column;
    padding: 12px 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    min-height: 60px;
    justify-content: center;
}

.info-item.full-width {
    grid-column: 1 / -1;
}

.info-item:last-child {
    border-bottom: none;
}

.info-item .label {
    font-weight: 600;
    color: #495057;
    font-size: 12px;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-item .value {
    color: #2c3e50;
    font-size: 14px;
    font-weight: 500;
    word-break: break-all;
}

.info-item .value.price {
    color: #e74c3c;
    font-weight: 700;
    font-size: 18px;
}

/* 商品详情样式 */
.order-items-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.order-item-detail {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.order-item-detail:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.item-image {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #e9ecef;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.item-info {
    flex: 1;
    min-width: 0;
}

.item-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
    font-size: 16px;
}

.item-description {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 8px;
    line-height: 1.4;
}

.item-price-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.order-price {
    color: #495057;
    font-weight: 500;
    font-size: 14px;
}

.current-price {
    color: #28a745;
    font-size: 12px;
}

.item-quantity {
    text-align: right;
    flex-shrink: 0;
}

.quantity {
    display: block;
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 5px;
}

.subtotal {
    display: block;
    color: #e74c3c;
    font-weight: 700;
    font-size: 16px;
}

.no-items {
    text-align: center;
    padding: 40px;
    color: #6c757d;
    font-style: italic;
}

.remark-content {
    background: white;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    color: #495057;
    line-height: 1.5;
}

.no-orders {
    text-align: center;
    padding: 60px 20px;
    color: #7f8c8d;
    font-size: 16px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    border: 2px dashed #dee2e6;
}

.no-orders::before {
    content: '📦';
    display: block;
    font-size: 48px;
    margin-bottom: 15px;
}

/* 地址管理模态框样式 */
.address-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
}

.address-modal .modal-content {
    background: white;
    border-radius: 16px;
    padding: 0;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
    animation: modalSlideIn 0.3s ease;
}

.address-modal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 16px 16px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.address-modal .modal-body {
    padding: 25px;
}

.address-modal .form-group {
    margin-bottom: 20px;
}

.address-modal .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 15px;
}

.address-modal label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
}

.address-modal input, .address-modal textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.address-modal input:focus, .address-modal textarea:focus {
    outline: none;
    border-color: #667eea;
}

.address-modal textarea {
    height: 80px;
    resize: vertical;
}

.address-modal .checkbox-label {
    display: flex;
    align-items: center;
    font-weight: normal;
    cursor: pointer;
}

.address-modal .checkbox-label input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
}

.address-modal .modal-footer {
    padding: 20px 25px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.address-modal .btn-cancel, .address-modal .btn-save {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.address-modal .btn-cancel {
    background: #6c757d;
    color: white;
}

.address-modal .btn-cancel:hover {
    background: #5a6268;
}

.address-modal .btn-save {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.address-modal .btn-save:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .order-info {
        grid-template-columns: 1fr;
    }

    .order-actions {
        flex-direction: column;
    }

    .btn-view-detail, .btn-pay, .btn-cancel {
        width: 100%;
        justify-content: center;
    }

    .order-detail-modal .modal-content {
        width: 95%;
        margin: 10px;
    }

    .address-modal .form-row {
        grid-template-columns: 1fr;
    }

    .address-modal .modal-footer {
        flex-direction: column;
    }

    .address-modal .btn-cancel, .address-modal .btn-save {
        width: 100%;
    }
}
</style>
`;

// 将样式添加到页面
if (!document.querySelector('#order-styles')) {
    const styleElement = document.createElement('div');
    styleElement.id = 'order-styles';
    styleElement.innerHTML = orderStyles;
    document.head.appendChild(styleElement);
}

// ==================== 产品详情功能 ====================

// 查看产品详情
window.viewProductDetail = function(productId) {
    console.log('🔍 查看产品详情:', productId);

    if (!productId) {
        window.userCenter?.showMessage('产品ID无效', 'error');
        return;
    }

    // 获取产品详情数据
    axios.get(`/api/products/${productId}`)
        .then(response => {
            if (response.data.success) {
                showProductDetailModal(response.data.data);
            } else {
                window.userCenter?.showMessage('获取产品详情失败: ' + response.data.message, 'error');
            }
        })
        .catch(error => {
            console.error('获取产品详情失败:', error);
            window.userCenter?.showMessage('网络错误，请重试', 'error');
        });
};

// 显示产品详情模态框
function showProductDetailModal(product) {
    console.log('📦 显示产品详情:', product);

    // 移除已存在的模态框
    const existingModal = document.querySelector('.product-detail-modal');
    if (existingModal) {
        existingModal.remove();
    }

    // 创建产品详情模态框
    const modal = createProductDetailModal(product);
    document.body.appendChild(modal);

    // 显示模态框
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);

    // 获取产品图片
    loadProductImages(product.id);

    // 加载产品社交数据
    loadProductSocialData(product.id);
}

// 创建产品详情模态框
function createProductDetailModal(product) {
    const modal = document.createElement('div');
    modal.className = 'product-detail-modal';
    modal.innerHTML = `
        <div class="modal-overlay" onclick="closeProductDetailModal()"></div>
        <div class="modal-content">
            <!-- 模态框头部 -->
            <div class="modal-header">
                <div class="header-content">
                    <div class="header-left">
                        <div class="header-icon">
                            <i class="fas fa-box-open"></i>
                        </div>
                        <div class="header-text">
                            <h2>${product.name || '产品详情'}</h2>
                            <p><i class="fas fa-tag"></i> ${getProductCategoryText(product)}</p>
                        </div>
                    </div>
                </div>
                <!-- 关闭按钮独立放置 -->
                <button class="close-btn" onclick="closeProductDetailModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- 模态框内容 -->
            <div class="modal-body">
                <div class="product-detail-grid">
                    <!-- 左侧：产品图片 -->
                    <div class="product-images-section">
                        <div class="main-image-container">
                            <img id="mainProductImage" src="${getImageUrl(product.image_url || product.imageUrl)}"
                                 alt="${product.name}" class="main-product-image"
                                 onerror="this.src='${getImageUrl(null)}'"
                                 onload="console.log('✅ 主图加载成功:', this.src)"
                                 onerror="console.log('❌ 主图加载失败:', this.src); this.src='${getImageUrl(null)}'">
                            <div class="image-zoom-hint">
                                <i class="fas fa-search-plus"></i>
                                点击放大
                            </div>
                        </div>
                        <div class="thumbnail-images-container">
                            <div id="productThumbnails" class="thumbnail-images">
                                <!-- 缩略图将通过JavaScript动态加载 -->
                            </div>
                        </div>
                    </div>

                    <!-- 右侧：产品信息 -->
                    <div class="product-info-section">
                        <!-- 价格信息 -->
                        <div class="price-section">
                            <div class="current-price">
                                <span class="currency">¥</span>
                                <span class="price-value">${product.price || '0.00'}</span>
                            </div>
                            ${product.originalPrice && product.originalPrice > product.price ?
                                `<div class="original-price">原价：¥${product.originalPrice}</div>` : ''}
                        </div>

                        <!-- 库存信息 -->
                        <div class="stock-section">
                            <div class="stock-info">
                                <span class="stock-label">库存：</span>
                                <span class="stock-value ${(product.stock || 0) > 0 ? 'in-stock' : 'out-of-stock'}">
                                    ${(product.stock || 0) > 0 ? `${product.stock} 件` : '暂无库存'}
                                </span>
                            </div>
                            ${product.isNew ? '<div class="new-badge">新品</div>' : ''}
                        </div>

                        <!-- 产品描述 -->
                        <div class="description-section">
                            <h3><i class="fas fa-info-circle"></i> 产品描述</h3>
                            <div class="description-content">
                                ${product.description || '暂无产品描述'}
                            </div>
                        </div>

                        <!-- 数量选择 -->
                        <div class="quantity-section">
                            <h3><i class="fas fa-calculator"></i> 购买数量</h3>
                            <div class="quantity-controls">
                                <button class="quantity-btn minus" onclick="changeQuantity(-1)">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <input type="number" id="productQuantity" value="1" min="1" max="${product.stock || 1}"
                                       class="quantity-input" onchange="validateQuantity()">
                                <button class="quantity-btn plus" onclick="changeQuantity(1)">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                            <div class="quantity-hint">最大可购买 ${product.stock || 0} 件</div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="action-buttons">
                            <button class="btn-add-cart ${(product.stock || 0) <= 0 ? 'disabled' : ''}"
                                    onclick="addToCart(${product.id})"
                                    ${(product.stock || 0) <= 0 ? 'disabled' : ''}>
                                <i class="fas fa-shopping-cart"></i>
                                ${(product.stock || 0) > 0 ? '加入购物车' : '暂无库存'}
                            </button>
                            <button class="btn-buy-now ${(product.stock || 0) <= 0 ? 'disabled' : ''}"
                                    onclick="buyNow(${product.id})"
                                    ${(product.stock || 0) <= 0 ? 'disabled' : ''}>
                                <i class="fas fa-bolt"></i>
                                ${(product.stock || 0) > 0 ? '立即购买' : '暂无库存'}
                            </button>
                        </div>

                        <!-- 社交功能按钮 -->
                        <div class="social-buttons" data-product-id="${product.id}">
                            <button class="btn-favorite" onclick="window.productFavorite?.toggleFavorite(${product.id})">
                                <i class="far fa-heart"></i>
                                <span>收藏</span>
                                <span class="favorite-count">0</span>
                            </button>
                            <div class="share-dropdown">
                                <button class="btn-share" onclick="this.parentElement.classList.toggle('active')">
                                    <i class="fas fa-share-alt"></i>
                                    <span>分享</span>
                                    <span class="share-count">0</span>
                                </button>
                                <div class="share-options">
                                    <button onclick="window.productShare?.shareProduct(${product.id}, 'wechat')">
                                        <i class="fab fa-weixin"></i> 微信
                                    </button>
                                    <button onclick="window.productShare?.shareProduct(${product.id}, 'weibo')">
                                        <i class="fab fa-weibo"></i> 微博
                                    </button>
                                    <button onclick="window.productShare?.shareProduct(${product.id}, 'qq')">
                                        <i class="fab fa-qq"></i> QQ
                                    </button>
                                    <button onclick="window.productShare?.shareProduct(${product.id}, 'link')">
                                        <i class="fas fa-link"></i> 复制链接
                                    </button>
                                </div>
                            </div>
                            <button class="btn-comment" onclick="window.productComment?.showCommentModal(${product.id}, '${product.name}', '${product.price}', '${getImageUrl(product.image_url || product.imageUrl)}')">
                                <i class="fas fa-comment-dots"></i>
                                <span>评论</span>
                            </button>
                            <button class="btn-view-comments" onclick="if(window.productComment && window.productComment.showCommentsListModal) window.productComment.showCommentsListModal(${product.id}); else console.error('评论功能未加载');">
                                <i class="fas fa-comments"></i>
                                <span>查看评论</span>
                                <span class="comment-count">0</span>
                            </button>
                        </div>

                        <!-- 产品评分显示 -->
                        <div class="product-rating-section">
                            <div class="rating-display">
                                <div class="rating-stars" id="productRatingStars">
                                    <span class="star">☆</span>
                                    <span class="star">☆</span>
                                    <span class="star">☆</span>
                                    <span class="star">☆</span>
                                    <span class="star">☆</span>
                                </div>
                                <span class="rating-score" id="productRatingScore">0.0</span>
                                <span class="rating-count" id="productRatingCount">(0条评论)</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 添加样式
    addProductDetailStyles();

    return modal;
}

// 关闭产品详情模态框
window.closeProductDetailModal = function() {
    const modal = document.querySelector('.product-detail-modal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
};

// 获取产品分类文本
function getProductCategoryText(product) {
    if (product.categories && Array.isArray(product.categories) && product.categories.length > 0) {
        // 如果有分类数组，显示所有分类名称
        return product.categories.map(cat => cat.name).join(', ');
    } else if (product.category) {
        // 如果有单个分类字段
        return product.category;
    } else {
        // 默认显示
        return '未分类';
    }
}

// 获取图片URL
function getImageUrl(imageUrl) {
    console.log('🔗 处理图片URL:', imageUrl);

    if (!imageUrl || imageUrl === 'null' || imageUrl === 'undefined') {
        console.log('📷 使用默认图片');
        // 返回内联SVG默认图片（避免中文字符导致的btoa错误）
        const svgContent = `<svg xmlns="http://www.w3.org/2000/svg" width="300" height="300" viewBox="0 0 300 300">
                <rect width="300" height="300" fill="#f8f9fa"/>
                <rect x="50" y="50" width="200" height="200" fill="#e9ecef" rx="10"/>
                <circle cx="120" cy="120" r="20" fill="#dee2e6"/>
                <path d="M80 180 L120 140 L160 180 L200 140 L220 160 L220 220 L80 220 Z" fill="#dee2e6"/>
                <text x="150" y="260" text-anchor="middle" font-family="Arial" font-size="14" fill="#6c757d">No Image</text>
            </svg>`;

        try {
            // 使用encodeURIComponent来安全编码SVG
            return 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(svgContent);
        } catch (error) {
            console.warn('SVG编码失败，使用备用图片:', error);
            // 备用方案：返回一个简单的data URL
            return 'data:image/svg+xml;charset=utf-8,%3Csvg xmlns="http://www.w3.org/2000/svg" width="300" height="300"%3E%3Crect width="300" height="300" fill="%23f8f9fa"/%3E%3C/svg%3E';
        }
    }

    if (imageUrl.startsWith('http') || imageUrl.startsWith('/')) {
        console.log('🌐 使用绝对路径:', imageUrl);
        return imageUrl;
    }

    const finalUrl = `/images/${imageUrl}`;
    console.log('📁 使用相对路径，最终URL:', finalUrl);
    return finalUrl;
}

// 加载产品图片
function loadProductImages(productId) {
    console.log('🖼️ 开始加载产品图片，产品ID:', productId);

    axios.get(`/api/product-images/product/${productId}`)
        .then(response => {
            console.log('📸 产品图片API响应:', response.data);

            if (response.data.success && response.data.data) {
                const images = response.data.data;
                console.log('🎯 获取到图片数据:', images);

                // 更新主图
                if (images.length > 0) {
                    const primaryImage = images.find(img => img.is_primary || img.isPrimary) || images[0];
                    const mainImage = document.getElementById('mainProductImage');
                    if (mainImage && primaryImage) {
                        const imageUrl = getImageUrl(primaryImage.image_url || primaryImage.imageUrl);
                        console.log('🖼️ 设置主图URL:', imageUrl);
                        mainImage.src = imageUrl;
                    }
                }

                displayProductImages(images);
            } else {
                console.warn('⚠️ 产品图片数据为空或获取失败');
            }
        })
        .catch(error => {
            console.error('❌ 获取产品图片失败:', error);
        });
}

// 加载产品社交数据
function loadProductSocialData(productId) {
    console.log('📊 开始加载产品社交数据，产品ID:', productId);

    // 检查收藏状态
    if (window.productFavorite) {
        window.productFavorite.checkFavoriteStatus(productId);
    }

    // 加载收藏数量
    axios.get(`/api/favorites/count/${productId}`)
        .then(response => {
            if (response.data.success) {
                const countElement = document.querySelector(`[data-product-id="${productId}"] .favorite-count`);
                if (countElement) {
                    countElement.textContent = response.data.count;
                }
            }
        })
        .catch(error => {
            console.error('获取收藏数量失败:', error);
        });

    // 加载分享数量
    axios.get(`/api/shares/count/${productId}`)
        .then(response => {
            if (response.data.success) {
                const countElement = document.querySelector(`[data-product-id="${productId}"] .share-count`);
                if (countElement) {
                    countElement.textContent = response.data.count;
                }
            }
        })
        .catch(error => {
            console.error('获取分享数量失败:', error);
        });

    // 加载评论数量和平均评分
    axios.get(`/api/comments/product/${productId}?status=1&page=1&size=1`)
        .then(response => {
            if (response.data.success) {
                const countElement = document.querySelector(`[data-product-id="${productId}"] .comment-count`);
                if (countElement) {
                    countElement.textContent = response.data.total || 0;
                }

                const ratingCountElement = document.getElementById('productRatingCount');
                if (ratingCountElement) {
                    ratingCountElement.textContent = `(${response.data.total || 0}条评论)`;
                }
            }
        })
        .catch(error => {
            console.error('获取评论数量失败:', error);
        });

    // 加载平均评分
    axios.get(`/api/comments/rating/${productId}`)
        .then(response => {
            if (response.data.success) {
                const rating = response.data.rating || 0;
                const ratingScoreElement = document.getElementById('productRatingScore');
                const ratingStarsElement = document.getElementById('productRatingStars');

                if (ratingScoreElement) {
                    ratingScoreElement.textContent = rating.toFixed(1);
                }

                if (ratingStarsElement) {
                    const fullStars = Math.floor(rating);
                    const hasHalfStar = rating % 1 >= 0.5;
                    let starsHtml = '';

                    for (let i = 0; i < 5; i++) {
                        if (i < fullStars) {
                            starsHtml += '<span class="star filled">★</span>';
                        } else if (i === fullStars && hasHalfStar) {
                            starsHtml += '<span class="star half">★</span>';
                        } else {
                            starsHtml += '<span class="star">☆</span>';
                        }
                    }

                    ratingStarsElement.innerHTML = starsHtml;
                }
            }
        })
        .catch(error => {
            console.error('获取平均评分失败:', error);
        });
}

// 显示产品图片
function displayProductImages(images) {
    const thumbnailsContainer = document.getElementById('productThumbnails');
    if (!thumbnailsContainer) {
        console.warn('⚠️ 缩略图容器未找到');
        return;
    }

    if (!images || !images.length) {
        console.warn('⚠️ 没有图片数据');
        thumbnailsContainer.innerHTML = '<p style="text-align: center; color: #999;">暂无更多图片</p>';
        return;
    }

    console.log('🎨 开始显示产品图片，数量:', images.length);

    const thumbnailsHTML = images.map((image, index) => {
        const imageUrl = getImageUrl(image.image_url || image.imageUrl);
        const isPrimary = image.is_primary || image.isPrimary || index === 0;

        console.log(`📷 图片${index + 1}:`, {
            url: imageUrl,
            isPrimary: isPrimary,
            originalData: image
        });

        return `
            <img src="${imageUrl}"
                 alt="产品图片${index + 1}"
                 class="thumbnail-image ${isPrimary ? 'active' : ''}"
                 onclick="changeMainImage('${imageUrl}')"
                 onerror="this.src='${getImageUrl(null)}'">
        `;
    }).join('');

    thumbnailsContainer.innerHTML = thumbnailsHTML;
    console.log('✅ 产品图片显示完成');
}

// 切换主图
window.changeMainImage = function(imageUrl) {
    const mainImage = document.getElementById('mainProductImage');
    if (mainImage) {
        mainImage.src = imageUrl;
    }

    // 更新缩略图选中状态
    const thumbnails = document.querySelectorAll('.thumbnail-image');
    thumbnails.forEach(thumb => {
        thumb.classList.remove('active');
        if (thumb.src === imageUrl) {
            thumb.classList.add('active');
        }
    });
};

// 改变数量
window.changeQuantity = function(delta) {
    const input = document.getElementById('productQuantity');
    if (!input) return;

    const currentValue = parseInt(input.value) || 1;
    const newValue = Math.max(1, Math.min(parseInt(input.max) || 1, currentValue + delta));
    input.value = newValue;
};

// 验证数量
window.validateQuantity = function() {
    const input = document.getElementById('productQuantity');
    if (!input) return;

    const value = parseInt(input.value) || 1;
    const max = parseInt(input.max) || 1;

    if (value < 1) {
        input.value = 1;
    } else if (value > max) {
        input.value = max;
        window.userCenter?.showMessage(`最大可购买 ${max} 件`, 'warning');
    }
};

// 加入购物车
window.addToCart = function(productId) {
    console.log('🛒 开始加入购物车，产品ID:', productId);

    const quantity = parseInt(document.getElementById('productQuantity')?.value) || 1;
    console.log('📦 购买数量:', quantity);

    if (!window.userCenter?.authManager?.isLoggedIn()) {
        console.log('⚠️ 用户未登录');
        window.userCenter?.showMessage('请先登录后再加入购物车', 'warning');
        setTimeout(() => {
            window.location.href = 'land.html';
        }, 1500);
        return;
    }

    const currentUser = window.userCenter.authManager.getCurrentUser();
    console.log('👤 当前用户:', currentUser);

    const cartData = {
        userPhone: currentUser.phone || currentUser.userId,
        productId: productId,
        quantity: quantity
    };

    console.log('📋 购物车数据:', cartData);

    axios.post('/api/cart/add', cartData)
        .then(response => {
            console.log('🛒 购物车API响应:', response.data);

            if (response.data.success) {
                window.userCenter?.showMessage('已加入购物车', 'success');

                // 更新购物车计数
                updateCartCount();

                // 刷新购物车内容
                if (window.shoppingCart) {
                    window.shoppingCart.loadCartFromServer();
                    console.log('🔄 购物车内容已刷新');
                }

                closeProductDetailModal();
            } else {
                window.userCenter?.showMessage('加入购物车失败: ' + response.data.message, 'error');
            }
        })
        .catch(error => {
            console.error('❌ 加入购物车失败:', error);
            window.userCenter?.showMessage('网络错误，请重试', 'error');
        });
};

// 创建全局购物车更新事件系统
window.cartUpdateEvent = new CustomEvent('cartUpdated');

// 更新购物车计数
function updateCartCount() {
    console.log('🔄 开始更新购物车计数');

    if (!window.userCenter?.authManager?.isLoggedIn()) {
        console.log('⚠️ 用户未登录，设置购物车计数为0');
        updateAllCartDisplays(0);
        return;
    }

    const currentUser = window.userCenter.authManager.getCurrentUser();
    const userIdentifier = currentUser.phone || currentUser.userId;

    console.log('🔄 更新购物车计数，用户:', userIdentifier);

    axios.get(`/api/cart/count/${userIdentifier}`)
        .then(response => {
            console.log('📊 购物车计数API响应:', response.data);

            if (response.data.success) {
                const count = response.data.count || 0;
                console.log('📊 获取到的购物车数量:', count);

                // 更新所有购物车显示
                updateAllCartDisplays(count);

                // 触发全局购物车更新事件
                window.dispatchEvent(new CustomEvent('cartUpdated', {
                    detail: { count: count }
                }));

            } else {
                console.warn('⚠️ 获取购物车计数失败:', response.data.message);
            }
        })
        .catch(error => {
            console.error('❌ 获取购物车计数失败:', error);
        });
}

// 统一更新所有购物车显示的函数
function updateAllCartDisplays(count) {
    console.log('🔄 统一更新所有购物车显示，数量:', count);

    // 更新主页购物车计数
    const cartCount = document.querySelector('.cart-count');
    if (cartCount) {
        cartCount.textContent = count;
        console.log('✅ 主页购物车计数已更新:', count);
    } else {
        console.warn('⚠️ 未找到主页购物车计数元素 .cart-count');
    }

    // 更新个人中心购物车计数
    const statCartCount = document.querySelector('.stat-cart-count');
    if (statCartCount) {
        statCartCount.textContent = count;
        console.log('✅ 个人中心购物车计数已更新:', count);
    } else {
        console.warn('⚠️ 未找到个人中心购物车计数元素 .stat-cart-count');
    }

    // 更新购物车模态框中的计数
    const modalCartCount = document.querySelector('.modal-cart-count');
    if (modalCartCount) {
        modalCartCount.textContent = count;
        console.log('✅ 购物车模态框计数已更新:', count);
    }

    // 更新用户中心的购物车数据
    if (window.userCenter) {
        // 确保userDetailData存在
        if (!window.userCenter.userDetailData) {
            window.userCenter.userDetailData = {};
        }
        window.userCenter.userDetailData.cartItemCount = count;

        // 立即更新个人中心的统计显示
        if (typeof window.userCenter.updateUserStats === 'function') {
            window.userCenter.updateUserStats();
            console.log('✅ 用户中心购物车数据已更新:', count);
        } else {
            console.warn('⚠️ window.userCenter.updateUserStats 不是函数');
        }
    } else {
        console.warn('⚠️ window.userCenter 不存在');
    }

    // 强制更新个人中心界面
    setTimeout(() => {
        const userCenterModal = document.querySelector('.user-center-modal');
        if (userCenterModal && userCenterModal.style.display !== 'none') {
            console.log('🔄 强制刷新个人中心界面');
            const statCartCountAgain = document.querySelector('.stat-cart-count');
            if (statCartCountAgain) {
                statCartCountAgain.textContent = count;
                console.log('✅ 强制更新个人中心购物车计数:', count);
            }
        }
    }, 100);
}

// 扩展UserCenter类的方法
if (window.UserCenter) {
    // 显示收藏夹模态框
    window.UserCenter.prototype.showFavoritesModal = function() {
        const modal = document.querySelector('.favorites-modal');
        if (modal) {
            modal.style.display = 'flex';
            this.loadUserFavorites();
        }
    };

    // 关闭收藏夹模态框
    window.UserCenter.prototype.closeFavoritesModal = function() {
        const modal = document.querySelector('.favorites-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    };

    // 显示评论模态框
    window.UserCenter.prototype.showCommentsModal = function() {
        const modal = document.querySelector('.comments-modal');
        if (modal) {
            modal.style.display = 'flex';
            this.loadUserComments();
        }
    };

    // 关闭评论模态框
    window.UserCenter.prototype.closeCommentsModal = function() {
        const modal = document.querySelector('.comments-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    };

    // 显示分享模态框
    window.UserCenter.prototype.showSharesModal = function() {
        const modal = document.querySelector('.shares-modal');
        if (modal) {
            modal.style.display = 'flex';
            this.loadUserShares();
        }
    };

    // 关闭分享模态框
    window.UserCenter.prototype.closeSharesModal = function() {
        const modal = document.querySelector('.shares-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    };

    // 加载用户收藏
    window.UserCenter.prototype.loadUserFavorites = function(page = 1) {
        const currentUser = this.authManager.getCurrentUser();
        const userPhone = currentUser.phone || currentUser.userId;

        axios.get(`/api/favorites/user/${userPhone}?page=${page}&size=10`)
            .then(response => {
                if (response.data.success) {
                    this.displayFavorites(response.data.data, response.data.total, page);
                } else {
                    this.showMessage('加载收藏失败', 'error');
                }
            })
            .catch(error => {
                console.error('加载收藏失败:', error);
                this.showMessage('加载收藏失败', 'error');
            });
    };

    // 显示收藏列表
    window.UserCenter.prototype.displayFavorites = function(favorites, total, page) {
        const container = document.getElementById('favoritesList');
        if (!container) return;

        if (!favorites || favorites.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-heart" style="font-size: 3rem; color: #ddd; margin-bottom: 1rem;"></i>
                    <p>暂无收藏商品</p>
                    <button onclick="window.userCenter?.closeFavoritesModal(); window.location.href='#products'" class="btn-browse-products">
                        去逛逛
                    </button>
                </div>
            `;
            return;
        }

        let html = '<div class="favorites-grid">';
        favorites.forEach(favorite => {
            html += `
                <div class="favorite-item">
                    <img src="${favorite.productImageUrl || '/images/default-product.png'}" alt="${favorite.productName}" class="favorite-image">
                    <div class="favorite-info">
                        <h4>${favorite.productName}</h4>
                        <p class="favorite-price">¥${favorite.productPrice}</p>
                        <div class="favorite-actions">
                            <button onclick="window.viewProductDetail(${favorite.productId})" class="btn-view-detail">查看详情</button>
                            <button onclick="window.userCenter?.removeFavorite(${favorite.productId})" class="btn-remove-favorite">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });
        html += '</div>';

        container.innerHTML = html;
        this.updatePagination('favoritesPagination', total, page, 10, (newPage) => this.loadUserFavorites(newPage));
    };

    // 移除收藏
    window.UserCenter.prototype.removeFavorite = function(productId) {
        const currentUser = this.authManager.getCurrentUser();
        const userPhone = currentUser.phone || currentUser.userId;

        axios.post('/api/favorites/remove', {
            userPhone: userPhone,
            productId: productId
        })
        .then(response => {
            if (response.data.success) {
                this.showMessage('取消收藏成功', 'success');
                this.loadUserFavorites(); // 重新加载收藏列表
            } else {
                this.showMessage('取消收藏失败', 'error');
            }
        })
        .catch(error => {
            console.error('取消收藏失败:', error);
            this.showMessage('取消收藏失败', 'error');
        });
    };

    // 加载用户评论
    window.UserCenter.prototype.loadUserComments = function(page = 1) {
        const currentUser = this.authManager.getCurrentUser();
        const userPhone = currentUser.phone || currentUser.userId;

        // 显示加载状态
        const container = document.getElementById('commentsList');
        if (container) {
            container.innerHTML = `
                <div class="loading-state">
                    <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #ccc; margin-bottom: 1rem;"></i>
                    <p>加载评论中...</p>
                </div>
            `;
        }

        axios.get(`/api/comments/user/${userPhone}?page=${page}&size=10`)
            .then(response => {
                if (response.data.success) {
                    this.displayComments(response.data.data, response.data.total, page);
                } else {
                    this.showMessage('加载评论失败', 'error');
                    if (container) {
                        container.innerHTML = `
                            <div class="error-state">
                                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; color: #f56565; margin-bottom: 1rem;"></i>
                                <p>加载评论失败</p>
                            </div>
                        `;
                    }
                }
            })
            .catch(error => {
                console.error('加载评论失败:', error);
                this.showMessage('加载评论失败', 'error');
                if (container) {
                    container.innerHTML = `
                        <div class="error-state">
                            <i class="fas fa-exclamation-triangle" style="font-size: 2rem; color: #f56565; margin-bottom: 1rem;"></i>
                            <p>网络错误，请重试</p>
                        </div>
                    `;
                }
            });
    };

    // 显示评论列表
    window.UserCenter.prototype.displayComments = function(comments, total, page) {
        const container = document.getElementById('commentsList');
        if (!container) return;

        if (!comments || comments.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-comment-dots" style="font-size: 3rem; color: #ddd; margin-bottom: 1rem;"></i>
                    <p>暂无评论记录</p>
                </div>
            `;
            return;
        }

        let html = '<div class="comments-list-container">';
        comments.forEach(comment => {
            const stars = '★'.repeat(comment.rating) + '☆'.repeat(5 - comment.rating);
            const commentDate = new Date(comment.createdTime).toLocaleDateString();

            html += `
                <div class="comment-item">
                    <div class="comment-header">
                        <img src="${comment.productImageUrl || '/images/default-product.png'}" alt="${comment.productName}" class="comment-product-image">
                        <div class="comment-product-info">
                            <h4>${comment.productName}</h4>
                            <div class="comment-rating">${stars}</div>
                            <span class="comment-date">${commentDate}</span>
                        </div>
                        <div class="comment-status">
                            ${comment.status === 0 ? '<span class="status-pending">待审核</span>' :
                              comment.status === 1 ? '<span class="status-approved">已通过</span>' :
                              '<span class="status-rejected">已拒绝</span>'}
                        </div>
                    </div>
                    <div class="comment-content">
                        <p>${comment.content}</p>
                        ${comment.images ? `
                            <div class="comment-images">
                                ${comment.images.split(',').map(img => `<img src="${img}" alt="评论图片" class="comment-image">`).join('')}
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        });
        html += '</div>';

        container.innerHTML = html;
        this.updatePagination('commentsPagination', total, page, 10, (newPage) => this.loadUserComments(newPage));
    };

    // 加载用户分享
    window.UserCenter.prototype.loadUserShares = function(page = 1) {
        const currentUser = this.authManager.getCurrentUser();
        const userPhone = currentUser.phone || currentUser.userId;

        axios.get(`/api/shares/user/${userPhone}?page=${page}&size=10`)
            .then(response => {
                if (response.data.success) {
                    this.displayShares(response.data.data, response.data.total, page);
                } else {
                    this.showMessage('加载分享失败', 'error');
                }
            })
            .catch(error => {
                console.error('加载分享失败:', error);
                this.showMessage('加载分享失败', 'error');
            });
    };

    // 显示分享列表
    window.UserCenter.prototype.displayShares = function(shares, total, page) {
        const container = document.getElementById('sharesList');
        if (!container) return;

        if (!shares || shares.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-share-alt" style="font-size: 3rem; color: #ddd; margin-bottom: 1rem;"></i>
                    <p>暂无分享记录</p>
                </div>
            `;
            return;
        }

        let html = '<div class="shares-list-container">';
        shares.forEach(share => {
            const shareDate = new Date(share.createdTime).toLocaleDateString();
            const shareTypeText = this.getShareTypeText(share.shareType);

            html += `
                <div class="share-item">
                    <img src="${share.productImageUrl || '/images/default-product.png'}" alt="${share.productName}" class="share-product-image">
                    <div class="share-info">
                        <h4>${share.productName}</h4>
                        <div class="share-details">
                            <span class="share-type">${shareTypeText}</span>
                            <span class="share-date">${shareDate}</span>
                        </div>
                    </div>
                    <div class="share-actions">
                        <button onclick="window.viewProductDetail(${share.productId})" class="btn-view-product">查看商品</button>
                    </div>
                </div>
            `;
        });
        html += '</div>';

        container.innerHTML = html;
        this.updatePagination('sharesPagination', total, page, 10, (newPage) => this.loadUserShares(newPage));
    };

    // 获取分享类型文本
    window.UserCenter.prototype.getShareTypeText = function(shareType) {
        const typeMap = {
            'wechat': '微信',
            'weibo': '微博',
            'qq': 'QQ',
            'link': '复制链接'
        };
        return typeMap[shareType] || shareType;
    };

    // 更新分页
    window.UserCenter.prototype.updatePagination = function(containerId, total, currentPage, pageSize, onPageChange) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const totalPages = Math.ceil(total / pageSize);
        if (totalPages <= 1) {
            container.innerHTML = '';
            return;
        }

        let html = '<div class="pagination">';

        // 上一页
        if (currentPage > 1) {
            html += `<button onclick="${onPageChange.toString().replace('newPage', currentPage - 1)}" class="page-btn">上一页</button>`;
        }

        // 页码
        for (let i = 1; i <= totalPages; i++) {
            if (i === currentPage) {
                html += `<button class="page-btn active">${i}</button>`;
            } else {
                html += `<button onclick="${onPageChange.toString().replace('newPage', i)}" class="page-btn">${i}</button>`;
            }
        }

        // 下一页
        if (currentPage < totalPages) {
            html += `<button onclick="${onPageChange.toString().replace('newPage', currentPage + 1)}" class="page-btn">下一页</button>`;
        }

        html += '</div>';
        container.innerHTML = html;
    };
}

// 更新个人中心订单统计
function updateOrderStats() {
    if (!window.userCenter?.authManager?.isLoggedIn()) {
        return;
    }

    const currentUser = window.userCenter.authManager.getCurrentUser();
    const userIdentifier = currentUser.phone || currentUser.userId;

    console.log('🔄 更新订单统计，用户:', userIdentifier);

    // 获取订单统计信息
    axios.get(`/api/user-detail/${userIdentifier}/orders?limit=100`)
        .then(response => {
            if (response.data.success && response.data.data) {
                const orderData = response.data.data;
                const totalCount = orderData.totalCount || 0;
                const pendingCount = orderData.pendingCount || 0;

                // 更新个人中心订单统计
                const statOrderCount = document.querySelector('.stat-order-count');
                if (statOrderCount) {
                    statOrderCount.textContent = totalCount;
                    console.log('✅ 个人中心订单统计已更新:', totalCount);
                }

                // 更新用户中心的订单数据
                if (window.userCenter && window.userCenter.userDetailData) {
                    window.userCenter.userDetailData.totalOrderCount = totalCount;
                    window.userCenter.userDetailData.pendingOrderCount = pendingCount;
                }
            }
        })
        .catch(error => {
            console.error('❌ 获取订单统计失败:', error);
        });
}

// 立即购买
window.buyNow = function(productId) {
    console.log('⚡ 开始立即购买，产品ID:', productId);

    const quantity = parseInt(document.getElementById('productQuantity')?.value) || 1;
    console.log('📦 购买数量:', quantity);

    if (!window.userCenter?.authManager?.isLoggedIn()) {
        console.log('⚠️ 用户未登录');
        window.userCenter?.showMessage('请先登录后再购买', 'warning');
        setTimeout(() => {
            window.location.href = 'land.html';
        }, 1500);
        return;
    }

    const currentUser = window.userCenter.authManager.getCurrentUser();
    console.log('👤 当前用户:', currentUser);

    // 获取产品信息
    const productModal = document.querySelector('.product-detail-modal');
    const productName = productModal?.querySelector('.header-text h2')?.textContent || '未知产品';
    const productPrice = productModal?.querySelector('.price-value')?.textContent || '0.00';

    // 创建订单数据（使用OrderCreateDTO格式）
    const orderData = {
        userPhone: currentUser.phone || currentUser.userId,
        totalAmount: parseFloat(productPrice) * quantity,
        receiverName: currentUser.name || currentUser.phone,
        receiverPhone: currentUser.phone,
        receiverAddress: '默认收货地址', // 这里应该从用户地址中选择
        remark: '立即购买订单'
    };

    console.log('📋 订单数据:', orderData);

    // 调用订单创建API
    axios.post('/api/order/create', orderData)
        .then(response => {
            console.log('📦 订单创建响应:', response.data);

            if (response.data.success) {
                const orderNo = response.data.orderNo || response.data.data;
                window.userCenter?.showMessage(`订单创建成功！订单号：${orderNo}`, 'success');

                // 关闭产品详情模态框
                closeProductDetailModal();

                // 更新订单统计
                updateOrderStats();

                // 可以跳转到订单详情页面或支付页面
                setTimeout(() => {
                    if (confirm('订单创建成功！是否查看订单详情？')) {
                        // 这里可以跳转到订单详情页面
                        console.log('跳转到订单详情页面，订单号:', orderNo);
                    }
                }, 1000);
            } else {
                window.userCenter?.showMessage('订单创建失败: ' + response.data.message, 'error');
            }
        })
        .catch(error => {
            console.error('❌ 订单创建失败:', error);
            window.userCenter?.showMessage('网络错误，请重试', 'error');
        });
};

// 添加产品详情样式
function addProductDetailStyles() {
    if (document.querySelector('#product-detail-styles')) return;

    const styles = `
        <style id="product-detail-styles">
        /* 产品详情模态框样式 */
        .product-detail-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .product-detail-modal.show {
            opacity: 1;
            visibility: visible;
        }

        .product-detail-modal .modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(5px);
        }

        .product-detail-modal .modal-content {
            position: relative;
            background: linear-gradient(135deg, #f0fdfa 0%, #ecfeff 100%);
            border-radius: 24px;
            width: 95%;
            max-width: 1200px;
            max-height: 90vh;
            margin: 2.5vh auto;
            overflow: hidden;
            box-shadow: 0 25px 80px rgba(6, 182, 212, 0.2);
            transform: translateY(-20px);
            transition: transform 0.3s ease;
        }

        .product-detail-modal.show .modal-content {
            transform: translateY(0);
        }

        /* 模态框头部 */
        .product-detail-modal .modal-header {
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            color: white;
            padding: 25px 30px 25px 30px;
            position: relative;
            border-radius: 24px 24px 0 0;
        }

        .product-detail-modal .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }

        .product-detail-modal .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .product-detail-modal .header-icon {
            width: 55px;
            height: 55px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.6rem;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .product-detail-modal .header-text h2 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }

        .product-detail-modal .header-text p {
            margin: 5px 0 0 0;
            opacity: 0.9;
            font-size: 0.95rem;
        }

        .product-detail-modal .close-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            font-size: 1.4rem;
            cursor: pointer;
            padding: 10px;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.3);
            position: absolute;
            top: 15px;
            right: 15px;
            z-index: 1000;
        }

        .product-detail-modal .close-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        /* 模态框内容 */
        .product-detail-modal .modal-body {
            padding: 30px;
            max-height: 70vh;
            overflow-y: auto;
        }

        .product-detail-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
        }

        /* 产品图片区域 */
        .product-images-section {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .main-image-container {
            position: relative;
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(6, 182, 212, 0.1);
            border: 2px solid rgba(6, 182, 212, 0.1);
        }

        .main-product-image {
            width: 100%;
            height: 400px;
            object-fit: cover;
            border-radius: 12px;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .main-product-image:hover {
            transform: scale(1.05);
        }

        .image-zoom-hint {
            position: absolute;
            bottom: 30px;
            right: 30px;
            background: rgba(6, 182, 212, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .main-image-container:hover .image-zoom-hint {
            opacity: 1;
        }

        .thumbnail-images-container {
            background: white;
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 4px 15px rgba(6, 182, 212, 0.08);
        }

        .thumbnail-images {
            display: flex;
            gap: 10px;
            overflow-x: auto;
            padding: 5px;
        }

        .thumbnail-image {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 8px;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            flex-shrink: 0;
        }

        .thumbnail-image:hover,
        .thumbnail-image.active {
            border-color: #06b6d4;
            transform: scale(1.1);
        }

        /* 产品信息区域 */
        .product-info-section {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .price-section {
            background: white;
            padding: 25px;
            border-radius: 16px;
            box-shadow: 0 8px 25px rgba(6, 182, 212, 0.1);
            border: 2px solid rgba(6, 182, 212, 0.1);
        }

        .current-price {
            display: flex;
            align-items: baseline;
            gap: 5px;
            margin-bottom: 10px;
        }

        .currency {
            font-size: 1.5rem;
            color: #dc2626;
            font-weight: 600;
        }

        .price-value {
            font-size: 2.5rem;
            color: #dc2626;
            font-weight: 700;
            font-family: 'Arial', sans-serif;
        }

        .original-price {
            color: #9ca3af;
            text-decoration: line-through;
            font-size: 1rem;
        }

        .stock-section {
            background: white;
            padding: 20px 25px;
            border-radius: 16px;
            box-shadow: 0 4px 15px rgba(6, 182, 212, 0.08);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .stock-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .stock-label {
            color: #64748b;
            font-weight: 500;
        }

        .stock-value.in-stock {
            color: #059669;
            font-weight: 600;
        }

        .stock-value.out-of-stock {
            color: #dc2626;
            font-weight: 600;
        }

        .new-badge {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .description-section,
        .quantity-section {
            background: white;
            padding: 25px;
            border-radius: 16px;
            box-shadow: 0 4px 15px rgba(6, 182, 212, 0.08);
        }

        .description-section h3,
        .quantity-section h3 {
            margin: 0 0 15px 0;
            color: #0f766e;
            font-size: 1.1rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .description-content {
            color: #64748b;
            line-height: 1.6;
            font-size: 0.95rem;
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 10px;
        }

        .quantity-btn {
            width: 40px;
            height: 40px;
            border: 2px solid #06b6d4;
            background: white;
            color: #06b6d4;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .quantity-btn:hover {
            background: #06b6d4;
            color: white;
            transform: scale(1.1);
        }

        .quantity-input {
            width: 80px;
            height: 40px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            text-align: center;
            font-size: 1rem;
            font-weight: 600;
            color: #0f766e;
        }

        .quantity-input:focus {
            outline: none;
            border-color: #06b6d4;
        }

        .quantity-hint {
            color: #64748b;
            font-size: 0.85rem;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
        }

        .btn-add-cart,
        .btn-buy-now {
            flex: 1;
            padding: 15px 20px;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-add-cart {
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            color: white;
        }

        .btn-add-cart:hover {
            background: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(6, 182, 212, 0.3);
        }

        .btn-buy-now {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            color: white;
        }

        .btn-buy-now:hover {
            background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3);
        }

        .btn-add-cart.disabled,
        .btn-buy-now.disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .product-detail-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .product-detail-modal .modal-content {
                width: 98%;
                margin: 1vh auto;
                max-height: 95vh;
            }

            .main-product-image {
                height: 300px;
            }

            .action-buttons {
                flex-direction: column;
            }

            .price-value {
                font-size: 2rem;
            }
        }
        </style>
    `;

    document.head.insertAdjacentHTML('beforeend', styles);
}

// ==================== 个人中心功能扩展 ====================

// 查看购物车
window.viewShoppingCart = function() {
    if (!window.userCenter?.authManager?.isLoggedIn()) {
        window.userCenter?.showMessage('请先登录后再查看购物车', 'warning');
        setTimeout(() => {
            window.location.href = 'land.html';
        }, 1500);
        return;
    }

    // 关闭用户中心模态框
    window.userCenter?.toggleUserCenter(false);

    // 打开购物车模态框
    if (window.shoppingCart) {
        window.shoppingCart.toggleCart(true);
    } else {
        window.userCenter?.showMessage('购物车功能加载中...', 'info');
    }
};

// ==================== 产品收藏、分享、评论功能 ====================

// 产品收藏功能
window.productFavorite = {
    // 切换收藏状态
    toggleFavorite: function(productId) {
        if (!window.userCenter?.authManager?.isLoggedIn()) {
            window.userCenter?.showMessage('请先登录后再收藏', 'warning');
            setTimeout(() => window.location.href = 'land.html', 1500);
            return;
        }

        const currentUser = window.userCenter.authManager.getCurrentUser();
        const userPhone = currentUser.phone || currentUser.userId;

        // 先检查当前收藏状态
        axios.get(`/api/favorites/check?userPhone=${userPhone}&productId=${productId}`)
            .then(response => {
                if (response.data.success) {
                    if (response.data.isFavorited) {
                        // 取消收藏
                        this.removeFavorite(userPhone, productId);
                    } else {
                        // 添加收藏
                        this.addFavorite(userPhone, productId);
                    }
                }
            })
            .catch(error => {
                console.error('检查收藏状态失败:', error);
                window.userCenter?.showMessage('操作失败', 'error');
            });
    },

    // 添加收藏
    addFavorite: function(userPhone, productId) {
        axios.post('/api/favorites/add', {
            userPhone: userPhone,
            productId: productId
        })
        .then(response => {
            if (response.data.success) {
                window.userCenter?.showMessage('收藏成功', 'success');
                this.updateFavoriteButton(productId, true);
                this.updateFavoriteCount(productId);
            } else {
                window.userCenter?.showMessage(response.data.message || '收藏失败', 'error');
            }
        })
        .catch(error => {
            console.error('收藏失败:', error);
            window.userCenter?.showMessage('收藏失败', 'error');
        });
    },

    // 取消收藏
    removeFavorite: function(userPhone, productId) {
        axios.post('/api/favorites/remove', {
            userPhone: userPhone,
            productId: productId
        })
        .then(response => {
            if (response.data.success) {
                window.userCenter?.showMessage('取消收藏成功', 'success');
                this.updateFavoriteButton(productId, false);
                this.updateFavoriteCount(productId);
            } else {
                window.userCenter?.showMessage(response.data.message || '取消收藏失败', 'error');
            }
        })
        .catch(error => {
            console.error('取消收藏失败:', error);
            window.userCenter?.showMessage('取消收藏失败', 'error');
        });
    },

    // 更新收藏按钮状态
    updateFavoriteButton: function(productId, isFavorited) {
        const favoriteBtn = document.querySelector(`[data-product-id="${productId}"] .btn-favorite`);
        if (favoriteBtn) {
            if (isFavorited) {
                favoriteBtn.classList.add('favorited');
                favoriteBtn.innerHTML = '<i class="fas fa-heart"></i> 已收藏';
            } else {
                favoriteBtn.classList.remove('favorited');
                favoriteBtn.innerHTML = '<i class="far fa-heart"></i> 收藏';
            }
        }
    },

    // 更新收藏数量
    updateFavoriteCount: function(productId) {
        axios.get(`/api/favorites/count/${productId}`)
            .then(response => {
                if (response.data.success) {
                    const countElement = document.querySelector(`[data-product-id="${productId}"] .favorite-count`);
                    if (countElement) {
                        countElement.textContent = response.data.count;
                    }
                }
            })
            .catch(error => {
                console.error('获取收藏数量失败:', error);
            });
    },

    // 检查收藏状态
    checkFavoriteStatus: function(productId) {
        if (!window.userCenter?.authManager?.isLoggedIn()) {
            return;
        }

        const currentUser = window.userCenter.authManager.getCurrentUser();
        const userPhone = currentUser.phone || currentUser.userId;

        axios.get(`/api/favorites/check?userPhone=${userPhone}&productId=${productId}`)
            .then(response => {
                if (response.data.success) {
                    this.updateFavoriteButton(productId, response.data.isFavorited);
                }
            })
            .catch(error => {
                console.error('检查收藏状态失败:', error);
            });
    }
};

// 产品分享功能
window.productShare = {
    // 分享产品
    shareProduct: function(productId, shareType) {
        if (!window.userCenter?.authManager?.isLoggedIn()) {
            window.userCenter?.showMessage('请先登录后再分享', 'warning');
            setTimeout(() => window.location.href = 'land.html', 1500);
            return;
        }

        const currentUser = window.userCenter.authManager.getCurrentUser();
        const userPhone = currentUser.phone || currentUser.userId;

        // 记录分享行为
        axios.post('/api/shares/add', {
            userPhone: userPhone,
            productId: productId,
            shareType: shareType
        })
        .then(response => {
            if (response.data.success) {
                this.performShare(productId, shareType);
                this.updateShareCount(productId);
            } else {
                window.userCenter?.showMessage('分享失败', 'error');
            }
        })
        .catch(error => {
            console.error('分享失败:', error);
            window.userCenter?.showMessage('分享失败', 'error');
        });
    },

    // 执行分享操作
    performShare: function(productId, shareType) {
        const shareUrl = `${window.location.origin}${window.location.pathname}#product-${productId}`;
        const shareTitle = '息壤集美妆 - 发现美好生活';

        switch (shareType) {
            case 'wechat':
                this.shareToWeChat(shareUrl, shareTitle);
                break;
            case 'weibo':
                this.shareToWeibo(shareUrl, shareTitle);
                break;
            case 'qq':
                this.shareToQQ(shareUrl, shareTitle);
                break;
            case 'link':
                this.copyLink(shareUrl);
                break;
            default:
                this.copyLink(shareUrl);
        }
    },

    // 分享到微信
    shareToWeChat: function(url, title) {
        // 微信分享需要微信SDK，这里模拟
        window.userCenter?.showMessage('请复制链接到微信分享', 'info');
        this.copyLink(url);
    },

    // 分享到微博
    shareToWeibo: function(url, title) {
        const weiboUrl = `https://service.weibo.com/share/share.php?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}`;
        window.open(weiboUrl, '_blank');
        window.userCenter?.showMessage('分享成功', 'success');
    },

    // 分享到QQ
    shareToQQ: function(url, title) {
        const qqUrl = `https://connect.qq.com/widget/shareqq/index.html?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}`;
        window.open(qqUrl, '_blank');
        window.userCenter?.showMessage('分享成功', 'success');
    },

    // 复制链接
    copyLink: function(url) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(url).then(() => {
                window.userCenter?.showMessage('链接已复制到剪贴板', 'success');
            }).catch(() => {
                this.fallbackCopyLink(url);
            });
        } else {
            this.fallbackCopyLink(url);
        }
    },

    // 备用复制方法
    fallbackCopyLink: function(url) {
        const textArea = document.createElement('textarea');
        textArea.value = url;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
            window.userCenter?.showMessage('链接已复制到剪贴板', 'success');
        } catch (err) {
            window.userCenter?.showMessage('复制失败，请手动复制', 'error');
        }
        document.body.removeChild(textArea);
    },

    // 更新分享数量
    updateShareCount: function(productId) {
        axios.get(`/api/shares/count/${productId}`)
            .then(response => {
                if (response.data.success) {
                    const countElement = document.querySelector(`[data-product-id="${productId}"] .share-count`);
                    if (countElement) {
                        countElement.textContent = response.data.count;
                    }
                }
            })
            .catch(error => {
                console.error('获取分享数量失败:', error);
            });
    }
};

// 产品评论功能
window.productComment = {
    currentProductId: null,
    selectedRating: 0,
    uploadedImages: [],

    // 显示评论模态框
    showCommentModal: function(productId, productName, productPrice, productImage) {
        if (!window.userCenter?.authManager?.isLoggedIn()) {
            window.userCenter?.showMessage('请先登录后再评论', 'warning');
            setTimeout(() => window.location.href = 'land.html', 1500);
            return;
        }

        this.currentProductId = productId;
        this.selectedRating = 0;
        this.uploadedImages = [];

        // 设置产品信息
        document.getElementById('commentProductImage').src = productImage || '/images/default-product.png';
        document.getElementById('commentProductName').textContent = productName;
        document.getElementById('commentProductPrice').textContent = `¥${productPrice}`;

        // 重置表单
        document.getElementById('commentContent').value = '';
        document.getElementById('commentCharCount').textContent = '0';
        document.getElementById('commentImagePreview').innerHTML = '';
        this.updateStarRating(0);

        // 显示模态框
        const modal = document.querySelector('.product-comment-modal');
        if (modal) {
            modal.style.display = 'flex';
        }

        // 绑定事件
        this.bindCommentEvents();
    },

    // 关闭评论模态框
    closeCommentModal: function() {
        const modal = document.querySelector('.product-comment-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    },

    // 绑定评论相关事件
    bindCommentEvents: function() {
        // 星级评分事件
        const stars = document.querySelectorAll('#starRating .star');
        stars.forEach((star, index) => {
            star.onclick = () => {
                this.selectedRating = index + 1;
                this.updateStarRating(this.selectedRating);
            };
        });

        // 评论内容字数统计
        const commentContent = document.getElementById('commentContent');
        commentContent.oninput = () => {
            const count = commentContent.value.length;
            document.getElementById('commentCharCount').textContent = count;
        };

        // 图片上传事件
        const imageInput = document.getElementById('commentImageInput');
        imageInput.onchange = (e) => {
            this.handleImageUpload(e.target.files);
        };
    },

    // 更新星级评分显示
    updateStarRating: function(rating) {
        const stars = document.querySelectorAll('#starRating .star');
        const ratingText = document.getElementById('ratingText');

        stars.forEach((star, index) => {
            if (index < rating) {
                star.classList.add('active');
            } else {
                star.classList.remove('active');
            }
        });

        const ratingTexts = ['请选择评分', '很差', '一般', '还行', '不错', '很好'];
        ratingText.textContent = ratingTexts[rating];
    },

    // 处理图片上传
    handleImageUpload: function(files) {
        console.log('🖼️ 开始处理图片上传:', files);
        console.log('📊 当前已上传图片数量:', this.uploadedImages.length);

        if (files.length + this.uploadedImages.length > 5) {
            window.userCenter?.showMessage('最多只能上传5张图片', 'warning');
            return;
        }

        const formData = new FormData();
        let validFiles = 0;

        Array.from(files).forEach(file => {
            console.log('📁 处理文件:', file.name, file.size, file.type);

            if (file.size > 2 * 1024 * 1024) {
                window.userCenter?.showMessage(`图片 ${file.name} 大小不能超过2MB`, 'warning');
                return;
            }

            // 验证文件类型
            if (!file.type.startsWith('image/')) {
                window.userCenter?.showMessage(`文件 ${file.name} 不是图片格式`, 'warning');
                return;
            }

            formData.append('files', file);
            validFiles++;
        });

        if (validFiles === 0) {
            window.userCenter?.showMessage('没有有效的图片文件', 'warning');
            return;
        }

        console.log('✅ 有效文件数量:', validFiles);

        // 显示上传中状态
        window.userCenter?.showMessage('正在上传图片...', 'info');

        axios.post('/api/comments/upload-images', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
        .then(response => {
            console.log('📤 图片上传响应:', response.data);

            if (response.data.success && response.data.imageUrls) {
                this.uploadedImages.push(...response.data.imageUrls);
                this.updateImagePreview();
                window.userCenter?.showMessage(`成功上传 ${response.data.imageUrls.length} 张图片`, 'success');
                console.log('✅ 图片上传成功，当前图片列表:', this.uploadedImages);
            } else {
                console.error('❌ 图片上传失败:', response.data.message);
                window.userCenter?.showMessage(response.data.message || '图片上传失败', 'error');
            }
        })
        .catch(error => {
            console.error('❌ 图片上传网络错误:', error);
            window.userCenter?.showMessage('图片上传失败: ' + (error.response?.data?.message || '网络错误'), 'error');
        });
    },

    // 更新图片预览
    updateImagePreview: function() {
        const preview = document.getElementById('commentImagePreview');
        let html = '';

        this.uploadedImages.forEach((imageUrl, index) => {
            html += `
                <div class="preview-image">
                    <img src="${imageUrl}" alt="评论图片">
                    <button class="remove-image" onclick="window.productComment.removeImage(${index})">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
        });

        preview.innerHTML = html;
    },

    // 移除图片
    removeImage: function(index) {
        this.uploadedImages.splice(index, 1);
        this.updateImagePreview();
    },

    // 提交评论
    submitComment: function() {
        if (this.selectedRating === 0) {
            window.userCenter?.showMessage('请选择评分', 'warning');
            return;
        }

        const content = document.getElementById('commentContent').value.trim();
        if (!content) {
            window.userCenter?.showMessage('请输入评论内容', 'warning');
            return;
        }

        const currentUser = window.userCenter.authManager.getCurrentUser();
        const userPhone = currentUser.phone || currentUser.userId;

        const commentData = {
            productId: this.currentProductId,
            userPhone: userPhone,
            userName: currentUser.name || '用户',
            userAvatar: currentUser.avatar || '/images/avatars/default-avatar.jpg',
            content: content,
            rating: this.selectedRating,
            images: this.uploadedImages.join(',')
        };

        axios.post('/api/comments/add', commentData)
            .then(response => {
                if (response.data.success) {
                    window.userCenter?.showMessage('评论提交成功，等待审核', 'success');
                    this.closeCommentModal();
                } else {
                    window.userCenter?.showMessage(response.data.message || '评论提交失败', 'error');
                }
            })
            .catch(error => {
                console.error('评论提交失败:', error);
                window.userCenter?.showMessage('评论提交失败', 'error');
            });
    },

    // 显示评论列表模态框
    showCommentsListModal: function(productId) {
        this.currentProductId = productId;

        // 显示评论列表模态框
        const modal = document.querySelector('.product-comments-list-modal');
        if (modal) {
            modal.style.display = 'flex';
            this.loadProductComments(productId);
        }
    },

    // 关闭评论列表模态框
    closeCommentsListModal: function() {
        const modal = document.querySelector('.product-comments-list-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    },

    // 加载产品评论
    loadProductComments: function(productId) {
        // 同时加载评论列表和评分统计
        Promise.all([
            axios.get(`/api/comments/product/${productId}?status=1&page=1&size=10`),
            axios.get(`/api/comments/rating/${productId}`)
        ]).then(([commentsResponse, ratingResponse]) => {
            if (commentsResponse.data.success) {
                this.displayCommentsList(commentsResponse.data.data);

                // 更新评论统计
                const statsData = {
                    total: commentsResponse.data.total || 0,
                    averageRating: ratingResponse.data.success ? ratingResponse.data.rating : 0
                };
                this.updateCommentsStats(statsData);
            } else {
                this.displayCommentsList([]);
                this.updateCommentsStats({ total: 0, averageRating: 0 });
            }
        }).catch(error => {
            console.error('加载评论失败:', error);
            this.displayCommentsList([]);
            this.updateCommentsStats({ total: 0, averageRating: 0 });
        });
    },

    // 显示评论列表
    displayCommentsList: function(comments) {
        const commentsList = document.getElementById('productCommentsList');
        if (!commentsList) {
            console.error('找不到产品评论列表容器');
            return;
        }

        if (comments.length === 0) {
            commentsList.innerHTML = `
                <div class="no-comments">
                    <i class="fas fa-comment-slash"></i>
                    <p>暂无评论</p>
                </div>
            `;
            return;
        }

        const commentsHTML = comments.map(comment => {
            // 处理用户头像路径
            let avatarSrc = comment.userAvatar || '/images/default-avatar.png';
            if (avatarSrc && !avatarSrc.startsWith('http') && !avatarSrc.startsWith('/')) {
                if (avatarSrc.startsWith('avatar_')) {
                    avatarSrc = `/images/avatars/${avatarSrc}`;
                } else {
                    avatarSrc = `/images/avatar/${avatarSrc}`;
                }
            }

            return `
            <div class="comment-item">
                <div class="comment-header">
                    <div class="user-info">
                        <img src="${avatarSrc}"
                             alt="用户头像" class="user-avatar"
                             onerror="this.src='/images/default-avatar.png'">
                        <div class="user-details">
                            <span class="user-name">${comment.userName || '匿名用户'}</span>
                            <div class="comment-rating">
                                ${'★'.repeat(comment.rating)}${'☆'.repeat(5 - comment.rating)}
                            </div>
                        </div>
                    </div>
                    <span class="comment-time">${this.formatTime(comment.createdTime)}</span>
                </div>
                <div class="comment-content">
                    <p>${comment.content}</p>
                    ${comment.images ? `
                        <div class="comment-images">
                            ${comment.images.split(',').filter(img => img.trim()).map(img => `
                                <img src="${img.trim()}" alt="评论图片" class="comment-image"
                                     onclick="window.productComment.previewImage('${img.trim()}')">
                            `).join('')}
                        </div>
                    ` : ''}
                </div>
            </div>
            `;
        }).join('');

        commentsList.innerHTML = commentsHTML;
    },

    // 更新评论统计
    updateCommentsStats: function(data) {
        const totalCount = document.getElementById('totalCommentsCount');
        const averageScore = document.getElementById('averageRatingScore');
        const averageStars = document.getElementById('averageRatingStars');

        if (totalCount) totalCount.textContent = data.total || 0;
        if (averageScore) averageScore.textContent = (data.averageRating || 0).toFixed(1);
        if (averageStars) {
            const rating = Math.round(data.averageRating || 0);
            averageStars.innerHTML = '★'.repeat(rating) + '☆'.repeat(5 - rating);
        }
    },

    // 格式化时间
    formatTime: function(timestamp) {
        if (!timestamp) return '未知时间';

        // 处理时间戳格式（可能是秒或毫秒）
        let time = timestamp;
        if (typeof timestamp === 'string') {
            time = parseInt(timestamp);
        }

        // 如果时间戳是秒格式（10位），转换为毫秒
        if (time.toString().length === 10) {
            time = time * 1000;
        }

        const date = new Date(time);

        // 检查日期是否有效
        if (isNaN(date.getTime())) {
            console.warn('无效的时间戳:', timestamp);
            return '时间格式错误';
        }

        // 格式化为中文日期时间
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}`;
    },

    // 预览图片
    previewImage: function(imageUrl) {
        // 创建图片预览模态框
        const modal = document.createElement('div');
        modal.className = 'image-preview-modal';
        modal.innerHTML = `
            <div class="modal-overlay" onclick="this.parentElement.remove()"></div>
            <div class="image-container">
                <img src="${imageUrl}" alt="预览图片">
                <button class="close-btn" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // 添加样式
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 20000;
        `;

        document.body.appendChild(modal);
    }
};

// 管理收货地址
window.manageAddresses = function() {
    console.log('🔍 检查用户登录状态...');

    // 使用统一的认证管理器检查登录状态
    if (!window.userCenter?.authManager?.isLoggedIn()) {
        console.log('❌ 用户未登录');
        window.userCenter?.showMessage('请先登录后再管理收货地址', 'warning');
        setTimeout(() => {
            window.location.href = 'land.html';
        }, 1500);
        return;
    }

    const currentUser = window.userCenter.authManager.getCurrentUser();
    console.log('🔍 当前用户数据:', currentUser);

    if (!currentUser || (!currentUser.phone && !currentUser.userId)) {
        console.log('❌ 用户数据不完整');
        window.userCenter?.showMessage('用户信息不完整，请重新登录', 'warning');
        setTimeout(() => {
            window.location.href = 'land.html';
        }, 1500);
        return;
    }

    console.log('✅ 用户已登录，打开地址管理');
    showAddressManagementModal();
};

// 显示地址管理模态框
function showAddressManagementModal() {
    // 移除已存在的模态框
    const existingModal = document.querySelector('.address-management-modal');
    if (existingModal) {
        existingModal.remove();
    }

    // 创建地址管理模态框
    const modal = createAddressManagementModal();
    document.body.appendChild(modal);

    // 显示模态框
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);

    // 加载地址列表
    loadUserAddresses();
}

// 创建地址管理模态框
function createAddressManagementModal() {
    const modal = document.createElement('div');
    modal.className = 'address-management-modal';
    modal.innerHTML = `
        <div class="modal-overlay" onclick="closeAddressManagementModal()"></div>
        <div class="modal-content">
            <!-- 模态框头部 -->
            <div class="modal-header">
                <div class="header-content">
                    <div class="header-left">
                        <div class="header-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="header-text">
                            <h2>收货地址管理</h2>
                            <p><i class="fas fa-home"></i> 管理您的收货地址</p>
                        </div>
                    </div>
                    <button class="close-btn" onclick="closeAddressManagementModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <!-- 模态框内容 -->
            <div class="modal-body">
                <!-- 添加地址按钮 -->
                <div class="add-address-section">
                    <button class="btn-add-address" onclick="showAddAddressForm()">
                        <i class="fas fa-plus"></i>
                        添加新地址
                    </button>
                </div>

                <!-- 地址列表 -->
                <div class="addresses-container">
                    <div id="addressesList" class="addresses-list">
                        <!-- 地址列表将通过JavaScript动态加载 -->
                        <div class="loading-addresses">
                            <i class="fas fa-spinner fa-spin"></i>
                            正在加载地址列表...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 添加样式
    addAddressManagementStyles();

    return modal;
}

// 关闭地址管理模态框
window.closeAddressManagementModal = function() {
    const modal = document.querySelector('.address-management-modal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
};

// 加载用户地址列表
function loadUserAddresses() {
    // 使用统一的认证管理器获取用户信息
    if (!window.userCenter?.authManager?.isLoggedIn()) {
        console.error('❌ 用户未登录');
        return;
    }

    const currentUser = window.userCenter.authManager.getCurrentUser();
    if (!currentUser) {
        console.error('❌ 无法获取用户信息');
        return;
    }

    const userIdentifier = currentUser.phone || currentUser.userId;
    console.log('🔍 加载地址列表，用户标识:', userIdentifier);

    axios.get(`/api/address/list/${userIdentifier}`)
        .then(response => {
            if (response.data.success) {
                displayAddressesList(response.data.data || []);
            } else {
                displayAddressesList([]);
            }
        })
        .catch(error => {
            console.error('获取地址列表失败:', error);
            displayAddressesList([]);
        });
}

// 显示地址列表
function displayAddressesList(addresses) {
    const addressesList = document.getElementById('addressesList');
    if (!addressesList) return;

    if (addresses.length === 0) {
        addressesList.innerHTML = `
            <div class="no-addresses">
                <div class="no-addresses-icon">
                    <i class="fas fa-map-marker-alt"></i>
                </div>
                <div class="no-addresses-text">
                    <h3>暂无收货地址</h3>
                    <p>添加您的第一个收货地址，享受便捷购物体验</p>
                </div>
                <button class="btn-add-first-address" onclick="showAddAddressForm()">
                    <i class="fas fa-plus"></i>
                    添加收货地址
                </button>
            </div>
        `;
        return;
    }

    const addressesHTML = addresses.map(address => `
        <div class="address-card ${address.isDefault ? 'default-address' : ''}">
            <div class="address-header">
                <div class="address-info">
                    <span class="receiver-name">${address.receiverName}</span>
                    <span class="receiver-phone">${address.receiverPhone}</span>
                </div>
                <div class="address-badges">
                    ${address.isDefault ? '<span class="default-badge">默认</span>' : ''}
                </div>
            </div>
            <div class="address-content">
                <div class="address-detail">
                    <i class="fas fa-map-marker-alt"></i>
                    ${address.province} ${address.city} ${address.district} ${address.detailAddress}
                </div>
            </div>
            <div class="address-actions">
                <button class="btn-edit" onclick="editAddress(${address.id})">
                    <i class="fas fa-edit"></i>
                    编辑
                </button>
                ${!address.isDefault ? `
                    <button class="btn-set-default" onclick="setDefaultAddress(${address.id})">
                        <i class="fas fa-star"></i>
                        设为默认
                    </button>
                ` : ''}
                <button class="btn-delete" onclick="deleteAddress(${address.id})">
                    <i class="fas fa-trash"></i>
                    删除
                </button>
            </div>
        </div>
    `).join('');

    addressesList.innerHTML = addressesHTML;
}

// 显示添加地址表单
window.showAddAddressForm = function() {
    showAddressForm();
};

// 编辑地址
window.editAddress = function(addressId) {
    showAddressForm(addressId);
};

// 设置默认地址
window.setDefaultAddress = function(addressId) {
    // 使用统一的认证管理器检查登录状态
    if (!window.userCenter?.authManager?.isLoggedIn()) {
        window.userCenter?.showMessage('请先登录', 'warning');
        return;
    }

    const currentUser = window.userCenter.authManager.getCurrentUser();
    if (!currentUser) {
        console.error('❌ 无法获取用户信息');
        window.userCenter?.showMessage('用户数据异常，请重新登录', 'error');
        return;
    }

    axios.post(`/api/address/set-default/${addressId}`, {
        userId: currentUser.phone || currentUser.userId
    })
    .then(response => {
        if (response.data.success) {
            window.userCenter?.showMessage('默认地址设置成功', 'success');
            loadUserAddresses(); // 重新加载地址列表
        } else {
            window.userCenter?.showMessage('设置失败: ' + response.data.message, 'error');
        }
    })
    .catch(error => {
        console.error('设置默认地址失败:', error);
        window.userCenter?.showMessage('网络错误，请重试', 'error');
    });
};

// 删除地址
window.deleteAddress = function(addressId) {
    if (!confirm('确定要删除这个收货地址吗？')) {
        return;
    }

    axios.delete(`/api/address/delete/${addressId}`)
        .then(response => {
            if (response.data.success) {
                window.userCenter?.showMessage('地址删除成功', 'success');
                loadUserAddresses(); // 重新加载地址列表
            } else {
                window.userCenter?.showMessage('删除失败: ' + response.data.message, 'error');
            }
        })
        .catch(error => {
            console.error('删除地址失败:', error);
            window.userCenter?.showMessage('网络错误，请重试', 'error');
        });
};

// 显示地址表单
function showAddressForm(addressId = null) {
    // 移除已存在的表单
    const existingForm = document.querySelector('.address-form-modal');
    if (existingForm) {
        existingForm.remove();
    }

    // 创建地址表单模态框
    const formModal = createAddressFormModal(addressId);
    document.body.appendChild(formModal);

    // 显示模态框
    setTimeout(() => {
        formModal.classList.add('show');
    }, 10);

    // 如果是编辑模式，加载地址数据
    if (addressId) {
        loadAddressData(addressId);
    }
}

// 创建地址表单模态框
function createAddressFormModal(addressId) {
    const isEdit = addressId !== null;
    const modal = document.createElement('div');
    modal.className = 'address-form-modal';
    modal.innerHTML = `
        <div class="modal-overlay" onclick="closeAddressForm()"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3>
                    <i class="fas fa-${isEdit ? 'edit' : 'plus'}"></i>
                    ${isEdit ? '编辑地址' : '添加地址'}
                </h3>
                <button class="close-btn" onclick="closeAddressForm()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="addressForm" class="address-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="receiverName">收货人姓名 *</label>
                            <input type="text" id="receiverName" name="receiverName" required>
                        </div>
                        <div class="form-group">
                            <label for="receiverPhone">联系电话 *</label>
                            <input type="tel" id="receiverPhone" name="receiverPhone" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="province">省份 *</label>
                            <select id="province" name="province" required>
                                <option value="">请选择省份</option>
                                <option value="北京市">北京市</option>
                                <option value="上海市">上海市</option>
                                <option value="天津市">天津市</option>
                                <option value="重庆市">重庆市</option>
                                <option value="广东省">广东省</option>
                                <option value="江苏省">江苏省</option>
                                <option value="浙江省">浙江省</option>
                                <option value="山东省">山东省</option>
                                <option value="河南省">河南省</option>
                                <option value="四川省">四川省</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="city">城市 *</label>
                            <input type="text" id="city" name="city" required placeholder="请输入城市">
                        </div>
                        <div class="form-group">
                            <label for="district">区县 *</label>
                            <input type="text" id="district" name="district" required placeholder="请输入区县">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="detailAddress">详细地址 *</label>
                        <textarea id="detailAddress" name="detailAddress" required placeholder="请输入详细地址，如街道、门牌号等"></textarea>
                    </div>
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="isDefault" name="isDefault">
                            <span class="checkmark"></span>
                            设为默认地址
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-cancel" onclick="closeAddressForm()">取消</button>
                <button type="button" class="btn-save" onclick="saveAddress(${addressId})">
                    ${isEdit ? '保存修改' : '添加地址'}
                </button>
            </div>
        </div>
    `;

    return modal;
}

// 关闭地址表单
window.closeAddressForm = function() {
    const modal = document.querySelector('.address-form-modal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
};

// 加载地址数据（编辑模式）
function loadAddressData(addressId) {
    axios.get(`/api/address/${addressId}`)
        .then(response => {
            if (response.data.success) {
                const address = response.data.data;
                fillAddressForm(address);
            } else {
                window.userCenter?.showMessage('获取地址信息失败', 'error');
            }
        })
        .catch(error => {
            console.error('获取地址信息失败:', error);
            window.userCenter?.showMessage('网络错误，请重试', 'error');
        });
}

// 填充地址表单
function fillAddressForm(address) {
    document.getElementById('receiverName').value = address.receiverName || '';
    document.getElementById('receiverPhone').value = address.receiverPhone || '';
    document.getElementById('province').value = address.province || '';
    document.getElementById('city').value = address.city || '';
    document.getElementById('district').value = address.district || '';
    document.getElementById('detailAddress').value = address.detailAddress || '';
    document.getElementById('isDefault').checked = address.isDefault || false;
}

// 保存地址
window.saveAddress = function(addressId) {
    const form = document.getElementById('addressForm');
    if (!form) return;

    // 验证表单
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // 调试：检查认证管理器状态
    console.log('🔍 保存地址 - 检查认证管理器:', window.userCenter?.authManager);
    console.log('🔍 保存地址 - localStorage内容:', localStorage.getItem('sky_current_user'));
    console.log('🔍 保存地址 - 所有localStorage键:', Object.keys(localStorage));

    // 多重验证用户登录状态
    let currentUser = null;

    // 方法1: 直接从localStorage获取 (尝试多个可能的键名)
    const possibleUserKeys = ['sky_current_user', 'current_user', 'user', 'userInfo', 'loginUser'];
    for (const key of possibleUserKeys) {
        const userData = localStorage.getItem(key);
        if (userData) {
            try {
                const parsedUser = JSON.parse(userData);
                if (parsedUser && (parsedUser.phone || parsedUser.userId || parsedUser.username)) {
                    currentUser = parsedUser;
                    console.log(`✅ 方法1 - 从localStorage键 ${key} 获取用户数据成功:`, currentUser);
                    break;
                }
            } catch (error) {
                console.error(`❌ 方法1 - 解析localStorage键 ${key} 失败:`, error);
            }
        }
    }

    // 方法2: 从认证管理器获取
    if (!currentUser && window.userCenter?.authManager) {
        try {
            // 强制重新加载用户数据
            if (typeof window.userCenter.authManager.loadUserFromStorage === 'function') {
                window.userCenter.authManager.loadUserFromStorage();
            }

            if (typeof window.userCenter.authManager.getCurrentUser === 'function') {
                currentUser = window.userCenter.authManager.getCurrentUser();
                console.log('✅ 方法2 - 从认证管理器获取用户数据:', currentUser);
            }
        } catch (error) {
            console.error('❌ 方法2 - 从认证管理器获取用户数据失败:', error);
        }
    }

    // 方法3: 检查全局变量
    if (!currentUser && window.currentUser) {
        currentUser = window.currentUser;
        console.log('✅ 方法3 - 从全局变量获取用户数据:', currentUser);
    }

    // 方法4: 从页面元素获取用户信息
    if (!currentUser) {
        try {
            const userNameElement = document.querySelector('.user-name');
            const userPhoneElement = document.querySelector('.user-phone');
            if (userNameElement && userNameElement.textContent.trim()) {
                currentUser = {
                    name: userNameElement.textContent.trim(),
                    phone: userPhoneElement ? userPhoneElement.textContent.trim() : null,
                    userId: userNameElement.textContent.trim()
                };
                console.log('✅ 方法4 - 从页面元素获取用户数据:', currentUser);
            }
        } catch (error) {
            console.error('❌ 方法4 - 从页面元素获取用户数据失败:', error);
        }
    }

    // 最终验证
    console.log('🔍 最终用户数据:', currentUser);
    console.log('🔍 用户数据类型:', typeof currentUser);
    console.log('🔍 用户手机号:', currentUser?.phone);
    console.log('🔍 用户ID:', currentUser?.userId);
    console.log('🔍 用户名:', currentUser?.username);
    console.log('🔍 用户名称:', currentUser?.name);

    // 检查用户数据是否有效
    if (!currentUser) {
        console.error('❌ 用户数据为空');
        console.error('❌ 所有获取方法都失败了');

        // 最后尝试：检查是否有任何登录相关的cookie或sessionStorage
        console.log('🔍 检查sessionStorage:', Object.keys(sessionStorage));
        console.log('🔍 检查cookie:', document.cookie);

        // 尝试强制重新登录检查
        if (window.userCenter?.authManager && typeof window.userCenter.authManager.isLoggedIn === 'function') {
            const isLoggedIn = window.userCenter.authManager.isLoggedIn();
            console.log('🔍 强制登录状态检查:', isLoggedIn);

            if (!isLoggedIn) {
                alert('登录状态已过期，请重新登录');
                window.location.href = 'land.html';
                return;
            }
        }

        // 如果认证管理器说已登录但获取不到用户数据，使用默认值
        console.log('⚠️ 使用默认用户数据进行地址保存');
        currentUser = {
            phone: 'unknown',
            userId: 'unknown_user_' + Date.now(),
            name: '未知用户'
        };
    }

    // 确保用户标识存在
    let userIdentifier = currentUser.phone || currentUser.userId || currentUser.username || currentUser.name;

    if (!userIdentifier) {
        console.error('❌ 用户数据不完整，缺少所有可能的标识字段');
        console.error('❌ 当前用户数据完整内容:', JSON.stringify(currentUser, null, 2));

        // 尝试使用其他字段作为用户标识
        const possibleIdentifiers = [currentUser.id, currentUser.email, currentUser.mobile];
        for (const identifier of possibleIdentifiers) {
            if (identifier) {
                userIdentifier = identifier;
                console.log('✅ 使用备用用户标识:', userIdentifier);
                break;
            }
        }

        if (!userIdentifier) {
            // 生成临时用户标识
            userIdentifier = 'temp_user_' + Date.now();
            console.log('⚠️ 生成临时用户标识:', userIdentifier);
            currentUser.userId = userIdentifier;
        }
    }

    console.log('✅ 最终使用的用户标识:', userIdentifier);

    console.log('✅ 用户数据验证通过，当前用户信息:', currentUser);

    // 最后的验证：确保用户标识不为空
    if (!userIdentifier || userIdentifier === 'undefined' || userIdentifier === 'null') {
        console.error('❌ 最终用户标识无效:', userIdentifier);

        // 尝试从页面URL或其他地方获取用户信息
        const urlParams = new URLSearchParams(window.location.search);
        const urlUser = urlParams.get('user') || urlParams.get('phone');
        if (urlUser) {
            userIdentifier = urlUser;
            console.log('✅ 从URL参数获取用户标识:', userIdentifier);
        } else {
            // 最后的备用方案：使用当前时间戳作为临时用户
            userIdentifier = 'guest_' + Date.now();
            console.log('⚠️ 使用临时用户标识:', userIdentifier);

            // 提示用户
            if (confirm('检测到用户信息异常，是否继续保存地址？\n（地址将保存为临时用户）')) {
                // 继续执行
            } else {
                return;
            }
        }
    }

    const formData = new FormData(form);
    const addressData = {
        userId: userIdentifier, // 使用确定的用户标识
        receiverName: formData.get('receiverName'),
        receiverPhone: formData.get('receiverPhone'),
        province: formData.get('province'),
        city: formData.get('city'),
        district: formData.get('district'),
        detailAddress: formData.get('detailAddress'),
        isDefault: formData.get('isDefault') === 'on'
    };

    console.log('📤 发送地址数据:', addressData);
    console.log('📤 使用的用户标识:', userIdentifier);

    const url = addressId ? `/api/address/update/${addressId}` : '/api/address/add';
    const method = addressId ? 'put' : 'post';

    console.log('📤 发送地址保存请求:', { url, method, data: addressData });

    axios[method](url, addressData)
        .then(response => {
            console.log('📥 地址保存响应:', response.data);

            if (response.data.success) {
                window.userCenter?.showMessage(addressId ? '地址修改成功' : '地址添加成功', 'success');
                closeAddressForm();
                loadUserAddresses(); // 重新加载地址列表
            } else {
                console.error('❌ 地址保存失败:', response.data.message);
                window.userCenter?.showMessage('保存失败: ' + response.data.message, 'error');
            }
        })
        .catch(error => {
            console.error('❌ 地址保存网络错误:', error);
            console.error('❌ 错误详情:', error.response?.data);

            // 尝试备用的保存方法
            console.log('🔄 尝试备用地址保存方法');
            const backupAddressData = {
                userPhone: userIdentifier, // 使用不同的字段名
                receiverName: addressData.receiverName,
                receiverPhone: addressData.receiverPhone,
                address: `${addressData.province} ${addressData.city} ${addressData.district} ${addressData.detailAddress}`,
                isDefault: addressData.isDefault
            };

            const backupUrl = '/api/user-detail/address/save';
            axios.post(backupUrl, backupAddressData)
                .then(backupResponse => {
                    console.log('📥 备用地址保存响应:', backupResponse.data);

                    if (backupResponse.data.success) {
                        window.userCenter?.showMessage('地址保存成功（备用方法）', 'success');
                        closeAddressForm();
                        loadUserAddresses();
                    } else {
                        window.userCenter?.showMessage('保存失败: ' + (backupResponse.data.message || '未知错误'), 'error');
                    }
                })
                .catch(backupError => {
                    console.error('❌ 备用地址保存也失败:', backupError);
                    window.userCenter?.showMessage('网络错误，请重试: ' + (error.response?.data?.message || error.message), 'error');
                });
        });
};

// 添加地址管理样式
function addAddressManagementStyles() {
    if (document.querySelector('#address-management-styles')) return;

    const styles = `
        <style id="address-management-styles">
        /* 地址管理模态框样式 */
        .address-management-modal,
        .address-form-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 10001;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .address-management-modal.show,
        .address-form-modal.show {
            opacity: 1;
            visibility: visible;
        }

        .address-management-modal .modal-overlay,
        .address-form-modal .modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(5px);
        }

        .address-management-modal .modal-content {
            position: relative;
            background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
            border-radius: 24px;
            width: 95%;
            max-width: 900px;
            max-height: 90vh;
            margin: 2.5vh auto;
            overflow: hidden;
            box-shadow: 0 25px 80px rgba(34, 197, 94, 0.2);
            transform: translateY(-20px);
            transition: transform 0.3s ease;
        }

        .address-form-modal .modal-content {
            position: relative;
            background: white;
            border-radius: 20px;
            width: 95%;
            max-width: 600px;
            max-height: 90vh;
            margin: 2.5vh auto;
            overflow: hidden;
            box-shadow: 0 25px 80px rgba(34, 197, 94, 0.2);
            transform: translateY(-20px);
            transition: transform 0.3s ease;
        }

        .address-management-modal.show .modal-content,
        .address-form-modal.show .modal-content {
            transform: translateY(0);
        }

        /* 模态框头部 */
        .address-management-modal .modal-header {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            color: white;
            padding: 25px 30px;
        }

        .address-form-modal .modal-header {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            color: white;
            padding: 20px 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .address-form-modal .modal-header h3 {
            margin: 0;
            font-size: 1.3rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .address-management-modal .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .address-management-modal .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .address-management-modal .header-icon {
            width: 55px;
            height: 55px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.6rem;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .address-management-modal .header-text h2 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }

        .address-management-modal .header-text p {
            margin: 5px 0 0 0;
            opacity: 0.9;
            font-size: 0.95rem;
        }

        .close-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            padding: 12px;
            border-radius: 50%;
            width: 44px;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        /* 模态框内容 */
        .address-management-modal .modal-body {
            padding: 30px;
            max-height: 70vh;
            overflow-y: auto;
        }

        .add-address-section {
            margin-bottom: 25px;
        }

        .btn-add-address {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }

        .btn-add-address:hover {
            background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
        }

        /* 地址列表 */
        .addresses-list {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .loading-addresses {
            text-align: center;
            padding: 40px;
            color: #6b7280;
        }

        .no-addresses {
            text-align: center;
            padding: 60px 20px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 15px rgba(34, 197, 94, 0.08);
        }

        .no-addresses-icon {
            font-size: 4rem;
            color: #d1d5db;
            margin-bottom: 20px;
        }

        .no-addresses-text h3 {
            margin: 0 0 10px 0;
            color: #374151;
            font-size: 1.3rem;
        }

        .no-addresses-text p {
            margin: 0 0 30px 0;
            color: #6b7280;
        }

        .btn-add-first-address {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }

        .btn-add-first-address:hover {
            background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
        }

        /* 地址卡片 */
        .address-card {
            background: white;
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(34, 197, 94, 0.08);
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .address-card:hover {
            border-color: rgba(34, 197, 94, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(34, 197, 94, 0.15);
        }

        .address-card.default-address {
            border-color: #22c55e;
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        }

        .address-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .address-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .receiver-name {
            font-weight: 600;
            color: #374151;
            font-size: 1.1rem;
        }

        .receiver-phone {
            color: #6b7280;
            font-size: 0.95rem;
        }

        .address-badges {
            display: flex;
            gap: 8px;
        }

        .default-badge {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .address-content {
            margin-bottom: 20px;
        }

        .address-detail {
            color: #6b7280;
            line-height: 1.5;
            display: flex;
            align-items: flex-start;
            gap: 8px;
        }

        .address-detail i {
            color: #22c55e;
            margin-top: 2px;
        }

        .address-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn-edit,
        .btn-set-default,
        .btn-delete {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.3s ease;
        }

        .btn-edit {
            background: #f3f4f6;
            color: #374151;
        }

        .btn-edit:hover {
            background: #e5e7eb;
            transform: translateY(-1px);
        }

        .btn-set-default {
            background: #fef3c7;
            color: #d97706;
        }

        .btn-set-default:hover {
            background: #fde68a;
            transform: translateY(-1px);
        }

        .btn-delete {
            background: #fee2e2;
            color: #dc2626;
        }

        .btn-delete:hover {
            background: #fecaca;
            transform: translateY(-1px);
        }

        /* 地址表单样式 */
        .address-form-modal .modal-body {
            padding: 25px;
            max-height: 70vh;
            overflow-y: auto;
        }

        .address-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-group label {
            font-weight: 600;
            color: #374151;
            font-size: 0.9rem;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 12px 15px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 0.95rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #22c55e;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            font-weight: 500;
            color: #374151;
        }

        .checkbox-label input[type="checkbox"] {
            display: none;
        }

        .checkmark {
            width: 20px;
            height: 20px;
            border: 2px solid #d1d5db;
            border-radius: 4px;
            position: relative;
            transition: all 0.3s ease;
        }

        .checkbox-label input[type="checkbox"]:checked + .checkmark {
            background: #22c55e;
            border-color: #22c55e;
        }

        .checkbox-label input[type="checkbox"]:checked + .checkmark::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .modal-footer {
            padding: 20px 25px;
            border-top: 1px solid #f3f4f6;
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }

        .btn-cancel,
        .btn-save {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 0.95rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-cancel {
            background: #f3f4f6;
            color: #374151;
        }

        .btn-cancel:hover {
            background: #e5e7eb;
        }

        .btn-save {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            color: white;
        }

        .btn-save:hover {
            background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .address-management-modal .modal-content,
            .address-form-modal .modal-content {
                width: 98%;
                margin: 1vh auto;
                max-height: 95vh;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .address-actions {
                justify-content: center;
            }

            .modal-footer {
                flex-direction: column;
            }

            .btn-cancel,
            .btn-save {
                width: 100%;
            }
        }
        </style>
    `;

    document.head.insertAdjacentHTML('beforeend', styles);
}

// 添加用户评论模态框样式
function addUserCommentsModalStyles() {
    if (document.getElementById('user-comments-modal-styles')) return;

    const style = document.createElement('style');
    style.id = 'user-comments-modal-styles';
    style.textContent = `
        .user-comments-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 15000;
            backdrop-filter: blur(5px);
        }
        .user-comments-modal .modal-content {
            background: white;
            border-radius: 20px;
            width: 90%;
            max-width: 700px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
        }
        .user-comments-modal .modal-header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .comments-stats {
            display: flex;
            gap: 20px;
            padding: 20px;
            background: #f8f9fa;
        }
        .comments-stats .stat-card {
            flex: 1;
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .comments-stats .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #ff6b6b;
            margin-bottom: 5px;
        }
        .comments-stats .stat-label {
            font-size: 12px;
            color: #666;
        }
        .comments-list {
            max-height: 400px;
            overflow-y: auto;
            padding: 20px;
        }
        .comment-item {
            border: 1px solid #f0f0f0;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            background: white;
        }
        .comment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .product-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .product-image {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            object-fit: cover;
        }
        .product-name {
            font-weight: 500;
            margin-bottom: 5px;
        }
        .comment-rating {
            color: #ffa500;
            font-size: 14px;
        }
        .comment-time {
            font-size: 12px;
            color: #999;
        }
        .comment-content p {
            margin: 0;
            line-height: 1.5;
        }
        .comment-images {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        .comment-image {
            width: 60px;
            height: 60px;
            border-radius: 6px;
            object-fit: cover;
            cursor: pointer;
        }
        .no-comments {
            text-align: center;
            color: #999;
            padding: 40px 0;
        }
    `;
    document.head.appendChild(style);
}

// 添加用户分享模态框样式
function addUserSharesModalStyles() {
    if (document.getElementById('user-shares-modal-styles')) return;

    const style = document.createElement('style');
    style.id = 'user-shares-modal-styles';
    style.textContent = `
        .user-shares-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 15000;
            backdrop-filter: blur(5px);
        }
        .user-shares-modal .modal-content {
            background: white;
            border-radius: 20px;
            width: 90%;
            max-width: 700px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
        }
        .user-shares-modal .modal-header {
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .shares-stats {
            display: flex;
            gap: 20px;
            padding: 20px;
            background: #f8f9fa;
        }
        .shares-stats .stat-card {
            flex: 1;
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .shares-stats .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #4ecdc4;
            margin-bottom: 5px;
        }
        .shares-stats .stat-label {
            font-size: 12px;
            color: #666;
        }
        .shares-list {
            max-height: 400px;
            overflow-y: auto;
            padding: 20px;
        }
        .share-item {
            border: 1px solid #f0f0f0;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            background: white;
        }
        .share-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .product-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .product-image {
            width: 40px;
            height: 40px;
            object-fit: cover;
            border-radius: 6px;
            border: 1px solid #e9ecef;
            flex-shrink: 0;
        }
        .product-details {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        .product-name {
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }
        .share-type {
            color: #4ecdc4;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .share-time {
            font-size: 12px;
            color: #999;
        }
        .share-content p {
            margin: 0;
            line-height: 1.5;
            color: #666;
        }
        .no-shares {
            text-align: center;
            color: #999;
            padding: 40px 0;
        }
    `;
    document.head.appendChild(style);
}

// 添加优惠券模态框样式
function addCouponsModalStyles() {
    if (document.getElementById('coupons-modal-styles')) return;

    const style = document.createElement('style');
    style.id = 'coupons-modal-styles';
    style.textContent = `
        .coupons-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 15000;
            backdrop-filter: blur(5px);
        }
        .coupons-modal .modal-content {
            background: white;
            border-radius: 20px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
        }
        .coupons-modal .modal-header {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .coupons-stats {
            display: flex;
            gap: 20px;
            padding: 20px;
            background: #f8f9fa;
        }
        .coupons-stats .stat-card {
            flex: 1;
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .coupons-stats .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #ff6b6b;
            margin-bottom: 5px;
        }
        .coupons-stats .stat-label {
            font-size: 12px;
            color: #666;
        }
        .coupons-tabs {
            display: flex;
            padding: 0 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        .tab-btn {
            padding: 12px 24px;
            border: none;
            background: none;
            cursor: pointer;
            font-weight: 500;
            color: #666;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }
        .tab-btn.active {
            color: #ff6b6b;
            border-bottom-color: #ff6b6b;
        }
        .coupons-list {
            max-height: 400px;
            overflow-y: auto;
            padding: 20px;
        }
        .coupon-item {
            display: flex;
            align-items: center;
            padding: 20px;
            margin-bottom: 15px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .coupon-item.available {
            background: linear-gradient(135deg, #fff5f5 0%, #ffe8e8 100%);
            border-left: 4px solid #ff6b6b;
        }
        .coupon-item.expired {
            background: #f8f9fa;
            border-left: 4px solid #ccc;
            opacity: 0.6;
        }
        .coupon-left {
            text-align: center;
            padding: 0 20px;
            border-right: 2px dashed #ddd;
            margin-right: 20px;
        }
        .coupon-discount {
            font-size: 28px;
            font-weight: 700;
            color: #ff6b6b;
            line-height: 1;
        }
        .coupon-type {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .coupon-info {
            flex: 1;
        }
        .coupon-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        .coupon-condition {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        .coupon-expire {
            font-size: 12px;
            color: #999;
        }
        .coupon-action {
            padding-left: 20px;
        }
        .btn-use-coupon {
            padding: 8px 16px;
            background: #ff6b6b;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .btn-use-coupon:hover {
            background: #ff5252;
            transform: translateY(-2px);
        }
        .expired-label {
            padding: 8px 16px;
            background: #ccc;
            color: white;
            border-radius: 20px;
            font-size: 14px;
        }
        .no-coupons {
            text-align: center;
            color: #999;
            padding: 40px 0;
        }
    `;
    document.head.appendChild(style);
}
