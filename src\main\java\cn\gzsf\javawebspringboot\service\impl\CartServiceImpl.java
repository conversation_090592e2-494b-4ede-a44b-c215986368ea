package cn.gzsf.javawebspringboot.service.impl;

import cn.gzsf.javawebspringboot.dto.CartItemDTO;
import cn.gzsf.javawebspringboot.entity.ShoppingCart;
import cn.gzsf.javawebspringboot.mapper.CartMapper;
import cn.gzsf.javawebspringboot.service.CartService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 购物车服务实现类
 */
@Service
@Transactional
public class CartServiceImpl implements CartService {

    @Autowired
    private CartMapper cartMapper;

    @Override
    public boolean addToCart(ShoppingCart cartItem) {
        try {
            // 设置创建时间和更新时间
            long currentTime = System.currentTimeMillis();
            cartItem.setCreatedTime(currentTime);
            cartItem.setUpdatedTime(currentTime);
            
            // 检查购物车中是否已存在该商品
            ShoppingCart existingItem = cartMapper.getCartItem(cartItem.getUserPhone(), cartItem.getProductId());
            
            if (existingItem != null) {
                // 如果存在，更新数量
                existingItem.setQuantity(existingItem.getQuantity() + cartItem.getQuantity());
                existingItem.setUpdatedTime(currentTime);
                return cartMapper.updateCartItem(existingItem) > 0;
            } else {
                // 如果不存在，添加新商品
                return cartMapper.addToCart(cartItem) > 0;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public List<CartItemDTO> getCartByUserPhone(String userPhone) {
        try {
            return cartMapper.getCartByUserPhone(userPhone);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public boolean updateCartItem(ShoppingCart cartItem) {
        try {
            cartItem.setUpdatedTime(System.currentTimeMillis());
            return cartMapper.updateCartItem(cartItem) > 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean removeFromCart(String userPhone, Integer productId) {
        try {
            return cartMapper.removeFromCart(userPhone, productId) > 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean clearCart(String userPhone) {
        try {
            return cartMapper.clearCart(userPhone) > 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public int getCartItemCount(String userPhone) {
        try {
            return cartMapper.getCartItemCount(userPhone);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }
}
