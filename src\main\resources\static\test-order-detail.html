<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单详情API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input {
            width: 300px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>🔍 订单详情API测试工具</h1>
    <p><strong>测试时间：</strong><span id="currentTime"></span></p>

    <div class="test-section">
        <h2>1. 测试订单列表API</h2>
        <div class="input-group">
            <label for="userPhone">用户手机号：</label>
            <input type="text" id="userPhone" value="13220248009" placeholder="输入用户手机号">
        </div>
        <button class="test-button" onclick="testOrderList()">获取订单列表</button>
        <div id="orderListResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. 测试订单详情API</h2>
        <div class="input-group">
            <label for="orderNo">订单号：</label>
            <input type="text" id="orderNo" value="ORD20241201001" placeholder="输入订单号">
        </div>
        <button class="test-button" onclick="testOrderDetail()">获取订单详情</button>
        <div id="orderDetailResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. 数据库数据验证</h2>
        <button class="test-button" onclick="testDatabaseData()">验证数据库数据</button>
        <div id="databaseResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>4. 前端订单详情函数测试</h2>
        <button class="test-button" onclick="testFrontendFunction()">测试前端函数</button>
        <div id="frontendResult" class="result"></div>
    </div>

    <script>
        // 更新当前时间
        document.getElementById('currentTime').textContent = new Date().toLocaleString();

        // 测试订单列表API
        async function testOrderList() {
            const userPhone = document.getElementById('userPhone').value;
            const resultDiv = document.getElementById('orderListResult');
            
            try {
                resultDiv.textContent = '正在获取订单列表...';
                resultDiv.className = 'result';
                
                const response = await axios.get(`/api/order/list/${userPhone}`);
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ 订单列表API测试成功\n\n` +
                    `响应状态: ${response.status}\n` +
                    `成功标志: ${response.data.success}\n` +
                    `订单数量: ${response.data.data ? response.data.data.length : 0}\n\n` +
                    `完整响应:\n${JSON.stringify(response.data, null, 2)}`;
                
                // 如果有订单，自动填充第一个订单号
                if (response.data.success && response.data.data && response.data.data.length > 0) {
                    const firstOrder = response.data.data[0];
                    const orderNo = firstOrder.order_no || firstOrder.orderNo;
                    if (orderNo) {
                        document.getElementById('orderNo').value = orderNo;
                    }
                }
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 订单列表API测试失败\n\n` +
                    `错误信息: ${error.message}\n` +
                    `响应状态: ${error.response?.status}\n` +
                    `响应数据: ${JSON.stringify(error.response?.data, null, 2)}`;
            }
        }

        // 测试订单详情API
        async function testOrderDetail() {
            const orderNo = document.getElementById('orderNo').value;
            const resultDiv = document.getElementById('orderDetailResult');
            
            if (!orderNo) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 请输入订单号';
                return;
            }
            
            try {
                resultDiv.textContent = '正在获取订单详情...';
                resultDiv.className = 'result';
                
                const response = await axios.get(`/api/order/${orderNo}`);
                
                resultDiv.className = 'result success';
                const orderData = response.data.data;
                const itemsCount = orderData?.items ? orderData.items.length : 0;
                
                resultDiv.textContent = `✅ 订单详情API测试成功\n\n` +
                    `响应状态: ${response.status}\n` +
                    `成功标志: ${response.data.success}\n` +
                    `订单号: ${orderData?.order_no || orderData?.orderNo || '未知'}\n` +
                    `商品数量: ${itemsCount}\n` +
                    `订单金额: ${orderData?.total_amount || orderData?.totalAmount || '未知'}\n\n` +
                    `商品详情:\n${orderData?.items ? JSON.stringify(orderData.items, null, 2) : '无商品信息'}\n\n` +
                    `完整响应:\n${JSON.stringify(response.data, null, 2)}`;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 订单详情API测试失败\n\n` +
                    `错误信息: ${error.message}\n` +
                    `响应状态: ${error.response?.status}\n` +
                    `响应数据: ${JSON.stringify(error.response?.data, null, 2)}`;
            }
        }

        // 验证数据库数据
        async function testDatabaseData() {
            const resultDiv = document.getElementById('databaseResult');
            
            try {
                resultDiv.textContent = '正在验证数据库数据...';
                resultDiv.className = 'result';
                
                // 测试多个API来验证数据完整性
                const tests = [
                    { name: '用户订单列表', url: '/api/order/list/13220248009' },
                    { name: '订单详情1', url: '/api/order/ORD20241201001' },
                    { name: '订单详情2', url: '/api/order/ORD20241201002' },
                    { name: '产品列表', url: '/products/category/0' }
                ];
                
                let results = [];
                
                for (const test of tests) {
                    try {
                        const response = await axios.get(test.url);
                        results.push({
                            name: test.name,
                            success: response.data.success,
                            dataCount: response.data.data ? 
                                (Array.isArray(response.data.data) ? response.data.data.length : 1) : 0,
                            hasItems: response.data.data?.items ? response.data.data.items.length : 0
                        });
                    } catch (error) {
                        results.push({
                            name: test.name,
                            success: false,
                            error: error.message
                        });
                    }
                }
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ 数据库数据验证完成\n\n` +
                    results.map(r => 
                        `${r.name}: ${r.success ? '✅' : '❌'} ` +
                        `${r.success ? `数据量: ${r.dataCount}${r.hasItems ? `, 商品: ${r.hasItems}` : ''}` : `错误: ${r.error}`}`
                    ).join('\n') + '\n\n' +
                    `详细结果:\n${JSON.stringify(results, null, 2)}`;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 数据库验证失败: ${error.message}`;
            }
        }

        // 测试前端函数
        function testFrontendFunction() {
            const resultDiv = document.getElementById('frontendResult');
            const orderNo = document.getElementById('orderNo').value || 'ORD20241201001';
            
            resultDiv.className = 'result';
            resultDiv.textContent = `正在测试前端订单详情函数...\n订单号: ${orderNo}`;
            
            // 模拟前端函数调用
            if (typeof window.viewOrderDetail === 'function') {
                try {
                    window.viewOrderDetail(orderNo);
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 前端函数调用成功\n\n` +
                        `函数名: viewOrderDetail\n` +
                        `参数: ${orderNo}\n` +
                        `状态: 已调用，请查看页面上的模态框`;
                } catch (error) {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 前端函数调用失败: ${error.message}`;
                }
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 前端函数 viewOrderDetail 未定义\n\n` +
                    `请确保已加载 index_new.js 文件`;
            }
        }

        // 页面加载完成后自动测试
        window.addEventListener('load', function() {
            console.log('🔍 订单详情测试页面已加载');
            
            // 自动执行基础测试
            setTimeout(() => {
                testOrderList();
            }, 1000);
        });
    </script>
</body>
</html>
