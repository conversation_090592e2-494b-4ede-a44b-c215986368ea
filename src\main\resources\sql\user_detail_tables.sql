-- 用户详情表
CREATE TABLE `user_detail` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `phone` VARCHAR(20) NOT NULL UNIQUE,           -- 外键，关联user表的phone字段
    `avatar_url` VARCHAR(255),                     -- 用户头像URL
    `signature` TEXT,                              -- 个性签名
    `created_time` BIGINT NOT NULL,                -- 创建时间
    `updated_time` BIGINT NOT NULL,                -- 更新时间
    FOREIGN KEY (`phone`) REFERENCES `user`(`phone`) ON DELETE CASCADE
);

-- 用户好友表
CREATE TABLE `user_friend` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `user_phone` VARCHAR(20) NOT NULL,             -- 用户手机号
    `friend_phone` VARCHAR(20) NOT NULL,           -- 好友手机号
    `friend_name` VARCHAR(255),                    -- 好友备注名
    `status` TINYINT DEFAULT 1,                    -- 好友状态：1-正常，0-已删除
    `created_time` BIGINT NOT NULL,                -- 添加时间
    UNIQUE KEY `unique_friendship` (`user_phone`, `friend_phone`),
    FOREIGN KEY (`user_phone`) REFERENCES `user`(`phone`) ON DELETE CASCADE,
    FOREIGN KEY (`friend_phone`) REFERENCES `user`(`phone`) ON DELETE CASCADE
);

-- 收货地址表
CREATE TABLE `user_address` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `user_phone` VARCHAR(20) NOT NULL,             -- 用户手机号
    `receiver_name` VARCHAR(100) NOT NULL,         -- 收货人姓名
    `receiver_phone` VARCHAR(20) NOT NULL,         -- 收货人电话
    `province` VARCHAR(50) NOT NULL,               -- 省份
    `city` VARCHAR(50) NOT NULL,                   -- 城市
    `district` VARCHAR(50) NOT NULL,               -- 区县
    `detail_address` VARCHAR(255) NOT NULL,        -- 详细地址
    `postal_code` VARCHAR(10),                     -- 邮政编码
    `is_default` BOOLEAN DEFAULT FALSE,            -- 是否默认地址
    `created_time` BIGINT NOT NULL,                -- 创建时间
    `updated_time` BIGINT NOT NULL,                -- 更新时间
    FOREIGN KEY (`user_phone`) REFERENCES `user`(`phone`) ON DELETE CASCADE
);

-- 购物车表
CREATE TABLE `shopping_cart` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `user_phone` VARCHAR(20) NOT NULL,             -- 用户手机号
    `product_id` INT NOT NULL,                     -- 商品ID
    `quantity` INT NOT NULL DEFAULT 1,             -- 商品数量
    `created_time` BIGINT NOT NULL,                -- 添加时间
    `updated_time` BIGINT NOT NULL,                -- 更新时间
    UNIQUE KEY `unique_cart_item` (`user_phone`, `product_id`),
    FOREIGN KEY (`user_phone`) REFERENCES `user`(`phone`) ON DELETE CASCADE,
    FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE CASCADE
);

-- 订单表
CREATE TABLE `user_order` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `order_no` VARCHAR(50) NOT NULL UNIQUE,        -- 订单号
    `user_phone` VARCHAR(20) NOT NULL,             -- 用户手机号
    `total_amount` DECIMAL(10, 2) NOT NULL,        -- 订单总金额
    `status` TINYINT NOT NULL DEFAULT 1,           -- 订单状态：1-待付款，2-待发货，3-待收货，4-已完成，5-已取消
    `receiver_name` VARCHAR(100) NOT NULL,         -- 收货人姓名
    `receiver_phone` VARCHAR(20) NOT NULL,         -- 收货人电话
    `receiver_address` TEXT NOT NULL,              -- 收货地址
    `remark` TEXT,                                 -- 订单备注
    `created_time` BIGINT NOT NULL,                -- 创建时间
    `updated_time` BIGINT NOT NULL,                -- 更新时间
    FOREIGN KEY (`user_phone`) REFERENCES `user`(`phone`) ON DELETE CASCADE
);

-- 订单详情表
CREATE TABLE `order_detail` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `order_id` INT NOT NULL,                       -- 订单ID
    `product_id` INT NOT NULL,                     -- 商品ID
    `product_name` VARCHAR(255) NOT NULL,          -- 商品名称（冗余存储，防止商品信息变更）
    `product_price` DECIMAL(10, 2) NOT NULL,       -- 商品价格（下单时的价格）
    `quantity` INT NOT NULL,                       -- 购买数量
    `subtotal` DECIMAL(10, 2) NOT NULL,            -- 小计金额
    FOREIGN KEY (`order_id`) REFERENCES `user_order`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`product_id`) REFERENCES `products`(`id`)
);

-- 插入一些测试数据
INSERT INTO `user_detail` (`phone`, `avatar_url`, `signature`, `created_time`, `updated_time`) VALUES
('15120248009', '/images/avatar/admin.jpg', '系统管理员，欢迎使用天镜美妆！', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('18823912577', '/images/avatar/wen.jpg', '爱美是女人的天性~', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('18796247689', '/images/avatar/kkkk.jpg', '每天都要美美哒！', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);

-- 插入一些测试地址
INSERT INTO `user_address` (`user_phone`, `receiver_name`, `receiver_phone`, `province`, `city`, `district`, `detail_address`, `is_default`, `created_time`, `updated_time`) VALUES
('15120248009', '管理员', '15120248009', '北京市', '北京市', '朝阳区', '三里屯街道1号', TRUE, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('18823912577', '小雯', '18823912577', '广东省', '深圳市', '南山区', '科技园南区2号楼', TRUE, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('18796247689', 'KKKK', '18796247689', '上海市', '上海市', '浦东新区', '陆家嘴金融中心3号', TRUE, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);
