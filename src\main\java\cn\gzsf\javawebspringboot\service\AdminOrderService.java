package cn.gzsf.javawebspringboot.service;

import cn.gzsf.javawebspringboot.entity.UserOrder;

import java.util.Map;

/**
 * 管理员订单服务接口
 */
public interface AdminOrderService {
    
    /**
     * 获取订单列表（分页）
     * @param page 页码
     * @param size 每页大小
     * @param status 订单状态筛选（可选）
     * @return 分页结果
     */
    Map<String, Object> getOrderList(int page, int size, Integer status);
    
    /**
     * 获取订单统计信息
     * @return 统计信息
     */
    Map<String, Object> getOrderStats();
    
    /**
     * 发货
     * @param orderNo 订单号
     * @return 是否成功
     */
    boolean shipOrder(String orderNo);
    
    /**
     * 管理员取消订单
     * @param orderNo 订单号
     * @return 是否成功
     */
    boolean cancelOrder(String orderNo);
    
    /**
     * 获取订单详情
     * @param orderNo 订单号
     * @return 订单详情
     */
    UserOrder getOrderDetail(String orderNo);
}
