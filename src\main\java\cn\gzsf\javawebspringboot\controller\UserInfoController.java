package cn.gzsf.javawebspringboot.controller;

import cn.gzsf.javawebspringboot.entity.User;
import cn.gzsf.javawebspringboot.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户信息API控制器
 */
@RestController
@RequestMapping("/api/user")
public class UserInfoController {

    @Autowired
    private UserService userService;

    /**
     * 根据用户标识获取用户信息
     * @param identifier 用户标识（可以是手机号或用户ID）
     * @return 用户信息
     */
    @GetMapping("/info/{identifier}")
    public Map<String, Object> getUserInfo(@PathVariable String identifier) {
        Map<String, Object> result = new HashMap<>();
        try {
            User user = null;
            
            // 尝试按手机号查找
            if (identifier.matches("^1[3-9]\\d{9}$")) {
                user = userService.findByPhone(identifier);
            } else {
                // 尝试按用户ID查找
                user = userService.findByUserId(identifier);
            }
            
            if (user != null) {
                // 构建返回的用户信息（不包含敏感信息）
                Map<String, Object> userInfo = new HashMap<>();
                userInfo.put("id", user.getId());
                userInfo.put("userId", user.getUserId());
                userInfo.put("username", user.getUsername());
                userInfo.put("phone", user.getPhone());
                userInfo.put("avatar", user.getAvatar());
                userInfo.put("signature", user.getSignature());
                userInfo.put("createdTime", user.getCreatedTime());
                
                result.put("success", true);
                result.put("data", userInfo);
            } else {
                result.put("success", false);
                result.put("message", "用户不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取用户信息失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 更新用户信息
     * @param userInfo 用户信息
     * @return 更新结果
     */
    @PostMapping("/update")
    public Map<String, Object> updateUserInfo(@RequestBody Map<String, Object> userInfo) {
        Map<String, Object> result = new HashMap<>();
        try {
            String phone = (String) userInfo.get("phone");
            if (phone == null || phone.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "用户手机号不能为空");
                return result;
            }

            User user = userService.findByPhone(phone);
            if (user == null) {
                result.put("success", false);
                result.put("message", "用户不存在");
                return result;
            }

            // 更新允许修改的字段
            if (userInfo.containsKey("username")) {
                user.setUsername((String) userInfo.get("username"));
            }
            if (userInfo.containsKey("avatar")) {
                user.setAvatar((String) userInfo.get("avatar"));
            }
            if (userInfo.containsKey("signature")) {
                user.setSignature((String) userInfo.get("signature"));
            }

            boolean success = userService.updateUser(user);
            if (success) {
                result.put("success", true);
                result.put("message", "更新成功");
            } else {
                result.put("success", false);
                result.put("message", "更新失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新用户信息失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 检查用户登录状态
     * @param identifier 用户标识
     * @return 登录状态
     */
    @GetMapping("/check-login/{identifier}")
    public Map<String, Object> checkLoginStatus(@PathVariable String identifier) {
        Map<String, Object> result = new HashMap<>();
        try {
            User user = null;
            
            // 尝试按手机号查找
            if (identifier.matches("^1[3-9]\\d{9}$")) {
                user = userService.findByPhone(identifier);
            } else {
                // 尝试按用户ID查找
                user = userService.findByUserId(identifier);
            }
            
            result.put("success", true);
            result.put("isLoggedIn", user != null);
            if (user != null) {
                result.put("userId", user.getUserId());
                result.put("username", user.getUsername());
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "检查登录状态失败：" + e.getMessage());
        }
        return result;
    }
}
