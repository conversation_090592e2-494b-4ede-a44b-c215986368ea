package cn.gzsf.javawebspringboot.entity;

import javax.persistence.*;

/**
 * 购物车实体类
 */
@Entity
@Table(name = "shopping_cart")
public class ShoppingCart {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    
    @Column(name = "user_phone", nullable = false, length = 20)
    private String userPhone;
    
    @Column(name = "product_id", nullable = false)
    private Integer productId;
    
    @Column(name = "quantity", nullable = false)
    private Integer quantity = 1;
    
    @Column(name = "created_time", nullable = false)
    private Long createdTime;
    
    @Column(name = "updated_time", nullable = false)
    private Long updatedTime;
    
    // 构造函数
    public ShoppingCart() {}
    
    public ShoppingCart(String userPhone, Integer productId, Integer quantity) {
        this.userPhone = userPhone;
        this.productId = productId;
        this.quantity = quantity;
        this.createdTime = System.currentTimeMillis();
        this.updatedTime = System.currentTimeMillis();
    }
    
    // Getter和Setter方法
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getUserPhone() {
        return userPhone;
    }
    
    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }
    
    public Integer getProductId() {
        return productId;
    }
    
    public void setProductId(Integer productId) {
        this.productId = productId;
    }
    
    public Integer getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }
    
    public Long getCreatedTime() {
        return createdTime;
    }
    
    public void setCreatedTime(Long createdTime) {
        this.createdTime = createdTime;
    }
    
    public Long getUpdatedTime() {
        return updatedTime;
    }
    
    public void setUpdatedTime(Long updatedTime) {
        this.updatedTime = updatedTime;
    }
}
