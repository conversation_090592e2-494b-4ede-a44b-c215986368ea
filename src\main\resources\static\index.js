// 搜索功能类
class SearchManager {
    constructor() {
        // 初始化元素
        this.searchInput = document.getElementById('searchInput');
        this.searchBtn = document.getElementById('searchBtn');
        this.suggestions = document.getElementById('suggestions');
        this.productGrid = document.querySelector('.product-grid');
        this.categoryFilter = document.querySelector('.category-filter');
        this.dynamicProducts = document.querySelector('.dynamic-products');

        console.log('🔍 SearchManager 初始化完成');
        console.log('📍 动态产品区域:', this.dynamicProducts);
        console.log('📍 产品网格区域:', this.productGrid);

        // 当前分类
        this.currentCategory = 'all';

        // 初始化事件
        this.initEvents();

        // 加载分类数据
        this.loadCategories();
    }

    // 初始化事件监听
    initEvents() {
        // 输入实时搜索
        this.searchInput.addEventListener('input', (e) => {
            const keyword = e.target.value.trim();
            if (keyword) {
                this.showSuggestions(keyword);
            } else {
                this.hideSuggestions();
                this.loadAllProducts();
            }
        });

        // 搜索按钮点击
        this.searchBtn.addEventListener('click', () => this.executeSearch());

        // 回车搜索
        this.searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.executeSearch();
        });

        // 建议项点击
        this.suggestions.addEventListener('click', (e) => {
            if (e.target.classList.contains('suggestion-item')) {
                this.fillInput(e.target.textContent);
                this.executeSearch();
            }
        });

        // 分类筛选点击事件
        this.categoryFilter.addEventListener('click', (e) => {
            if (e.target.closest('.category-item')) {
                this.handleCategoryClick(e.target.closest('.category-item'));
            }
        });

        // 页面加载时加载所有产品
        window.addEventListener('load', () => this.loadAllProducts());
    }

    // 加载分类数据
    loadCategories() {
        axios.get('/admin/category/all')
            .then(response => {
                this.renderCategories(response.data);
            })
            .catch(error => {
                console.error('加载分类失败:', error);
                // 如果分类加载失败，使用默认分类
                this.useDefaultCategories();
            });
    }

    // 渲染分类
    renderCategories(categories) {
        const categoryList = this.categoryFilter.querySelector('.category-list');
        categoryList.innerHTML = `
            <li class="category-item active" data-category="all">
                <i class="fas fa-star"></i>
                <span>全部</span>
            </li>
        `;

        categories.forEach(category => {
            const categoryItem = document.createElement('li');
            categoryItem.className = 'category-item';
            categoryItem.setAttribute('data-category', category.name);
            categoryItem.innerHTML = `
                <i class="fas fa-spa"></i>
                <span>${category.name}</span>
            `;
            categoryList.appendChild(categoryItem);
        });
    }

    // 使用默认分类（当后端分类接口不可用时）
    useDefaultCategories() {
        const defaultCategories = [
            { name: '护肤品', icon: 'fas fa-spa' },
            { name: '彩妆', icon: 'fas fa-paint-brush' },
            { name: '香水', icon: 'fas fa-wine-bottle' },
            { name: '工具', icon: 'fas fa-toolbox' }
        ];
        this.renderCategories(defaultCategories);
    }

    // 处理分类点击
    handleCategoryClick(categoryItem) {
        // 移除所有active类
        document.querySelectorAll('.category-item').forEach(item => {
            item.classList.remove('active');
        });

        // 添加active类到当前点击的分类
        categoryItem.classList.add('active');

        // 获取分类名称
        this.currentCategory = categoryItem.getAttribute('data-category');

        // 根据分类加载产品
        this.loadProductsByCategory(this.currentCategory);
    }

    // 根据分类加载产品
    loadProductsByCategory(category) {
        if (category === 'all') {
            this.loadAllProducts();
        } else {
            // 这里可以扩展为按分类搜索的API
            axios.get('/products/search', { params: { keyword: category } })
                .then(response => {
                    this.renderProducts(response.data);
                    // 分类筛选后也滚动到产品区域
                    this.scrollToProducts();
                })
                .catch(error => {
                    console.error('按分类加载产品失败:', error);
                    this.loadAllProducts(); // 失败时加载所有产品
                });
        }
    }

    // 加载所有产品
    loadAllProducts() {
        axios.get('/products')
            .then(response => {
                this.renderProducts(response.data);
            })
            .catch(error => {
                console.error('加载产品失败:', error);
                this.showErrorMessage('加载产品失败，请刷新页面重试');
            });
    }

    // 渲染产品卡片
    renderProducts(products) {
        // 优先使用动态产品区域，如果不存在则使用产品网格
        const targetContainer = this.dynamicProducts || this.productGrid;

        console.log('🎨 开始渲染产品到容器:', targetContainer);
        console.log('📦 产品数量:', products.length);

        // 清空现有产品
        if (targetContainer) {
            targetContainer.innerHTML = '';
        }

        if (products.length === 0) {
            this.showNoProductsMessage();
            return;
        }

        products.forEach(product => {
            const productCard = document.createElement('article');
            productCard.classList.add('product-card');

            // 处理产品图片URL，优先使用主图
            let imageUrl = '/images/default-product.svg';
            if (product.primary_image_url) {
                imageUrl = product.primary_image_url;
            } else if (product.imageUrl) {
                imageUrl = product.imageUrl;
            } else if (product.image_url) {
                imageUrl = product.image_url;
            }

            productCard.innerHTML = `
                <div class="card-inner">
                    <div class="card-front" style="background-image: url('${imageUrl}')">
                        <div class="product-badge">${product.isNew || product.new ? '新品' : ''}</div>
                    </div>
                    <div class="card-back">
                        <h3>${product.name}</h3>
                        <p>${product.description || '暂无描述'}</p>
                        <div class="price">¥${product.price}</div>
                        <button class="btn-detail" data-product-id="${product.id}">查看详情</button>
                        <button class="btn-cart" data-product-id="${product.id}" data-product="${product.name}" data-price="${product.price}" data-image="${imageUrl}">
                            <i class="fas fa-cart-plus"></i> 加入购物车
                        </button>
                    </div>
                </div>
            `;
            targetContainer.appendChild(productCard);
        });

        console.log('✅ 产品渲染完成');
    }

    // 显示无产品消息
    showNoProductsMessage() {
        const targetContainer = this.dynamicProducts || this.productGrid;
        if (targetContainer) {
            targetContainer.innerHTML = `
                <div class="no-products-message">
                    <i class="fas fa-search" style="font-size: 3rem; color: #ccc; margin-bottom: 1rem;"></i>
                    <p>暂无相关产品</p>
                </div>
            `;
        }
    }

    // 显示错误消息
    showErrorMessage(message) {
        this.productGrid.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-triangle" style="font-size: 3rem; color: #ff6b6b; margin-bottom: 1rem;"></i>
                <p>${message}</p>
            </div>
        `;
    }

    // 显示搜索建议
    showSuggestions(keyword) {
        axios.get('/products/search', { params: { keyword } })
            .then(response => {
                const results = response.data;
                if (results.length === 0) {
                    this.suggestions.innerHTML = '<div class="no-results">无相关产品</div>';
                } else {
                    this.suggestions.innerHTML = results.map(product =>
                        `<div class="suggestion-item">${product.name}</div>`
                    ).join('');
                }
                this.suggestions.style.display = 'block';
            })
            .catch(error => console.error('搜索建议失败:', error));
    }

    // 隐藏搜索建议
    hideSuggestions() {
        this.suggestions.style.display = 'none';
    }

    // 填充搜索框
    fillInput(text) {
        this.searchInput.value = text;
        this.searchInput.focus(); // 保持焦点
    }

    // 执行搜索
    executeSearch() {
        const keyword = this.searchInput.value.trim();
        this.hideSuggestions(); // 搜索时隐藏建议列表

        if (!keyword) {
            this.loadAllProducts();
            return;
        }

        axios.get('/products/search', { params: { keyword } })
            .then(response => {
                const results = response.data;
                if (results.length === 0) {
                    this.showNoResultsToast(); // 显示无结果提示
                    return;
                }
                this.renderProducts(results);

                // 搜索完成后滚动到产品区域
                this.scrollToProducts();
            })
            .catch(error => console.error('搜索产品失败:', error));
    }

    // 滚动到产品区域
    scrollToProducts() {
        console.log('🎯 开始滚动到产品区域...');

        // 查找产品区域元素，优先使用动态产品区域
        const productSection = this.dynamicProducts ||
                              document.querySelector('.dynamic-products') ||
                              this.productGrid ||
                              document.querySelector('.product-grid') ||
                              document.querySelector('#products') ||
                              document.querySelector('#products-section');

        console.log('🔍 找到的产品区域元素:', productSection);

        if (productSection) {
            // 计算滚动位置，留出一些顶部空间
            const rect = productSection.getBoundingClientRect();
            const scrollTop = window.pageYOffset + rect.top - 100; // 留出100px的顶部空间

            // 平滑滚动到产品区域
            window.scrollTo({
                top: scrollTop,
                behavior: 'smooth'
            });

            // 添加高亮效果
            productSection.style.transition = 'all 0.3s ease';
            productSection.style.boxShadow = '0 0 20px rgba(255, 105, 180, 0.3)';
            productSection.style.borderRadius = '10px';

            console.log('✨ 添加高亮效果');

            // 2秒后移除高亮效果
            setTimeout(() => {
                productSection.style.boxShadow = '';
                productSection.style.borderRadius = '';
                console.log('🔄 移除高亮效果');
            }, 2000);
        } else {
            console.warn('⚠️ 未找到产品区域元素');
        }
    }

    // 显示无结果提示（临时Toast）
    showNoResultsToast() {
        const toast = document.createElement('div');
        toast.className = 'search-toast';
        toast.textContent = '没有找到相关产品，请尝试其他关键词';
        toast.style.position = 'fixed';
        toast.style.top = '60px';
        toast.style.right = '20px';
        toast.style.padding = '10px 20px';
        toast.style.borderRadius = '25px';
        toast.style.backgroundColor = 'rgba(255, 77, 77, 0.9)';
        toast.style.color = 'white';
        toast.style.boxShadow = '0 3px 10px rgba(0,0,0,0.2)';
        document.body.appendChild(toast);

        // 3秒后消失
        setTimeout(() => toast.remove(), 3000);
    }
}

// 购物车功能类
class ShoppingCart {
    constructor() {
        // 初始化购物车数组，用于存储商品信息
        this.cart = [];
        // 当前用户手机号（模拟登录状态）
        this.currentUserPhone = '15120248009'; // 这里应该从登录状态获取
        // 初始化事件监听
        this.initEvents();
        // 从后端加载购物车数据
        this.loadCartFromServer();

        // 创建提示框容器
        this.createAlertContainer();
    }

    // 创建全局提示框
    createAlertContainer() {
        this.alertContainer = document.createElement('div');
        this.alertContainer.className = 'cart-alert';
        document.body.appendChild(this.alertContainer);
    }

    initEvents() {
        // 为所有“加入购物车”按钮添加点击事件监听器
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('btn-cart')) {
                // 获取商品名称
                const product = e.target.dataset.product;
                // 获取商品价格并转换为整数
                const price = parseInt(e.target.dataset.price);
                // 验证商品名称和价格是否有效
                if (product && product.trim() !== '' && !isNaN(price) && price > 0) {
                    try {
                        this.addItem(product, price);
                        this.showAlert('success', '✔️ 添加成功'); // 成功提示
                    } catch (error) {
                        console.error('添加购物车失败:', error);
                        this.showAlert('error', '❌ 添加失败'); // 失败提示
                    }
                } else {
                    this.showAlert('error', '⚠️ 商品信息不完整'); // 验证失败提示
                }
            }
        });

        // 为购物车图标添加点击事件监听器，用于显示或隐藏购物车模态框
        document.querySelector('.cart-counter').addEventListener('click', () => {
            this.toggleCart();
        });

        // 为关闭购物车模态框的按钮添加点击事件监听器
        document.querySelector('.close-cart').addEventListener('click', () => {
            this.toggleCart(false);
        });

        // 使用事件委托，为购物车中的数量调整和删除按钮添加点击事件监听器
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('cart-item-increase')) {
                // 增加商品数量
                this.changeQuantity(e.target.closest('li'), 1);
            } else if (e.target.classList.contains('cart-item-decrease')) {
                // 减少商品数量
                this.changeQuantity(e.target.closest('li'), -1);
            } else if (e.target.classList.contains('cart-item-remove')) {
                // 弹出确认框，确认是否删除商品
                if (confirm('确定要删除该商品吗？')) {
                    // 删除商品
                    this.removeItem(e.target.closest('li'));
                }
            }
        });
    }

    // 显示提示框方法
    showAlert(type, message) {
        const alert = document.createElement('div');
        alert.className = `cart-alert-item ${type}`;
        alert.textContent = message;

        // 添加到提示容器
        this.alertContainer.appendChild(alert);

        // 3秒后自动消失
        setTimeout(() => {
            alert.remove();
        }, 3000);
    }

    // 添加商品到购物车的方法
    addItem(name, price) {
        // 加强参数验证
        if (typeof name !== 'string' || name.trim() === '') {
            throw new Error('无效的商品名称');
        }
        if (typeof price !== 'number' || price <= 0) {
            throw new Error('无效的商品价格');
        }
        // 查找购物车中是否已存在该商品
        const existingItem = this.cart.find(item => item.name === name);
        if (existingItem) {
            // 若存在，增加该商品的数量
            existingItem.quantity++;
        } else {
            // 若不存在，将商品添加到购物车
            this.cart.push({
                name: name.trim(), // 清理空格
                price: Math.max(0, price), // 确保非负数
                quantity: 1
            });
        }

        try {
            this.updateCartDisplay();
            this.saveCartToLocalStorage();
        } catch (error) {
            console.error('保存购物车失败:', error);
            this.showAlert('error', '⚠️ 保存失败，请重试');
            throw error; // 重新抛出错误
        }
    }

    // 更新购物车显示
    updateCartDisplay() {
        const cartItems = document.querySelector('.cart-items');
        const cartCount = document.querySelector('.cart-count');
        const modalCartCount = document.querySelector('.modal-cart-count');
        const emptyCart = document.querySelector('.empty-cart');

        // 更新购物车图标数量
        const totalItems = this.cart.reduce((sum, item) => sum + item.quantity, 0);
        cartCount.textContent = totalItems;
        if (modalCartCount) modalCartCount.textContent = totalItems;

        // 如果购物车为空，显示空购物车提示
        if (this.cart.length === 0) {
            cartItems.style.display = 'none';
            if (emptyCart) emptyCart.style.display = 'block';
            this.updateCartSummary();
            return;
        }

        // 隐藏空购物车提示，显示商品列表
        cartItems.style.display = 'block';
        if (emptyCart) emptyCart.style.display = 'none';

        cartItems.innerHTML = '';

        this.cart.forEach((item, index) => {
            const li = document.createElement('li');
            li.className = 'cart-item';
            li.innerHTML = `
                <div class="cart-item-select">
                    <input type="checkbox" class="cart-item-checkbox" checked>
                </div>
                <div class="cart-item-image">
                    <img src="${item.imageUrl || '/images/default-product.svg'}" alt="${item.name}">
                </div>
                <div class="cart-item-info">
                    <div class="item-name">${item.name}</div>
                    <div class="item-price">¥${item.price}</div>
                </div>
                <div class="cart-item-quantity">
                    <button class="cart-item-decrease" ${item.quantity <= 1 ? 'disabled' : ''}>-</button>
                    <span class="quantity-display">${item.quantity}</span>
                    <button class="cart-item-increase">+</button>
                </div>
                <div class="cart-item-total">
                    ¥${(item.price * item.quantity).toFixed(2)}
                </div>
                <div class="cart-item-actions">
                    <button class="cart-item-remove" title="删除商品">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
            cartItems.appendChild(li);
        });

        // 添加全选复选框事件监听
        const selectAllCheckbox = document.getElementById('selectAllCart');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', () => this.toggleSelectAll());
        }

        // 添加商品复选框事件监听
        document.querySelectorAll('.cart-item-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', () => this.updateCartSummary());
        });

        // 更新购物车摘要
        this.updateCartSummary();
    }

    // 改变商品数量
    changeQuantity(itemElement, amount) {
        const name = itemElement.querySelector('.item-name').textContent;
        const existingItem = this.cart.find(item => item.name === name);

        if (existingItem) {
            const newQuantity = existingItem.quantity + amount;
            if (newQuantity <= 0) {
                // 如果数量为0或负数，删除商品
                if (confirm('确定要删除该商品吗？')) {
                    this.removeItem(itemElement);
                }
                return;
            }

            existingItem.quantity = newQuantity;
            this.updateCartDisplay();
            this.saveCartToLocalStorage();
            this.saveCartToServer();

            // 显示数量变更提示
            this.showAlert('info', `${existingItem.name} 数量已更新为 ${newQuantity}`);
        }
    }

    // 删除商品
    removeItem(itemElement) {
        const name = itemElement.querySelector('.item-name').textContent;
        const removedItem = this.cart.find(item => item.name === name);

        this.cart = this.cart.filter(item => item.name !== name);
        this.updateCartDisplay();
        this.saveCartToLocalStorage();
        this.saveCartToServer();

        if (removedItem) {
            this.showAlert('success', `已删除 ${removedItem.name}`);
        }
    }

    // 显示或隐藏购物车模态框
    toggleCart(show) {
        const cartModal = document.querySelector('.cart-modal');
        if (show === undefined) {
            cartModal.style.display = cartModal.style.display === 'block' ? 'none' : 'block';
        } else {
            cartModal.style.display = show ? 'block' : 'none';
        }
    }

    // 从本地存储加载购物车数据
    loadCartFromLocalStorage() {
        const cartData = localStorage.getItem('shoppingCart');
        if (cartData) {
            this.cart = JSON.parse(cartData);
            this.updateCartDisplay();
        }
    }

    // 保存购物车数据到本地存储
    saveCartToLocalStorage() {
        localStorage.setItem('shoppingCart', JSON.stringify(this.cart));
    }

    // ==================== 增强购物车功能 ====================

    // 从服务器加载购物车数据
    loadCartFromServer() {
        if (!this.currentUserPhone) {
            this.loadCartFromLocalStorage();
            return;
        }

        axios.get(`/api/cart/${this.currentUserPhone}`)
            .then(response => {
                if (response.data.success) {
                    this.cart = response.data.data || [];
                    this.updateCartDisplay();
                } else {
                    console.log('服务器购物车为空，使用本地存储');
                    this.loadCartFromLocalStorage();
                }
            })
            .catch(error => {
                console.error('加载购物车失败，使用本地存储:', error);
                this.loadCartFromLocalStorage();
            });
    }

    // 保存购物车到服务器
    saveCartToServer() {
        if (!this.currentUserPhone) {
            this.saveCartToLocalStorage();
            return;
        }

        const cartData = {
            userPhone: this.currentUserPhone,
            items: this.cart
        };

        axios.post('/api/cart/save', cartData)
            .then(response => {
                if (response.data.success) {
                    console.log('购物车已同步到服务器');
                } else {
                    console.error('同步购物车失败:', response.data.message);
                }
            })
            .catch(error => {
                console.error('同步购物车失败:', error);
            });
    }

    // 全选/取消全选
    toggleSelectAll() {
        const selectAllCheckbox = document.getElementById('selectAllCart');
        const itemCheckboxes = document.querySelectorAll('.cart-item-checkbox');

        itemCheckboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
        });

        this.updateCartSummary();
    }

    // 更新购物车摘要信息
    updateCartSummary() {
        const selectedItems = this.getSelectedItems();
        const selectedCount = selectedItems.length;
        const totalPrice = selectedItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);

        document.getElementById('selectedItemsCount').textContent = selectedCount;
        document.getElementById('totalPrice').textContent = totalPrice.toFixed(2);

        // 更新结算按钮状态
        const checkoutBtn = document.querySelector('.btn-checkout');
        checkoutBtn.disabled = selectedCount === 0;
        checkoutBtn.textContent = selectedCount > 0 ? `立即结算(${selectedCount})` : '立即结算';
    }

    // 获取选中的商品
    getSelectedItems() {
        const selectedItems = [];
        const itemCheckboxes = document.querySelectorAll('.cart-item-checkbox:checked');

        itemCheckboxes.forEach(checkbox => {
            const itemElement = checkbox.closest('li');
            const name = itemElement.querySelector('.item-name').textContent;
            const item = this.cart.find(cartItem => cartItem.name === name);
            if (item) {
                selectedItems.push(item);
            }
        });

        return selectedItems;
    }

    // 删除选中的商品
    clearSelectedItems() {
        const selectedItems = this.getSelectedItems();
        if (selectedItems.length === 0) {
            this.showAlert('warning', '请先选择要删除的商品');
            return;
        }

        if (confirm(`确定要删除选中的 ${selectedItems.length} 件商品吗？`)) {
            selectedItems.forEach(selectedItem => {
                this.cart = this.cart.filter(item => item.name !== selectedItem.name);
            });

            this.updateCartDisplay();
            this.saveCartToLocalStorage();
            this.saveCartToServer();
            this.showAlert('success', `已删除 ${selectedItems.length} 件商品`);
        }
    }

    // 清空购物车
    clearAllItems() {
        if (this.cart.length === 0) {
            this.showAlert('info', '购物车已经是空的');
            return;
        }

        if (confirm('确定要清空购物车吗？此操作不可恢复！')) {
            this.cart = [];
            this.updateCartDisplay();
            this.saveCartToLocalStorage();
            this.saveCartToServer();
            this.showAlert('success', '购物车已清空');
        }
    }

    // 结算
    checkout() {
        const selectedItems = this.getSelectedItems();
        if (selectedItems.length === 0) {
            this.showAlert('warning', '请先选择要结算的商品');
            return;
        }

        // 显示订单确认模态框
        this.showOrderConfirmation(selectedItems);
    }

    // 显示订单确认模态框
    showOrderConfirmation(selectedItems) {
        const modal = document.querySelector('.order-confirmation-modal');
        modal.style.display = 'flex';

        // 加载收货地址
        this.loadUserAddresses();

        // 渲染订单商品列表
        this.renderOrderItems(selectedItems);

        // 计算费用
        this.calculateOrderTotal(selectedItems);
    }

    // 加载用户收货地址
    loadUserAddresses() {
        if (!this.currentUserPhone) {
            document.getElementById('orderAddressList').innerHTML = `
                <div class="no-address">
                    <p>请先登录</p>
                    <button class="btn-login" onclick="window.location.href='land.html'">去登录</button>
                </div>
            `;
            return;
        }

        axios.get(`/addresses/${this.currentUserPhone}`)
            .then(response => {
                if (response.data.success && response.data.data.length > 0) {
                    this.renderAddresses(response.data.data);
                } else {
                    document.getElementById('orderAddressList').innerHTML = `
                        <div class="no-address">
                            <p>暂无收货地址，请先添加地址</p>
                            <button class="btn-add-address" onclick="window.userCenter?.manageAddresses()">
                                添加收货地址
                            </button>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('加载收货地址失败:', error);
                document.getElementById('orderAddressList').innerHTML = `
                    <div class="no-address">
                        <p>加载地址失败，请重试</p>
                    </div>
                `;
            });
    }

    // 渲染收货地址列表
    renderAddresses(addresses) {
        const addressList = document.getElementById('orderAddressList');
        addressList.innerHTML = addresses.map((address, index) => `
            <div class="address-item ${index === 0 ? 'selected' : ''}" data-address-id="${address.id}">
                <div class="receiver-info">
                    <span class="receiver-name">${address.receiverName}</span>
                    <span class="receiver-phone">${address.receiverPhone}</span>
                    ${address.isDefault ? '<span class="default-tag">默认</span>' : ''}
                </div>
                <div class="address-detail">${address.province} ${address.city} ${address.district} ${address.detailAddress}</div>
            </div>
        `).join('');

        // 添加地址选择事件
        addressList.addEventListener('click', (e) => {
            const addressItem = e.target.closest('.address-item');
            if (addressItem) {
                document.querySelectorAll('.address-item').forEach(item => item.classList.remove('selected'));
                addressItem.classList.add('selected');
            }
        });
    }

    // 渲染订单商品列表
    renderOrderItems(selectedItems) {
        const orderItemsList = document.getElementById('orderItemsList');
        orderItemsList.innerHTML = selectedItems.map(item => `
            <div class="order-item">
                <div class="order-item-image">
                    <img src="${item.imageUrl || '/images/default-product.svg'}" alt="${item.name}">
                </div>
                <div class="order-item-info">
                    <div class="order-item-name">${item.name}</div>
                    <div class="order-item-price">¥${item.price}</div>
                </div>
                <div class="order-item-quantity">x${item.quantity}</div>
            </div>
        `).join('');
    }

    // 计算订单总价
    calculateOrderTotal(selectedItems) {
        const subtotal = selectedItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const shipping = subtotal >= 99 ? 0 : 10; // 满99免运费
        const total = subtotal + shipping;

        document.getElementById('orderSubtotal').textContent = subtotal.toFixed(2);
        document.getElementById('orderShipping').textContent = shipping.toFixed(2);
        document.getElementById('orderTotal').textContent = total.toFixed(2);
    }

    // 关闭订单确认模态框
    closeOrderConfirmation() {
        document.querySelector('.order-confirmation-modal').style.display = 'none';
    }

    // 提交订单
    submitOrder() {
        const selectedAddress = document.querySelector('.address-item.selected');
        if (!selectedAddress) {
            this.showAlert('warning', '请选择收货地址');
            return;
        }

        const selectedItems = this.getSelectedItems();
        const orderData = {
            userPhone: this.currentUserPhone,
            addressId: selectedAddress.dataset.addressId,
            items: selectedItems,
            remark: document.getElementById('orderRemark').value,
            subtotal: parseFloat(document.getElementById('orderSubtotal').textContent),
            shipping: parseFloat(document.getElementById('orderShipping').textContent),
            total: parseFloat(document.getElementById('orderTotal').textContent)
        };

        axios.post('/api/order/create-enhanced', orderData)
            .then(response => {
                if (response.data.success) {
                    // 从购物车中移除已下单的商品
                    selectedItems.forEach(selectedItem => {
                        this.cart = this.cart.filter(item => item.name !== selectedItem.name);
                    });

                    this.updateCartDisplay();
                    this.saveCartToLocalStorage();
                    this.saveCartToServer();

                    // 关闭订单确认模态框
                    this.closeOrderConfirmation();

                    // 显示支付成功模态框
                    this.showPaymentSuccess(response.data.orderNo);
                } else {
                    this.showAlert('error', response.data.message || '下单失败');
                }
            })
            .catch(error => {
                console.error('下单失败:', error);
                this.showAlert('error', '下单失败，请重试');
            });
    }

    // 显示支付成功模态框
    showPaymentSuccess(orderNo) {
        const modal = document.querySelector('.payment-success-modal');
        document.getElementById('successOrderNo').textContent = orderNo;
        modal.style.display = 'flex';
    }

    // 关闭支付成功模态框
    closePaymentSuccess() {
        document.querySelector('.payment-success-modal').style.display = 'none';
    }
}

// 初始化搜索和购物车功能
document.addEventListener('DOMContentLoaded', () => {
    window.searchManager = new SearchManager();
    window.shoppingCart = new ShoppingCart();
});

// 全局函数，供HTML调用
window.addToCart = function(productId, productName, price) {
    if (window.shoppingCart) {
        window.shoppingCart.addItem(productName, price);
    }
};

window.toggleCart = function() {
    if (window.shoppingCart) {
        window.shoppingCart.toggleCart();
    }
};