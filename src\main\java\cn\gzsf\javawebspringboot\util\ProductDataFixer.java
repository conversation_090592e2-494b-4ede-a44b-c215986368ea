package cn.gzsf.javawebspringboot.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 产品数据修复工具
 * 用于修复产品表中缺失的图片URL
 */
@Component
@Order(2) // 在DatabaseUpdater之后执行
public class ProductDataFixer implements CommandLineRunner {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public void run(String... args) throws Exception {
        try {
            System.out.println("开始检查和修复产品数据...");
            
            // 检查产品表结构
            checkTableStructure();
            
            // 检查产品数据
            checkProductData();
            
            // 修复缺失的图片URL
            fixMissingImageUrls();
            
            System.out.println("产品数据检查和修复完成");
            
        } catch (Exception e) {
            System.err.println("产品数据修复失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void checkTableStructure() {
        try {
            String sql = "DESCRIBE products";
            List<Map<String, Object>> columns = jdbcTemplate.queryForList(sql);
            
            System.out.println("产品表结构:");
            for (Map<String, Object> column : columns) {
                System.out.println("  " + column.get("Field") + " - " + column.get("Type"));
            }
        } catch (Exception e) {
            System.err.println("检查表结构失败: " + e.getMessage());
        }
    }
    
    private void checkProductData() {
        try {
            String sql = "SELECT id, name, image_url, stock FROM products LIMIT 5";
            List<Map<String, Object>> products = jdbcTemplate.queryForList(sql);

            System.out.println("前5个产品数据:");
            for (Map<String, Object> product : products) {
                System.out.println("  ID: " + product.get("id") +
                                 ", 名称: " + product.get("name") +
                                 ", 图片URL: " + product.get("image_url") +
                                 ", 库存: " + product.get("stock"));
            }

            // 统计图片URL情况
            String countSql = "SELECT " +
                    "COUNT(*) as total, " +
                    "SUM(CASE WHEN image_url IS NULL OR image_url = '' THEN 1 ELSE 0 END) as without_image, " +
                    "SUM(CASE WHEN image_url IS NOT NULL AND image_url != '' THEN 1 ELSE 0 END) as with_image " +
                    "FROM products";
            Map<String, Object> stats = jdbcTemplate.queryForMap(countSql);
            System.out.println("图片URL统计: 总计=" + stats.get("total") +
                             ", 有图片=" + stats.get("with_image") +
                             ", 无图片=" + stats.get("without_image"));

        } catch (Exception e) {
            System.err.println("检查产品数据失败: " + e.getMessage());
        }
    }
    
    private void fixMissingImageUrls() {
        try {
            // 强制更新所有产品的图片URL
            String updateSql = "UPDATE products SET image_url = '/images/074dc74f-ab1e-4241-ad36-40a3dae3234a.jpg'";
            int updatedRows = jdbcTemplate.update(updateSql);

            System.out.println("强制更新了 " + updatedRows + " 个产品的图片URL");

            // 验证更新结果
            String verifySql = "SELECT id, name, image_url FROM products LIMIT 3";
            List<Map<String, Object>> products = jdbcTemplate.queryForList(verifySql);
            System.out.println("验证更新结果:");
            for (Map<String, Object> product : products) {
                System.out.println("  产品ID: " + product.get("id") +
                                 ", 名称: " + product.get("name") +
                                 ", 图片URL: " + product.get("image_url"));
            }

        } catch (Exception e) {
            System.err.println("修复图片URL失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
