package cn.gzsf.javawebspringboot.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 文件上传控制器
 */
@RestController
@RequestMapping("/api/upload")
@CrossOrigin(origins = "*")
public class FileUploadController {

    @Value("${file.upload.dir:src/main/resources/static/images}")
    private String uploadDir;

    /**
     * 上传产品图片
     */
    @PostMapping("/product-image")
    public Map<String, Object> uploadProductImage(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "productId", required = false) Long productId) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 验证文件
            if (file.isEmpty()) {
                result.put("success", false);
                result.put("message", "文件不能为空");
                return result;
            }
            
            // 验证文件类型
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                result.put("success", false);
                result.put("message", "只能上传图片文件");
                return result;
            }
            
            // 验证文件大小（2MB）
            if (file.getSize() > 2 * 1024 * 1024) {
                result.put("success", false);
                result.put("message", "文件大小不能超过2MB");
                return result;
            }
            
            // 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            String extension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            String filename = UUID.randomUUID().toString() + extension;
            
            // 确保上传目录存在
            Path uploadPath = Paths.get(uploadDir);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }
            
            // 保存文件
            Path filePath = uploadPath.resolve(filename);
            Files.copy(file.getInputStream(), filePath);
            
            // 构建访问URL
            String imageUrl = "/images/" + filename;
            
            result.put("success", true);
            result.put("message", "上传成功");
            result.put("imageUrl", imageUrl);
            result.put("filename", filename);
            result.put("fileSize", file.getSize());
            
            // 如果提供了productId，可以在这里保存到product_images表
            if (productId != null) {
                // TODO: 保存到product_images表
                result.put("productId", productId);
            }
            
        } catch (IOException e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "文件上传失败：" + e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "系统错误：" + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 上传轮播图
     */
    @PostMapping("/carousel-image")
    public Map<String, Object> uploadCarouselImage(@RequestParam("file") MultipartFile file) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 验证文件
            if (file.isEmpty()) {
                result.put("success", false);
                result.put("message", "文件不能为空");
                return result;
            }
            
            // 验证文件类型
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                result.put("success", false);
                result.put("message", "只能上传图片文件");
                return result;
            }
            
            // 验证文件大小（5MB）
            if (file.getSize() > 5 * 1024 * 1024) {
                result.put("success", false);
                result.put("message", "文件大小不能超过5MB");
                return result;
            }
            
            // 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            String extension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            String filename = "carousel_" + UUID.randomUUID().toString() + extension;
            
            // 确保上传目录存在
            Path uploadPath = Paths.get(uploadDir);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }
            
            // 保存文件
            Path filePath = uploadPath.resolve(filename);
            Files.copy(file.getInputStream(), filePath);
            
            // 构建访问URL
            String imageUrl = "/images/" + filename;
            
            result.put("success", true);
            result.put("message", "上传成功");
            result.put("imageUrl", imageUrl);
            result.put("filename", filename);
            result.put("fileSize", file.getSize());
            
        } catch (IOException e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "文件上传失败：" + e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "系统错误：" + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 上传用户头像
     */
    @PostMapping("/avatar")
    public Map<String, Object> uploadAvatar(@RequestParam("file") MultipartFile file) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 验证文件
            if (file.isEmpty()) {
                result.put("success", false);
                result.put("message", "文件不能为空");
                return result;
            }
            
            // 验证文件类型
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                result.put("success", false);
                result.put("message", "只能上传图片文件");
                return result;
            }
            
            // 验证文件大小（2MB）
            if (file.getSize() > 2 * 1024 * 1024) {
                result.put("success", false);
                result.put("message", "文件大小不能超过2MB");
                return result;
            }
            
            // 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            String extension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            String filename = "avatar_" + UUID.randomUUID().toString() + extension;
            
            // 确保头像目录存在
            Path avatarPath = Paths.get(uploadDir, "avatars");
            if (!Files.exists(avatarPath)) {
                Files.createDirectories(avatarPath);
            }
            
            // 保存文件
            Path filePath = avatarPath.resolve(filename);
            Files.copy(file.getInputStream(), filePath);
            
            // 构建访问URL
            String imageUrl = "/images/avatars/" + filename;
            
            result.put("success", true);
            result.put("message", "头像上传成功");
            result.put("imageUrl", imageUrl);
            result.put("filename", filename);
            result.put("fileSize", file.getSize());
            
        } catch (IOException e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "头像上传失败：" + e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "系统错误：" + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 删除文件
     */
    @DeleteMapping("/file")
    public Map<String, Object> deleteFile(@RequestParam("filename") String filename) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Path filePath = Paths.get(uploadDir, filename);
            if (Files.exists(filePath)) {
                Files.delete(filePath);
                result.put("success", true);
                result.put("message", "文件删除成功");
            } else {
                result.put("success", false);
                result.put("message", "文件不存在");
            }
        } catch (IOException e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "文件删除失败：" + e.getMessage());
        }
        
        return result;
    }
}
