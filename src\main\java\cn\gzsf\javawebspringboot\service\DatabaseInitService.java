package cn.gzsf.javawebspringboot.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

/**
 * 数据库初始化服务
 * 在应用启动时自动创建新功能所需的数据表
 */
@Service
public class DatabaseInitService implements ApplicationRunner {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        try {
            System.out.println("开始初始化Admin新功能数据表...");
            
            // 创建系统日志表
            createSystemLogsTable();
            
            // 创建系统备份表
            createSystemBackupsTable();
            
            // 创建安全事件表
            createSecurityEventsTable();
            
            // 创建IP管理表
            createIpManagementTable();
            
            // 创建自动备份配置表
            createAutoBackupConfigTable();
            
            // 创建安全配置表
            createSecurityConfigTable();
            
            // 插入默认配置数据
            insertDefaultConfigs();
            
            System.out.println("Admin新功能数据表初始化完成！");
        } catch (Exception e) {
            System.err.println("数据表初始化失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建系统日志表
     */
    private void createSystemLogsTable() {
        String sql = "CREATE TABLE IF NOT EXISTS system_logs (" +
                "id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID'," +
                "level VARCHAR(10) NOT NULL COMMENT '日志级别：INFO, WARN, ERROR, DEBUG'," +
                "action VARCHAR(20) NOT NULL COMMENT '操作类型：LOGIN, CREATE, UPDATE, DELETE, SELECT'," +
                "user VARCHAR(50) NOT NULL COMMENT '操作用户'," +
                "description TEXT NOT NULL COMMENT '操作描述'," +
                "ip VARCHAR(45) NOT NULL COMMENT '操作IP地址'," +
                "user_agent TEXT COMMENT '用户代理'," +
                "time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间'," +
                "details JSON COMMENT '详细信息（JSON格式）'," +
                "INDEX idx_level (level)," +
                "INDEX idx_action (action)," +
                "INDEX idx_user (user)," +
                "INDEX idx_time (time)," +
                "INDEX idx_ip (ip)" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统操作日志表'";
        
        jdbcTemplate.execute(sql);
        System.out.println("✓ 系统日志表创建完成");
    }

    /**
     * 创建系统备份表
     */
    private void createSystemBackupsTable() {
        String sql = "CREATE TABLE IF NOT EXISTS system_backups (" +
                "id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '备份ID'," +
                "name VARCHAR(200) NOT NULL COMMENT '备份名称'," +
                "type VARCHAR(20) NOT NULL COMMENT '备份类型：full, data, structure'," +
                "file_path VARCHAR(500) NOT NULL COMMENT '备份文件路径'," +
                "file_size BIGINT DEFAULT 0 COMMENT '文件大小（字节）'," +
                "status VARCHAR(20) NOT NULL DEFAULT 'processing' COMMENT '备份状态：success, failed, processing'," +
                "create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'," +
                "description TEXT COMMENT '备份描述'," +
                "creator VARCHAR(50) NOT NULL COMMENT '创建者'," +
                "INDEX idx_type (type)," +
                "INDEX idx_status (status)," +
                "INDEX idx_create_time (create_time)," +
                "INDEX idx_creator (creator)" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统备份表'";
        
        jdbcTemplate.execute(sql);
        System.out.println("✓ 系统备份表创建完成");
    }

    /**
     * 创建安全事件表
     */
    private void createSecurityEventsTable() {
        String sql = "CREATE TABLE IF NOT EXISTS security_events (" +
                "id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '事件ID'," +
                "type VARCHAR(50) NOT NULL COMMENT '事件类型：LOGIN_FAIL, SQL_INJECTION, XSS_ATTACK, BRUTE_FORCE, SUSPICIOUS_ACCESS'," +
                "ip VARCHAR(45) NOT NULL COMMENT '来源IP'," +
                "description TEXT NOT NULL COMMENT '事件描述'," +
                "severity VARCHAR(10) NOT NULL COMMENT '严重程度：高, 中, 低'," +
                "status VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '处理状态：pending, handled'," +
                "time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发生时间'," +
                "user_agent TEXT COMMENT '用户代理'," +
                "details JSON COMMENT '详细信息（JSON格式）'," +
                "handler VARCHAR(50) COMMENT '处理人'," +
                "handle_time DATETIME COMMENT '处理时间'," +
                "INDEX idx_type (type)," +
                "INDEX idx_ip (ip)," +
                "INDEX idx_severity (severity)," +
                "INDEX idx_status (status)," +
                "INDEX idx_time (time)" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='安全事件表'";
        
        jdbcTemplate.execute(sql);
        System.out.println("✓ 安全事件表创建完成");
    }

    /**
     * 创建IP管理表
     */
    private void createIpManagementTable() {
        String sql = "CREATE TABLE IF NOT EXISTS ip_management (" +
                "id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'IP管理ID'," +
                "ip VARCHAR(45) NOT NULL COMMENT 'IP地址'," +
                "type VARCHAR(20) NOT NULL COMMENT '类型：blacklist, whitelist'," +
                "reason TEXT COMMENT '原因/描述'," +
                "create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'," +
                "expire_time DATETIME COMMENT '到期时间（可选）'," +
                "creator VARCHAR(50) NOT NULL COMMENT '创建者'," +
                "status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '状态：active, expired, disabled'," +
                "INDEX idx_ip (ip)," +
                "INDEX idx_type (type)," +
                "INDEX idx_status (status)," +
                "INDEX idx_create_time (create_time)," +
                "INDEX idx_expire_time (expire_time)" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='IP管理表'";
        
        jdbcTemplate.execute(sql);
        System.out.println("✓ IP管理表创建完成");
    }

    /**
     * 创建自动备份配置表
     */
    private void createAutoBackupConfigTable() {
        String sql = "CREATE TABLE IF NOT EXISTS auto_backup_config (" +
                "id INT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID'," +
                "enabled BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否启用自动备份'," +
                "frequency VARCHAR(20) NOT NULL DEFAULT 'daily' COMMENT '备份频率：daily, weekly, monthly'," +
                "backup_time TIME NOT NULL DEFAULT '02:00:00' COMMENT '备份时间'," +
                "retention_days INT NOT NULL DEFAULT 30 COMMENT '保留天数'," +
                "backup_type VARCHAR(20) NOT NULL DEFAULT 'full' COMMENT '备份类型'," +
                "last_backup_time DATETIME COMMENT '最后备份时间'," +
                "created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'," +
                "updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='自动备份配置表'";
        
        jdbcTemplate.execute(sql);
        System.out.println("✓ 自动备份配置表创建完成");
    }

    /**
     * 创建安全配置表
     */
    private void createSecurityConfigTable() {
        String sql = "CREATE TABLE IF NOT EXISTS security_config (" +
                "id INT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID'," +
                "config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键'," +
                "config_value TEXT COMMENT '配置值'," +
                "config_type VARCHAR(20) NOT NULL DEFAULT 'string' COMMENT '配置类型：string, number, boolean'," +
                "description TEXT COMMENT '配置描述'," +
                "created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'," +
                "updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'," +
                "INDEX idx_config_key (config_key)" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='安全配置表'";
        
        jdbcTemplate.execute(sql);
        System.out.println("✓ 安全配置表创建完成");
    }

    /**
     * 插入默认配置数据
     */
    private void insertDefaultConfigs() {
        try {
            // 插入默认的自动备份配置
            String checkBackupConfig = "SELECT COUNT(*) FROM auto_backup_config";
            Integer backupConfigCount = jdbcTemplate.queryForObject(checkBackupConfig, Integer.class);
            
            if (backupConfigCount == 0) {
                String insertBackupConfig = "INSERT INTO auto_backup_config (enabled, frequency, backup_time, retention_days, backup_type) " +
                        "VALUES (FALSE, 'daily', '02:00:00', 30, 'full')";
                jdbcTemplate.update(insertBackupConfig);
                System.out.println("✓ 默认自动备份配置插入完成");
            }

            // 插入默认的安全配置
            String[] securityConfigs = {
                    "INSERT IGNORE INTO security_config (config_key, config_value, config_type, description) VALUES ('password_min_length', '8', 'number', '密码最小长度')",
                    "INSERT IGNORE INTO security_config (config_key, config_value, config_type, description) VALUES ('password_require_special', 'false', 'boolean', '密码是否需要特殊字符')",
                    "INSERT IGNORE INTO security_config (config_key, config_value, config_type, description) VALUES ('login_max_attempts', '5', 'number', '登录最大尝试次数')",
                    "INSERT IGNORE INTO security_config (config_key, config_value, config_type, description) VALUES ('session_timeout', '30', 'number', '会话超时时间（分钟）')",
                    "INSERT IGNORE INTO security_config (config_key, config_value, config_type, description) VALUES ('enable_captcha', 'true', 'boolean', '是否启用验证码')",
                    "INSERT IGNORE INTO security_config (config_key, config_value, config_type, description) VALUES ('rate_limit_enabled', 'true', 'boolean', 'API限流是否启用')",
                    "INSERT IGNORE INTO security_config (config_key, config_value, config_type, description) VALUES ('requests_per_minute', '100', 'number', '每分钟请求数限制')",
                    "INSERT IGNORE INTO security_config (config_key, config_value, config_type, description) VALUES ('ip_whitelist_enabled', 'false', 'boolean', 'IP白名单是否启用')",
                    "INSERT IGNORE INTO security_config (config_key, config_value, config_type, description) VALUES ('cors_enabled', 'true', 'boolean', 'CORS跨域是否启用')",
                    "INSERT IGNORE INTO security_config (config_key, config_value, config_type, description) VALUES ('sql_injection_protection', 'true', 'boolean', 'SQL注入防护是否启用')"
            };

            for (String sql : securityConfigs) {
                jdbcTemplate.update(sql);
            }
            System.out.println("✓ 默认安全配置插入完成");

        } catch (Exception e) {
            System.err.println("插入默认配置失败: " + e.getMessage());
        }
    }
}
