package cn.gzsf.javawebspringboot.controller;

import cn.gzsf.javawebspringboot.entity.Product;
import cn.gzsf.javawebspringboot.service.ProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 调试控制器 - 用于检查数据和配置
 */
@RestController
@RequestMapping("/debug")
public class DebugController {

    @Autowired
    private ProductService productService;

    /**
     * 检查产品数据
     */
    @GetMapping("/products")
    public Map<String, Object> checkProducts() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<Product> products = productService.getAllProducts();
            result.put("success", true);
            result.put("total", products.size());
            result.put("products", products);
            
            // 统计图片URL情况
            long withImageUrl = products.stream().filter(p -> p.getImageUrl() != null && !p.getImageUrl().isEmpty()).count();
            long withoutImageUrl = products.size() - withImageUrl;
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("withImageUrl", withImageUrl);
            stats.put("withoutImageUrl", withoutImageUrl);
            result.put("imageStats", stats);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }
        
        return result;
    }

    /**
     * 检查前几个产品的详细信息
     */
    @GetMapping("/products/detail")
    public Map<String, Object> checkProductsDetail() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<Product> products = productService.getProductsPage(0, 5);
            result.put("success", true);
            result.put("products", products);
            
            // 为每个产品添加详细信息
            for (Product product : products) {
                System.out.println("产品ID: " + product.getId());
                System.out.println("产品名称: " + product.getName());
                System.out.println("图片URL: " + product.getImageUrl());
                System.out.println("库存: " + product.getStock());
                System.out.println("---");
            }
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }
        
        return result;
    }
}
