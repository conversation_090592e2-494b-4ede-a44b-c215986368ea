package cn.gzsf.javawebspringboot.service;

import cn.gzsf.javawebspringboot.entity.ProductFavorite;

import java.util.List;
import java.util.Map;

/**
 * 产品收藏服务接口
 */
public interface ProductFavoriteService {
    
    /**
     * 添加收藏
     */
    boolean addFavorite(String userPhone, Long productId);
    
    /**
     * 取消收藏
     */
    boolean removeFavorite(String userPhone, Long productId);
    
    /**
     * 检查是否已收藏
     */
    boolean isFavorited(String userPhone, Long productId);
    
    /**
     * 获取用户收藏列表
     */
    Map<String, Object> getUserFavorites(String userPhone, int page, int size);
    
    /**
     * 获取产品收藏总数
     */
    int getProductFavoriteCount(Long productId);
    
    /**
     * 获取热门收藏产品
     */
    List<ProductFavorite> getPopularFavorites(int limit);
    
    /**
     * 获取所有收藏记录（管理员用）
     */
    Map<String, Object> getAllFavorites(int page, int size);

    /**
     * 获取收藏统计（管理员用）
     */
    Map<String, Object> getFavoriteStats();

    /**
     * 根据ID删除收藏记录（管理员用）
     */
    boolean deleteFavoriteById(Long id);
}
