# SpringBoot项目启动脚本
Write-Host "🚀 启动SpringBoot项目..." -ForegroundColor Green
Write-Host "================================" -ForegroundColor Cyan

# 设置工作目录
$projectDir = Get-Location
Write-Host "📁 项目目录: $projectDir" -ForegroundColor White

# 检查Java版本
Write-Host "☕ 检查Java版本..." -ForegroundColor Yellow
try {
    $javaVersion = java -version 2>&1 | Select-String "version"
    Write-Host "✅ Java版本: $javaVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Java未安装或不在PATH中" -ForegroundColor Red
    exit 1
}

# 设置Maven路径
$mavenPath = ".\apache-maven-3.9.4\bin\mvn.cmd"
if (Test-Path $mavenPath) {
    Write-Host "📦 使用本地Maven: $mavenPath" -ForegroundColor Cyan
} else {
    Write-Host "❌ Maven未找到" -ForegroundColor Red
    exit 1
}

# 尝试编译项目
Write-Host "🔨 编译项目..." -ForegroundColor Yellow
try {
    & $mavenPath clean compile -q
    Write-Host "✅ 编译成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 编译失败，尝试直接运行..." -ForegroundColor Yellow
}

# 启动应用
Write-Host "🎯 启动SpringBoot应用..." -ForegroundColor Green
Write-Host "📍 端口: 8082" -ForegroundColor Cyan
Write-Host "🌐 访问地址: http://localhost:8082" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

try {
    & $mavenPath spring-boot:run
} catch {
    Write-Host "❌ Maven启动失败，尝试直接运行Java..." -ForegroundColor Yellow
    
    # 直接使用Java启动
    $classpath = "target\classes"
    $mainClass = "cn.gzsf.javawebspringboot.JavaWebSpringBootApplication"
    
    Write-Host "🔧 使用Java直接启动..." -ForegroundColor Yellow
    java -cp $classpath $mainClass
}
